#!/usr/bin/env python3
"""
🧹 AGGRESSIVE CLEANUP SCRIPT
Triệt để cleanup các file rác, duplicate, outdated
"""

import os
import shutil
import glob
from pathlib import Path

def aggressive_cleanup():
    """Aggressive cleanup"""
    print("🧹 AGGRESSIVE CLEANUP - TRIỆT ĐỂ DỌN DẸP")
    print("=" * 60)
    
    # Files to keep (ONLY current working versions)
    KEEP_FILES = {
        "mcpo_complete_proxy.py",  # Current MCPO
        "test_unified_wrapper.py", # Test script
        "cleanup_project.py",      # Cleanup script
        "cleanup_aggressive.py",   # This script
        "PROJECT_STRUCTURE.md",    # Documentation
        
        # Jina Crawler working files
        "mcp-integration/servers/jina_crawler/http_wrapper.py",
        "mcp-integration/servers/jina_crawler/Dockerfile",
        "mcp-integration/servers/jina_crawler/docker-compose.yml",
        
        # Open WebUI working config
        "mem0-owui/docker-compose-openwebui-mcpo.yml",
        "mem0-owui/mcp-integration/config/openwebui_mcpo_config.json",
    }
    
    # Aggressive removal patterns
    REMOVE_PATTERNS = [
        # All old docker-compose files except working ones
        "docker-compose*.yml",
        "*compose*.yml",
        
        # All old Dockerfiles except working ones  
        "Dockerfile.*",
        
        # All old Python scripts except working ones
        "*_server.py",
        "*_wrapper.py", 
        "*_proxy.py",
        "fix_*.py",
        "check_*.py",
        "test_*.py",
        
        # All old config files
        "*config*.json",
        
        # Cache and temp files
        "*.pyc",
        "__pycache__",
        "*.log",
        "*.tmp",
        "*.temp",
        "*.backup",
        "*.bak",
        "*~",
        
        # Old directories
        "screenshots",
        "memory_backup", 
        "docs",
        "scripts",
        "models",
        "servers",
        "data/openwebui_collections",
        "data/faiss_index",
        "data/memory_feedback",
        "data/catomanton",
        "data/documents",
        "data/memory_analytics",
    ]
    
    # Specific files to remove
    REMOVE_SPECIFIC = [
        # Empty files created by manual cleanup
        "mcpo_unified_wrapper.py",
        "mcpo_proper_config.json", 
        "mcpo_fixed_server.py",
        "mcpo_proper_server.py",
        "mcpo_simple_proxy.py",
        "Dockerfile.mcpo-unified",
        "Dockerfile.mcpo-fixed", 
        "Dockerfile.mcpo-proper",
        "mcpo_config_unified.json",
        
        # Old compose files
        "docker-compose.http-interface.yml",
        "docker-compose.jina-crawler-http.yml", 
        "docker-compose.proxy-8002.yml",
        "docker-compose-openwebui-mcpo.yml",
        "docker-compose.jina-ai-only.yml",
        "simple_jina_crawler_compose.yml",
        "docker-compose.real-browser.yml",
        "docker-compose.jina-http-8001.yml",
        "docker-compose.perplexica-mcp-proxy.yml",
        "mem0-owui-compose.yml",
        "docker-compose.perplexica-fixed.yml",
        "docker-compose.standalone-mcp.yml",
        "docker-compose.jina-mcp-proxy.yml",
        
        # Old Jina Crawler files
        "mcp-integration/servers/jina_crawler/docker-compose.mcpo.yml",
        "mcp-integration/servers/jina_crawler/docker-compose.optimized.yml",
        
        # Old mem0-owui files
        "mem0-owui/mcp-integration/docker-compose.mcpo.yml",
        "mem0-owui/docker-compose.yml",
        "mem0-owui/docker-compose.example.yml",
        
        # Unified MCPO directory (old)
        "unified-mcpo",
    ]
    
    removed_count = 0
    kept_count = 0
    
    # Remove specific files
    print("\n📁 REMOVING SPECIFIC FILES:")
    for file_path in REMOVE_SPECIFIC:
        if os.path.exists(file_path):
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"   ❌ Removed dir: {file_path}")
                else:
                    os.remove(file_path)
                    print(f"   ❌ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ Failed to remove {file_path}: {e}")
        else:
            print(f"   ✅ Already gone: {file_path}")
    
    # Remove files matching patterns
    print("\n🔍 REMOVING FILES MATCHING PATTERNS:")
    for pattern in REMOVE_PATTERNS:
        matches = glob.glob(pattern, recursive=True)
        for file_path in matches:
            # Skip if it's in keep list
            if any(keep_file in file_path for keep_file in KEEP_FILES):
                print(f"   ✅ Keeping: {file_path}")
                kept_count += 1
                continue
                
            # Skip venv and node_modules
            if any(skip in file_path for skip in ['venv/', 'node_modules/', '.git/', 'docling_env/', 'backend/app/rag/docling_env/']):
                continue
                
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"   ❌ Removed dir: {file_path}")
                else:
                    os.remove(file_path)
                    print(f"   ❌ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ Failed to remove {file_path}: {e}")
    
    # Clean up __pycache__ directories
    print("\n🗂️ REMOVING __pycache__ DIRECTORIES:")
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                dir_path = os.path.join(root, dir_name)
                # Skip venv directories
                if 'venv' in dir_path or 'node_modules' in dir_path:
                    continue
                try:
                    shutil.rmtree(dir_path)
                    print(f"   ❌ Removed: {dir_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ⚠️ Failed to remove {dir_path}: {e}")
    
    # Clean up empty directories
    print("\n📂 REMOVING EMPTY DIRECTORIES:")
    empty_dirs = []
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            # Skip venv and node_modules
            if any(skip in dir_path for skip in ['venv', 'node_modules', '.git']):
                continue
            try:
                if not os.listdir(dir_path):  # Empty directory
                    empty_dirs.append(dir_path)
            except:
                pass
    
    for dir_path in empty_dirs:
        try:
            os.rmdir(dir_path)
            print(f"   ❌ Removed empty dir: {dir_path}")
            removed_count += 1
        except Exception as e:
            print(f"   ⚠️ Failed to remove {dir_path}: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 AGGRESSIVE CLEANUP SUMMARY:")
    print(f"   ❌ Files/dirs removed: {removed_count}")
    print(f"   ✅ Files kept: {kept_count}")
    
    return removed_count, kept_count

def show_final_structure():
    """Show final clean structure"""
    print("\n✅ FINAL CLEAN STRUCTURE:")
    print("   🔧 mcpo_complete_proxy.py - MCPO 12 tools")
    print("   🔧 mcp-integration/servers/jina_crawler/ - Jina Crawler")
    print("   🔧 mem0-owui/docker-compose-openwebui-mcpo.yml - Open WebUI")
    print("   🔧 mem0-owui/mcp-integration/config/openwebui_mcpo_config.json - Config")
    print("   📝 PROJECT_STRUCTURE.md - Documentation")
    print("   🧹 cleanup_*.py - Cleanup scripts")
    print("   🧪 test_unified_wrapper.py - Test script")

def check_disk_space():
    """Check disk space after cleanup"""
    print("\n💾 DISK SPACE AFTER CLEANUP:")
    os.system("df -h . | tail -1")
    os.system("du -sh . 2>/dev/null | head -1")

if __name__ == "__main__":
    removed, kept = aggressive_cleanup()
    show_final_structure()
    check_disk_space()
    
    print(f"\n🎉 AGGRESSIVE CLEANUP COMPLETE!")
    print(f"   Removed {removed} files/directories")
    print(f"   Kept {kept} essential files")
    print("   Project is now ultra-clean and minimal!")
