#!/usr/bin/env python3
"""
Restore MCPO with Jina-Crawler Update
"""

import json
import subprocess
import time
import sys

def run_command(cmd, check=True):
    """Run shell command"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False
    print(result.stdout)
    return True

def main():
    print("🔄 Restoring MCPO with Jina-Crawler Update...")
    
    # 1. Stop existing container
    print("\n1. Stopping existing container...")
    run_command("docker stop mcpo-container", check=False)
    run_command("docker rm mcpo-container", check=False)
    
    # 2. Create working config (without problematic servers)
    print("\n2. Creating working configuration...")
    working_config = {
        "mcpServers": {
            "filesystem": {
                "command": "python",
                "args": ["/app/servers/filesystem/server.py"]
            },
            "wikipedia": {
                "command": "python",
                "args": ["/app/servers/wikipedia/server.py"]
            },
            "time_utilities": {
                "command": "python",
                "args": ["/app/servers/time_utilities/server.py"]
            },
            "weather_service": {
                "command": "python",
                "args": ["/app/servers/weather_service/server.py"]
            },
            "sqlite": {
                "command": "python",
                "args": ["/app/servers/sqlite/server.py"]
            },
            "github": {
                "command": "python",
                "args": ["/app/servers/github/server.py"]
            },
            "brave_search": {
                "command": "python",
                "args": ["/app/servers/brave_search/server.py"]
            },
            "gemini_search_engine": {
                "command": "python",
                "args": ["/app/servers/gemini_search_engine/server_with_grounding.py"]
            },
            "mem0_system": {
                "command": "python",
                "args": ["/app/servers/mem0_system/server.py"]
            },
            "jina-crawler": {
                "command": "fastmcp",
                "args": [
                    "run",
                    "/home/<USER>/jina-crawler/mcp_server_simple.py"
                ],
                "env": {
                    "GEMINI_API_KEY": "your_gemini_api_key_here"
                }
            }
        }
    }
    
    with open("config/mcpo_config_working.json", "w") as f:
        json.dump(working_config, f, indent=2)
    
    # 3. Start container with working config
    print("\n3. Starting MCPO container...")
    start_cmd = '''docker run -d \
        --name mcpo-container \
        --network acca-network \
        -p 5000:5000 \
        -v $(pwd)/config:/app/config \
        -v $(pwd)/servers:/app/servers \
        -v /home/<USER>/jina-crawler:/home/<USER>/jina-crawler \
        mem0-owui-mcpo-container \
        mcpo --host 0.0.0.0 --port 5000 --config config/mcpo_config_working.json'''
    
    if not run_command(start_cmd):
        print("❌ Failed to start container")
        return False
    
    # 4. Wait and test
    print("\n4. Waiting for container to start...")
    time.sleep(15)
    
    # Check if container is running
    if not run_command("docker ps | grep mcpo", check=False):
        print("❌ Container is not running. Checking logs...")
        run_command("docker logs mcpo-container --tail 10", check=False)
        
        # Try with even simpler config
        print("\n5. Trying with minimal config...")
        return try_minimal_config()
    
    print("\n5. Testing MCPO endpoints...")
    if run_command("curl -s http://localhost:5000/openapi.json | jq '.info.title'", check=False):
        print("✅ MCPO is running!")
        
        # Test jina-crawler specifically
        if run_command("curl -s http://localhost:5000/jina-crawler/openapi.json", check=False):
            print("✅ Jina-crawler endpoint working!")
        else:
            print("⚠️ Jina-crawler endpoint not accessible")
        
        return True
    else:
        print("❌ MCPO not responding")
        return False

def try_minimal_config():
    """Try with minimal config"""
    print("🔧 Trying minimal configuration...")
    
    # Stop current container
    run_command("docker stop mcpo-container", check=False)
    run_command("docker rm mcpo-container", check=False)
    
    # Create minimal config
    minimal_config = {
        "mcpServers": {
            "filesystem": {
                "command": "python",
                "args": ["/app/servers/filesystem/server.py"]
            },
            "wikipedia": {
                "command": "python",
                "args": ["/app/servers/wikipedia/server.py"]
            }
        }
    }
    
    with open("config/mcpo_config_minimal_working.json", "w") as f:
        json.dump(minimal_config, f, indent=2)
    
    # Start with minimal config
    start_cmd = '''docker run -d \
        --name mcpo-container \
        --network acca-network \
        -p 5000:5000 \
        -v $(pwd)/config:/app/config \
        -v $(pwd)/servers:/app/servers \
        mem0-owui-mcpo-container \
        mcpo --host 0.0.0.0 --port 5000 --config config/mcpo_config_minimal_working.json'''
    
    if not run_command(start_cmd):
        print("❌ Failed to start minimal container")
        return False
    
    time.sleep(10)
    
    if run_command("docker ps | grep mcpo", check=False):
        print("✅ Minimal MCPO is running!")
        
        if run_command("curl -s http://localhost:5000/openapi.json | jq '.info.title'", check=False):
            print("✅ Minimal MCPO responding!")
            return True
    
    return False

if __name__ == "__main__":
    if main():
        print("\n🎉 MCPO restored successfully!")
        print("📋 Available at: http://localhost:5000")
        print("📄 API docs: http://localhost:5000/openapi.json")
        sys.exit(0)
    else:
        print("\n❌ Failed to restore MCPO")
        sys.exit(1)