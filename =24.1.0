Collecting PyPDF2
  Downloading pypdf2-3.0.1-py3-none-any.whl.metadata (6.8 kB)
Collecting python-docx
  Downloading python_docx-1.2.0-py3-none-any.whl.metadata (2.0 kB)
Collecting Pillow
  Downloading pillow-11.3.0-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl.metadata (9.0 kB)
Collecting aiofiles
  Downloading aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: lxml>=3.1.0 in /usr/local/lib/python3.12/site-packages (from python-docx) (6.0.0)
Requirement already satisfied: typing_extensions>=4.9.0 in /usr/local/lib/python3.12/site-packages (from python-docx) (4.14.1)
Downloading pypdf2-3.0.1-py3-none-any.whl (232 kB)
Downloading python_docx-1.2.0-py3-none-any.whl (252 kB)
Downloading pillow-11.3.0-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl (6.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.0/6.0 MB 89.5 MB/s eta 0:00:00
Downloading aiofiles-24.1.0-py3-none-any.whl (15 kB)
Installing collected packages: python-docx, PyPDF2, Pillow, aiofiles
Successfully installed Pillow-11.3.0 PyPDF2-3.0.1 aiofiles-24.1.0 python-docx-1.2.0
