#!/usr/bin/env python3
"""
🧪 TEST ORACLE ADVANCED MEMORY PIPELINE VALVES
Test the new LLM model override valves
"""

import json
import requests
import time

def test_oracle_pipeline_valves():
    """Test Oracle Advanced Memory Pipeline valves"""
    print("🧪 TESTING ORACLE ADVANCED MEMORY PIPELINE VALVES")
    print("=" * 60)
    
    # Open WebUI API endpoint
    base_url = "http://localhost:3000"
    
    # Test data
    test_message = {
        "model": "default-model",
        "messages": [
            {"role": "user", "content": "What is artificial intelligence?"}
        ],
        "temperature": 0.5
    }
    
    print("📋 CURRENT VALVES CONFIGURATION:")
    valves_file = "mem0-owui/webui-data/pipelines/oracle-advanced-memory/valves.json"
    
    try:
        with open(valves_file, 'r') as f:
            valves = json.load(f)
        
        print("✅ LLM Model Override Valves:")
        print(f"   override_llm_model: {valves.get('override_llm_model', 'NOT SET')}")
        print(f"   target_llm_model: {valves.get('target_llm_model', 'NOT SET')}")
        print(f"   target_llm_provider: {valves.get('target_llm_provider', 'NOT SET')}")
        print(f"   llm_temperature: {valves.get('llm_temperature', 'NOT SET')}")
        print(f"   llm_max_tokens: {valves.get('llm_max_tokens', 'NOT SET')}")
        print(f"   llm_top_p: {valves.get('llm_top_p', 'NOT SET')}")
        
        print("\n📊 Memory System Valves:")
        print(f"   enable_oracle_memory: {valves.get('enable_oracle_memory', 'NOT SET')}")
        print(f"   enable_mem0_coordination: {valves.get('enable_mem0_coordination', 'NOT SET')}")
        print(f"   max_oracle_memories: {valves.get('max_oracle_memories', 'NOT SET')}")
        print(f"   max_mem0_memories: {valves.get('max_mem0_memories', 'NOT SET')}")
        print(f"   memory_relevance_threshold: {valves.get('memory_relevance_threshold', 'NOT SET')}")
        
        print("\n🔧 Oracle Configuration Valves:")
        print(f"   oracle_user: {valves.get('oracle_user', 'NOT SET')}")
        print(f"   oracle_dsn: {'SET' if valves.get('oracle_dsn') else 'NOT SET'}")
        print(f"   oracle_wallet_location: {'SET' if valves.get('oracle_wallet_location') else 'NOT SET'}")
        
        print("\n🎯 Mem0 Integration Valves:")
        print(f"   qdrant_host: {valves.get('qdrant_host', 'NOT SET')}")
        print(f"   qdrant_port: {valves.get('qdrant_port', 'NOT SET')}")
        print(f"   mem0_collection: {valves.get('mem0_collection', 'NOT SET')}")
        print(f"   embedding_model: {valves.get('embedding_model', 'NOT SET')}")
        print(f"   embedding_provider: {valves.get('embedding_provider', 'NOT SET')}")
        
    except Exception as e:
        print(f"❌ Error reading valves: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎯 VALVE COMPLETENESS CHECK:")
    
    # Check for missing valves
    required_valves = [
        'override_llm_model', 'target_llm_model', 'target_llm_provider',
        'llm_temperature', 'llm_max_tokens', 'llm_top_p',
        'enable_oracle_memory', 'enable_mem0_coordination',
        'oracle_user', 'oracle_password', 'oracle_dsn',
        'qdrant_host', 'qdrant_port', 'mem0_collection'
    ]
    
    missing_valves = []
    for valve in required_valves:
        if valve not in valves:
            missing_valves.append(valve)
    
    if missing_valves:
        print(f"❌ Missing valves: {missing_valves}")
        return False
    else:
        print("✅ All required valves are present!")
    
    # Check valve types and values
    print("\n🔍 VALVE VALIDATION:")
    
    # Boolean valves
    bool_valves = ['override_llm_model', 'enable_oracle_memory', 'enable_mem0_coordination']
    for valve in bool_valves:
        if isinstance(valves.get(valve), bool):
            print(f"   ✅ {valve}: {valves[valve]} (bool)")
        else:
            print(f"   ❌ {valve}: {valves.get(valve)} (should be bool)")
    
    # Numeric valves
    if isinstance(valves.get('llm_temperature'), (int, float)) and 0 <= valves['llm_temperature'] <= 1:
        print(f"   ✅ llm_temperature: {valves['llm_temperature']} (valid range)")
    else:
        print(f"   ❌ llm_temperature: {valves.get('llm_temperature')} (should be 0.0-1.0)")
    
    if isinstance(valves.get('llm_max_tokens'), int) and valves['llm_max_tokens'] > 0:
        print(f"   ✅ llm_max_tokens: {valves['llm_max_tokens']} (positive int)")
    else:
        print(f"   ❌ llm_max_tokens: {valves.get('llm_max_tokens')} (should be positive int)")
    
    # String valves
    string_valves = ['target_llm_model', 'target_llm_provider', 'oracle_user']
    for valve in string_valves:
        if isinstance(valves.get(valve), str) and valves[valve]:
            print(f"   ✅ {valve}: '{valves[valve]}' (non-empty string)")
        else:
            print(f"   ❌ {valve}: '{valves.get(valve)}' (should be non-empty string)")
    
    print("\n" + "=" * 60)
    print("📝 VALVE RECOMMENDATIONS:")
    
    # Recommendations
    if not valves.get('override_llm_model'):
        print("💡 Set 'override_llm_model' to true to test LLM model override functionality")
    
    if valves.get('target_llm_model') == 'gemini-2.5-flash':
        print("💡 Consider testing with different models: claude-3-5-sonnet, gpt-4o, etc.")
    
    if valves.get('llm_temperature') == 0.7:
        print("💡 Experiment with different temperatures: 0.1 (focused), 0.9 (creative)")
    
    if valves.get('max_oracle_memories') == 3 and valves.get('max_mem0_memories') == 3:
        print("💡 Consider adjusting memory limits based on your use case")
    
    print("\n🎉 VALVE ANALYSIS COMPLETE!")
    print("✅ Oracle Advanced Memory Pipeline has comprehensive valve configuration")
    print("🔧 All LLM model override valves are properly implemented")
    print("💾 Memory system valves are configured and ready")
    
    return True

def show_valve_usage_examples():
    """Show examples of how to use the valves"""
    print("\n" + "=" * 60)
    print("📚 VALVE USAGE EXAMPLES:")
    
    print("\n🔄 LLM Model Override:")
    print("   1. Set 'override_llm_model': true")
    print("   2. Set 'target_llm_model': 'claude-3-5-sonnet'")
    print("   3. Set 'target_llm_provider': 'openai-compatible'")
    print("   4. Adjust 'llm_temperature': 0.3 for focused responses")
    
    print("\n💾 Memory Configuration:")
    print("   1. 'max_oracle_memories': 5 (for more context)")
    print("   2. 'max_mem0_memories': 2 (for efficiency)")
    print("   3. 'memory_relevance_threshold': 0.4 (higher = more selective)")
    
    print("\n🎯 Performance Tuning:")
    print("   1. 'memory_search_timeout': 3 (seconds)")
    print("   2. 'enable_debug_logging': false (for production)")
    print("   3. 'max_total_memories_injected': 8 (total limit)")

if __name__ == "__main__":
    success = test_oracle_pipeline_valves()
    show_valve_usage_examples()
    
    if success:
        print(f"\n🎉 All valves are properly configured!")
    else:
        print(f"\n❌ Some valves need attention!")
