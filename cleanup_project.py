#!/usr/bin/env python3
"""
🧹 PROJECT CLEANUP SCRIPT
Clean up duplicate, outdated, and unnecessary files
Keep only the working/current versions
"""

import os
import shutil
import glob
from pathlib import Path

def cleanup_project():
    """Clean up project files"""
    print("🧹 STARTING PROJECT CLEANUP")
    print("=" * 60)
    
    # Files to keep (current working versions)
    KEEP_FILES = {
        # Current working MCPO
        "mcpo_complete_proxy.py",  # Current working MCPO with 12 tools
        
        # Current working Jina Crawler
        "mcp-integration/servers/jina_crawler/http_wrapper.py",
        "mcp-integration/servers/jina_crawler/Dockerfile",
        
        # Current working configs
        "mem0-owui/mcp-integration/config/openwebui_mcpo_config.json",
        "mem0-owui/docker-compose-openwebui-mcpo.yml",
        
        # Test scripts
        "test_unified_wrapper.py",
    }
    
    # Directories to clean
    CLEANUP_PATTERNS = [
        # Old MCPO files
        "mcpo_*.py",
        "fix_mcpo_*.py", 
        "check_mcpo_*.py",
        
        # Old Dockerfiles
        "Dockerfile.mcpo-*",
        "Dockerfile.jina-*",
        "Dockerfile.*-mcpo",
        "Dockerfile.proxy-*",
        "Dockerfile.perplexica-*",
        "Dockerfile.real-*",
        "Dockerfile.http-*",
        "Dockerfile.standalone-*",
        
        # Old config files
        "mcpo_config*.json",
        "mcpo_proper_config.json",
        
        # Duplicate Jina Crawler Dockerfiles
        "mcp-integration/servers/jina_crawler/Dockerfile.*",
        "mem0-owui/mcp-integration/servers/jina_crawler/Dockerfile.*",
        
        # Old mem0-owui Dockerfiles
        "mem0-owui/Dockerfile.*",
        
        # Backup files
        "*.backup",
        "*~",
        "*.bak",
        
        # Log files
        "*.log",
        "logs/*.log",
    ]
    
    # Files to definitely remove (known old/broken)
    REMOVE_FILES = [
        # Old MCPO implementations
        "mcpo_real_browser_http_server.py",
        "mcpo_proper_server.py", 
        "mcpo_fixed_server.py",
        "mcpo_simple_proxy.py",  # Replaced by mcpo_complete_proxy.py
        
        # Old custom servers
        "mcp-integration/custom_mcpo_server.py",
        
        # Old test files
        "test_mcpo_integration.py",
        "test_jina_crawler.py",
        
        # Old wrapper files
        "mcp-integration/servers/jina_crawler/mcpo_wrapper.py",
        "mcp-integration/servers/jina_crawler/start_mcpo.sh",
    ]
    
    removed_count = 0
    kept_count = 0
    
    # Remove specific files
    print("\n📁 REMOVING SPECIFIC OLD FILES:")
    for file_path in REMOVE_FILES:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ❌ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ Failed to remove {file_path}: {e}")
        else:
            print(f"   ✅ Already gone: {file_path}")
    
    # Remove files matching patterns
    print("\n🔍 REMOVING FILES MATCHING PATTERNS:")
    for pattern in CLEANUP_PATTERNS:
        matches = glob.glob(pattern, recursive=True)
        for file_path in matches:
            # Skip if it's in keep list
            if any(keep_file in file_path for keep_file in KEEP_FILES):
                print(f"   ✅ Keeping: {file_path}")
                kept_count += 1
                continue
                
            # Skip if it's a directory
            if os.path.isdir(file_path):
                continue
                
            try:
                os.remove(file_path)
                print(f"   ❌ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ Failed to remove {file_path}: {e}")
    
    # Clean up empty directories
    print("\n📂 REMOVING EMPTY DIRECTORIES:")
    empty_dirs = []
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            try:
                if not os.listdir(dir_path):  # Empty directory
                    empty_dirs.append(dir_path)
            except:
                pass
    
    for dir_path in empty_dirs:
        try:
            os.rmdir(dir_path)
            print(f"   ❌ Removed empty dir: {dir_path}")
            removed_count += 1
        except Exception as e:
            print(f"   ⚠️ Failed to remove {dir_path}: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CLEANUP SUMMARY:")
    print(f"   ❌ Files removed: {removed_count}")
    print(f"   ✅ Files kept: {kept_count}")
    
    # Show current working files
    print("\n✅ CURRENT WORKING FILES:")
    print("   🔧 MCPO 8000: mcpo_complete_proxy.py (12 tools)")
    print("   🔧 Jina Crawler 8002: mcp-integration/servers/jina_crawler/")
    print("   🔧 Open WebUI: mem0-owui/docker-compose-openwebui-mcpo.yml")
    print("   🔧 Config: mem0-owui/mcp-integration/config/openwebui_mcpo_config.json")
    
    print("\n🎯 PROJECT STRUCTURE CLEANED UP!")
    return removed_count, kept_count

def show_current_containers():
    """Show current running containers"""
    print("\n🐳 CURRENT RUNNING CONTAINERS:")
    os.system("docker ps --format 'table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}' | grep -E '(mcpo|jina|open-webui)'")

if __name__ == "__main__":
    removed, kept = cleanup_project()
    show_current_containers()
    
    print(f"\n🎉 CLEANUP COMPLETE!")
    print(f"   Removed {removed} old/duplicate files")
    print(f"   Kept {kept} important files")
    print("   Project is now cleaner and more organized!")
