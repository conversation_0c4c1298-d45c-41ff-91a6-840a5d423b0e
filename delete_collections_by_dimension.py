#!/usr/bin/env python3
"""
Delete Qdrant collections by specific dimensions
"""

import sys
from qdrant_client import QdrantClient

def delete_collections_by_dimension(target_dimension):
    """Delete all Qdrant collections with the specified dimension"""
    
    try:
        client = QdrantClient(host='localhost', port=6333)
        
        # Get all collections
        collections = client.get_collections()
        print(f"🗂️  Available collections:")
        
        deleted_collections = []
        
        for collection in collections.collections:
            collection_name = collection.name
            print(f"\n📁 Checking collection: {collection_name}")
            
            try:
                # Get collection info
                info = client.get_collection(collection_name)
                vector_size = info.config.params.vectors.size
                
                print(f"   📏 Vector dimensions: {vector_size}")
                
                # Check if dimension matches target
                if vector_size == target_dimension:
                    print(f"   🗑️  Deleting collection with {target_dimension} dimensions...")
                    
                    # Delete the collection
                    client.delete_collection(collection_name)
                    deleted_collections.append(collection_name)
                    print(f"   ✅ Successfully deleted: {collection_name}")
                else:
                    print(f"   ℹ️  Skipping collection (not {target_dimension} dimensions)")
                    
            except Exception as e:
                print(f"   ❌ Error processing collection {collection_name}: {e}")
                
        print(f"\n🎉 Deletion process completed!")
        print(f"🗑️  Deleted {len(deleted_collections)} collections with {target_dimension} dimensions:")
        for collection in deleted_collections:
            print(f"   - {collection}")
                
    except Exception as e:
        print(f"❌ Error connecting to Qdrant: {e}")
        print("💡 Make sure Qdrant is running on localhost:6333")

def show_help():
    print("Usage: python3 delete_collections_by_dimension.py <dimension>")
    print("Example: python3 delete_collections_by_dimension.py 3072")
    print("This script will delete all Qdrant collections with the specified dimension.")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        show_help()
        sys.exit(1)
        
    try:
        dimension = int(sys.argv[1])
        delete_collections_by_dimension(dimension)
    except ValueError:
        print("Error: Dimension must be a number")
        show_help()
        sys.exit(1)