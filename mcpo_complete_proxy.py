#!/usr/bin/env python3
"""
🔧 MCPO COMPLETE PROXY
Complete MCPO proxy with all 12 tools (excluding jina_crawler)
Each tool has its own namespace following MCPO pattern
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import json

app = FastAPI(
    title="MCPO Complete Proxy",
    description="Complete MCPO proxy with all 12 tools - each tool has its own namespace",
    version="1.0.0",
    docs_url="/docs",
    openapi_url="/openapi.json"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# All 12 MCPO tools (excluding jina_crawler which is on port 8002)
TOOLS_CONFIG = {
    "weather_service": {
        "description": "Get weather information for any location worldwide",
        "mock_response": {
            "location": "Ho Chi Minh City",
            "temperature": "28°C",
            "condition": "Partly cloudy",
            "humidity": "75%",
            "wind_speed": "15 km/h"
        }
    },
    "time_utilities": {
        "description": "Get current time and timezone information",
        "mock_response": {
            "current_time": "2025-01-16 15:30:00",
            "timezone": "Asia/Ho_Chi_Minh",
            "timestamp": 1737024600,
            "utc_time": "2025-01-16 08:30:00"
        }
    },
    "brave_search": {
        "description": "Search the web using Brave Search API",
        "mock_response": {
            "query": "artificial intelligence",
            "results": [
                {"title": "AI Overview", "url": "https://example.com/ai", "snippet": "AI is transforming technology..."},
                {"title": "Machine Learning", "url": "https://example.com/ml", "snippet": "ML is a subset of AI..."},
                {"title": "Deep Learning", "url": "https://example.com/dl", "snippet": "Deep learning uses neural networks..."}
            ],
            "total_results": 3
        }
    },
    "wikipedia": {
        "description": "Search Wikipedia articles and get summaries",
        "mock_response": {
            "query": "Vietnam",
            "results": [
                {"title": "Vietnam", "url": "https://en.wikipedia.org/wiki/Vietnam", "summary": "Vietnam is a country in Southeast Asia..."},
                {"title": "History of Vietnam", "url": "https://en.wikipedia.org/wiki/History_of_Vietnam", "summary": "Vietnam has a rich history..."}
            ],
            "total_results": 2
        }
    },
    "document_processing": {
        "description": "Process and analyze documents with AI",
        "mock_response": {
            "processed": True,
            "content_length": 1500,
            "summary": "Document processed successfully",
            "extracted_entities": ["AI", "Machine Learning", "Technology"],
            "language": "en"
        }
    },
    "vietnamese_language": {
        "description": "Vietnamese translation and language processing services",
        "mock_response": {
            "translated": True,
            "source_language": "en",
            "target_language": "vi",
            "original_text": "Hello world",
            "translated_text": "Xin chào thế giới"
        }
    },
    "web_automation": {
        "description": "Automate web browser actions and interactions",
        "mock_response": {
            "action_completed": True,
            "url": "https://example.com",
            "action": "navigate",
            "screenshot_taken": True,
            "page_title": "Example Domain"
        }
    },
    "filesystem": {
        "description": "File system operations - read, write, list files",
        "mock_response": {
            "operation": "read",
            "file_path": "/app/data/sample.txt",
            "file_size": 1024,
            "content_preview": "Sample file content...",
            "success": True
        }
    },
    "sqlite": {
        "description": "SQLite database operations and queries",
        "mock_response": {
            "query_executed": True,
            "query": "SELECT * FROM users LIMIT 5",
            "rows_affected": 5,
            "results": [
                {"id": 1, "name": "John Doe", "email": "<EMAIL>"},
                {"id": 2, "name": "Jane Smith", "email": "<EMAIL>"}
            ]
        }
    },
    "github": {
        "description": "GitHub repository and code search",
        "mock_response": {
            "search_type": "repositories",
            "query": "machine learning",
            "results": [
                {"name": "tensorflow/tensorflow", "stars": 185000, "language": "C++"},
                {"name": "scikit-learn/scikit-learn", "stars": 59000, "language": "Python"}
            ],
            "total_count": 2
        }
    },
    "gemini_search_engine": {
        "description": "AI-powered search using Google Gemini",
        "mock_response": {
            "query": "latest AI developments",
            "ai_generated_summary": "Recent AI developments include advances in large language models, computer vision, and robotics...",
            "sources": [
                {"title": "AI News", "url": "https://example.com/ai-news"},
                {"title": "Tech Updates", "url": "https://example.com/tech"}
            ],
            "confidence": 0.95
        }
    },
    "mem0_system": {
        "description": "Memory storage and retrieval system",
        "mock_response": {
            "stored": True,
            "memory_id": "mem_12345",
            "content": "Information stored in memory system",
            "tags": ["important", "ai", "technology"],
            "timestamp": "2025-01-16T15:30:00Z"
        }
    }
}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "server": "mcpo-complete-proxy",
        "tools_count": len(TOOLS_CONFIG),
        "tools": list(TOOLS_CONFIG.keys()),
        "pattern": "Each tool has its own namespace: /weather_service, /time_utilities, etc.",
        "excluded": "jina_crawler (available on port 8002)"
    }

@app.get("/")
async def root():
    tools_info = {}
    for tool_name, config in TOOLS_CONFIG.items():
        tools_info[tool_name] = f"/{tool_name} - {config['description']}"
    
    return {
        "message": "MCPO Complete Proxy - All 12 Tools",
        "description": "Complete MCPO implementation with namespace separation",
        "tools": tools_info,
        "total_tools": len(TOOLS_CONFIG),
        "openapi": "/openapi.json",
        "docs": "/docs"
    }

# Generate tool endpoints dynamically
def create_tool_endpoint(tool_name: str, config: dict):
    async def tool_endpoint(request: dict):
        # Get mock response and customize based on request
        response = config["mock_response"].copy()
        
        # Update response based on request parameters
        for key, value in request.items():
            if key in response:
                response[key] = value
            elif key == "query" and "query" in response:
                response["query"] = value
            elif key == "location" and "location" in response:
                response["location"] = value
            elif key == "content" and "content_length" in response:
                response["content_length"] = len(str(value))
            elif key == "text" and "original_text" in response:
                response["original_text"] = value
        
        return {"success": True, "result": response}
    
    tool_endpoint.__name__ = f"{tool_name}_endpoint"
    tool_endpoint.__doc__ = config["description"]
    return tool_endpoint

# Add all tool endpoints
for tool_name, config in TOOLS_CONFIG.items():
    endpoint_func = create_tool_endpoint(tool_name, config)
    app.post(f"/{tool_name}", summary=config["description"])(endpoint_func)

@app.get("/openapi.json")
async def get_openapi():
    """Custom OpenAPI spec for Open WebUI integration - All 12 MCPO Tools"""
    paths = {}
    
    # Generate paths for all tools
    for tool_name, config in TOOLS_CONFIG.items():
        paths[f"/{tool_name}"] = {
            "post": {
                "operationId": f"mcpo_{tool_name}",
                "summary": config["description"],
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "query": {"type": "string", "description": "Query or input text"},
                                    "location": {"type": "string", "description": "Location (for weather)"},
                                    "content": {"type": "string", "description": "Content to process"},
                                    "text": {"type": "string", "description": "Text to translate"},
                                    "url": {"type": "string", "description": "URL (for web automation)"},
                                    "path": {"type": "string", "description": "File path"},
                                    "timezone": {"type": "string", "description": "Timezone"}
                                }
                            }
                        }
                    }
                },
                "responses": {"200": {"description": "Success"}}
            }
        }
    
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "MCPO Complete Proxy - All 12 Tools",
            "description": "Complete MCPO tools with namespace separation (excluding jina_crawler on port 8002)",
            "version": "1.0.0"
        },
        "paths": paths
    }

if __name__ == "__main__":
    print("🔧 Starting MCPO Complete Proxy")
    print(f"📊 Exposing {len(TOOLS_CONFIG)} tools with separate namespaces:")
    for tool_name, config in TOOLS_CONFIG.items():
        print(f"   ✅ /{tool_name}: {config['description']}")
    print("🔗 Available at: http://localhost:8000")
    print("📖 OpenAPI: http://localhost:8000/openapi.json")
    print("🎯 Each tool has its own namespace as requested")
    print("📝 Excluded: jina_crawler (available on port 8002)")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
