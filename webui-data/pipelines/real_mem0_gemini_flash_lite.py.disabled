"""
Simple Mem0 768D Pipeline
Optimized memory pipeline with 768 dimensions
"""

import os
import asyncio
import json
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    print("⚠️  mem0 not available - pipeline will run in mock mode")
    MEM0_AVAILABLE = False


class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(default="default_user", description="Default user ID")
        
        # Qdrant config
        qdrant_host: str = Field(default="qdrant", description="Qdrant host")
        qdrant_port: str = Field(default="6333", description="Qdrant port")
        collection_name: str = Field(default="mem0_openai_compatible_768", description="Collection name (768D with OpenAI-compatible endpoint)")
        
        # Gemini config
        gemini_api_key: str = Field(default="AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec", description="Gemini API key")
        
        # Memory settings
        max_memories: int = Field(default=3, description="Max memories to inject")
        relevance_threshold: float = Field(default=0.2, description="Relevance threshold")
        
        # Debug
        debug_logging: bool = Field(default=True, description="Enable debug logging")

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.memory_client = None

    async def get_memory_client(self):
        """Get or create memory client"""
        if self.memory_client is None and MEM0_AVAILABLE:
            try:
                # Set environment variables for OpenAI-compatible Gemini
                os.environ['OPENAI_BASE_URL'] = 'https://generativelanguage.googleapis.com/v1beta/openai/'
                os.environ['OPENAI_API_KEY'] = self.valves.gemini_api_key

                config = {
                    "vector_store": {
                        "provider": "qdrant",
                        "config": {
                            "host": self.valves.qdrant_host,
                            "port": int(self.valves.qdrant_port),
                            "collection_name": self.valves.collection_name,
                        },
                    },
                    "llm": {
                        "provider": "openai",
                        "config": {
                            "model": "gemini-2.5-flash-lite",
                            "temperature": 0.1,
                            "max_tokens": 1000,
                        },
                    },
                    "embedder": {
                        "provider": "gemini",
                        "config": {
                            "api_key": self.valves.gemini_api_key,
                            "model": "text-embedding-004",
                            "embedding_dims": 768,
                        },
                    },
                }
                
                self.memory_client = await AsyncMemory.from_config(config)
                if self.valves.debug_logging:
                    print(f"✅ Memory client initialized: {self.valves.collection_name}")
                    
            except Exception as e:
                if self.valves.debug_logging:
                    print(f"❌ Failed to initialize memory client: {e}")
                self.memory_client = None
        
        return self.memory_client

    async def on_startup(self):
        print(f"🚀 Starting Simple Mem0 Pipeline (768D - OpenAI Compatible)")
        print(f"📊 Collection: {self.valves.collection_name}")
        print(f"🔧 Dimensions: 768 (optimized)")
        print(f"🌐 Using OpenAI-compatible Gemini endpoint")
        await self.get_memory_client()

    async def on_shutdown(self):
        print(f"🛑 Shutting down Simple Mem0 Pipeline")

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Process incoming requests and inject memories"""
        if not MEM0_AVAILABLE:
            return body
            
        try:
            memory_client = await self.get_memory_client()
            if not memory_client:
                return body

            # Get user message
            messages = body.get("messages", [])
            if not messages:
                return body
                
            last_message = messages[-1]
            if last_message.get("role") != "user":
                return body
                
            user_message = last_message.get("content", "")
            if not user_message:
                return body

            # Get user ID
            user_id = user.get("id", self.valves.user_id) if user else self.valves.user_id
            
            # Search memories
            if self.valves.debug_logging:
                print(f"🔍 Searching memories for: {user_message[:50]}...")
            
            try:
                memories = await memory_client.search(
                    query=user_message,
                    user_id=user_id,
                    limit=10
                )
                
                # Filter by relevance
                relevant_memories = []
                for memory in memories:
                    score = memory.get("score", 0)
                    if score >= self.valves.relevance_threshold:
                        relevant_memories.append(memory)
                
                # Limit memories
                relevant_memories = relevant_memories[:self.valves.max_memories]
                
                if relevant_memories:
                    memory_context = "\n".join([
                        f"- {memory['memory']}" for memory in relevant_memories
                    ])
                    
                    # Inject system message
                    system_message = {
                        "role": "system",
                        "content": f"Relevant memories:\n{memory_context}\n\nUse these memories for context."
                    }
                    
                    body["messages"].insert(0, system_message)
                    
                    if self.valves.debug_logging:
                        print(f"💾 Injected {len(relevant_memories)} memories")
                
            except Exception as e:
                if self.valves.debug_logging:
                    print(f"❌ Error searching memories: {e}")
                
        except Exception as e:
            if self.valves.debug_logging:
                print(f"❌ Error in inlet: {e}")
            
        return body

    async def outlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Process outgoing responses and store memories"""
        if not MEM0_AVAILABLE:
            return body
            
        try:
            memory_client = await self.get_memory_client()
            if not memory_client:
                return body

            # Get user ID
            user_id = user.get("id", self.valves.user_id) if user else self.valves.user_id
            
            # Get messages
            messages = body.get("messages", [])
            if len(messages) < 2:
                return body
                
            # Find user and assistant messages
            user_message = None
            assistant_message = None
            
            for i in range(len(messages) - 1, -1, -1):
                msg = messages[i]
                if msg.get("role") == "assistant" and not assistant_message:
                    assistant_message = msg.get("content", "")
                elif msg.get("role") == "user" and not user_message:
                    user_message = msg.get("content", "")
                    
                if user_message and assistant_message:
                    break
            
            # Store conversation
            if user_message and assistant_message:
                conversation = f"User: {user_message}\nAssistant: {assistant_message}"
                
                try:
                    await memory_client.add(
                        messages=conversation,
                        user_id=user_id
                    )
                    
                    if self.valves.debug_logging:
                        print(f"💾 Stored conversation memory for user: {user_id}")
                        
                except Exception as e:
                    if self.valves.debug_logging:
                        print(f"❌ Error storing memory: {e}")
                    
        except Exception as e:
            if self.valves.debug_logging:
                print(f"❌ Error in outlet: {e}")
            
        return body
