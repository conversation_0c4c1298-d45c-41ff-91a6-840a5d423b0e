#!/usr/bin/env python3
"""
Simple fix to add reader mode fallback for 403 errors
"""

import subprocess

def add_simple_reader_fallback():
    """Add simple reader mode fallback"""
    
    print("🔧 Adding simple reader mode fallback...")
    
    # Read current jini_crawler.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading jini_crawler.py: {result.stderr}")
        return False
    
    content = result.stdout
    
    # Find the error handling section and add reader mode fallback
    old_error_section = '''                except Exception as e:
                    logger.error(f"❌ TLS bypass error: {e}")

            if not html_content:
                logger.error(f"❌ Error scraping {url}: Failed to fetch content from {url} (status: {status})")
                return None'''
    
    new_error_section = '''                except Exception as e:
                    logger.error(f"❌ TLS bypass error: {e}")

            # 🚀 READER MODE FALLBACK for 403/blocked sites
            if not html_content and status in [403, 429, 503]:
                logger.info(f"📖 Trying Reader Mode fallback for blocked URL: {url}")
                try:
                    # Use Googlebot user agent to bypass blocks
                    reader_headers = {
                        'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Referer': 'https://www.google.com/',
                    }
                    
                    async with self.session.get(url, headers=reader_headers, timeout=30) as response:
                        if response.status == 200:
                            html_content = await response.text()
                            status = 200
                            logger.info(f"✅ Reader Mode successful for {url}")
                        else:
                            logger.warning(f"⚠️ Reader Mode also failed with status {response.status}")
                            
                except Exception as e:
                    logger.error(f"❌ Reader Mode error: {e}")

            if not html_content:
                logger.error(f"❌ Error scraping {url}: Failed to fetch content from {url} (status: {status})")
                return None'''
    
    # Replace the error section
    content = content.replace(old_error_section, new_error_section)
    
    # Write fixed content
    with open('/tmp/jini_crawler_simple_reader.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/jini_crawler_simple_reader.py', 'jina-crawler-mcp:/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying fixed file: {copy_result.stderr}")
        return False
    
    print("✅ Simple reader mode fallback added")
    return True

def main():
    """Main function"""
    print("🚀 Adding simple reader mode fallback for 403 errors...")
    
    if add_simple_reader_fallback():
        print("\n🎉 Simple reader mode fallback added!")
        print("🔄 Restarting container...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 Fallback logic:")
        print("1. Regular request")
        print("2. TLS bypass (if Cloudflare)")
        print("3. 📖 Reader Mode (if 403/429/503)")
        print("🎯 Should handle zhihu.com 403 errors")
    else:
        print("\n❌ Failed to add reader mode fallback")

if __name__ == "__main__":
    main()
