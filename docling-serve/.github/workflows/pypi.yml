name: "Build and publish package"

on:
  release:
    types: [published]

permissions:
  contents: read

jobs:

  build-package:
    uses: ./.github/workflows/job-build.yml

  build-and-publish:
    needs:
      - build-package
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/docling-serve  # Replace <package-name> with your PyPI project name
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing
    steps:
      - name: Download all the dists
        uses: actions/download-artifact@v4
        with:
          name: python-package-distributions
          path: dist/
      - name: Publish distribution 📦 to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          # currently not working with reusable workflows
          attestations: false
