name: <PERSON><PERSON> GitHub Actions workflows
on:
  push:
    branches: ["main"]
    paths:
      - '.github/**'
  pull_request:
    branches: ["main"]
    paths:
      - '.github/**'

jobs:
  actionlint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Download actionlint
        id: get_actionlint
        run: bash <(curl https://raw.githubusercontent.com/rhysd/actionlint/main/scripts/download-actionlint.bash)
        shell: bash
      - name: Check workflow files
        run: PATH=".:$PATH" make action-lint
        shell: bash
