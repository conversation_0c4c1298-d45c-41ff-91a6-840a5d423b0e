name: Dry run docling-serve image building

on:
  workflow_call:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build_image:
    name: Build ${{ matrix.spec.name }} container image
    strategy:
      matrix:
        spec:
          - name: docling-project/docling-serve
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-extra flash-attn
            platforms: linux/amd64, linux/arm64
          - name: docling-project/docling-serve-cpu
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cpu --no-extra flash-attn
            platforms: linux/amd64, linux/arm64
          - name: docling-project/docling-serve-cu124
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cu124
            platforms: linux/amd64
          - name: docling-project/docling-serve-cu126
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cu126
            platforms: linux/amd64
          - name: docling-project/docling-serve-cu128
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cu128
            platforms: linux/amd64

    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write

    uses: ./.github/workflows/job-image.yml
    with:
      publish: false
      build_args: ${{ matrix.spec.build_args }}
      ghcr_image_name: ${{ matrix.spec.name }}
      quay_image_name: ""
      platforms: ${{ matrix.spec.platforms }}
