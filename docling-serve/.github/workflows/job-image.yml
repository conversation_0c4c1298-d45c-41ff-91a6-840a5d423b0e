name: Build docling-serve container image

on:
  workflow_call:
    inputs:
      build_args:
        type: string
        description: "Extra build arguments for the build."
        default: ""
      ghcr_image_name:
        type: string
        description: "Name of the image for GHCR."
      quay_image_name:
        type: string
        description: "Name of the image Quay."
      platforms:
        type: string
        description: "Platform argument for building images."
        default: linux/amd64, linux/arm64
      publish:
        type: boolean
        description: "If true, the images will be published."
        default: false
      environment:
        type: string
        description: "GH Action environment"
        default: ""

env:
  GHCR_REGISTRY: ghcr.io
  QUAY_REGISTRY: quay.io

jobs:
  image:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    environment: ${{ inputs.environment }}

    steps:
      - name: Free up space in github runner
        # Free space as indicated here : https://github.com/actions/runner-images/issues/2840#issuecomment-790492173
        run: |
            df -h
            sudo rm -rf "/usr/local/share/boost"
            sudo rm -rf "$AGENT_TOOLSDIRECTORY"
            sudo rm -rf /usr/share/dotnet /opt/ghc /usr/local/lib/android /usr/local/share/powershell /usr/share/swift /usr/local/.ghcup
            # shellcheck disable=SC2046
            sudo docker rmi "$(docker image ls -aq)" >/dev/null 2>&1 || true
            df -h

      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Log in to the GHCR container image registry
        if: ${{ inputs.publish }}
        uses: docker/login-action@v3
        with:
          registry: ${{ env.GHCR_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Log in to the Quay container image registry
        if: ${{ inputs.publish }}
        uses: docker/login-action@v3
        with:
          registry: ${{ env.QUAY_REGISTRY }}
          username: ${{ secrets.QUAY_USERNAME }}
          password: ${{ secrets.QUAY_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Extract metadata (tags, labels) for docling-serve ghcr image
        id: ghcr_meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.GHCR_REGISTRY }}/${{ inputs.ghcr_image_name }}

      - name: Build and push image to ghcr.io
        id: ghcr_push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ inputs.publish }}
          tags: ${{ steps.ghcr_meta.outputs.tags }}
          labels: ${{ steps.ghcr_meta.outputs.labels }}
          platforms: ${{ inputs.platforms}}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          file: Containerfile
          build-args: ${{ inputs.build_args }}

      - name: Generate artifact attestation
        if: ${{ inputs.publish }}
        uses: actions/attest-build-provenance@v1
        with:
          subject-name: ${{ env.GHCR_REGISTRY }}/${{ inputs.ghcr_image_name }}
          subject-digest: ${{ steps.ghcr_push.outputs.digest }}
          push-to-registry: true

      - name: Extract metadata (tags, labels) for docling-serve quay image
        if: ${{ inputs.publish }}
        id: quay_meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.QUAY_REGISTRY }}/${{ inputs.quay_image_name }}

      - name: Build and push image to quay.io
        if: ${{ inputs.publish }}
        # id: push-serve-cpu-quay
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ inputs.publish }}
          tags: ${{ steps.quay_meta.outputs.tags }}
          labels: ${{ steps.quay_meta.outputs.labels }}
          platforms: ${{ inputs.platforms}}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          file: Containerfile
          build-args: ${{ inputs.build_args }}
      
      # - name: Inspect the image details
      #   run: |
      #     echo "${{ steps.ghcr_push.outputs.metadata }}"

      - name: Remove Local Docker Images
        run: |
          docker image prune -af
