name: Publish docling-serve images

on:
  push:
    branches:
      - main
  release:
    types: [published]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build_and_publish_images:
    name: Build and push ${{ matrix.spec.name }} container image to GHCR and QUAY
    strategy:
      matrix:
        spec:
          - name: docling-project/docling-serve
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-extra flash-attn
            platforms: linux/amd64, linux/arm64
          - name: docling-project/docling-serve-cpu
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cpu --no-extra flash-attn
            platforms: linux/amd64, linux/arm64
          - name: docling-project/docling-serve-cu124
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cu124
            platforms: linux/amd64
          - name: docling-project/docling-serve-cu126
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cu126
            platforms: linux/amd64
          - name: docling-project/docling-serve-cu128
            build_args: |
              UV_SYNC_EXTRA_ARGS=--no-group pypi --group cu128
            platforms: linux/amd64

    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    secrets: inherit

    uses: ./.github/workflows/job-image.yml
    with:
      publish: true
      environment: registry-creds
      build_args: ${{ matrix.spec.build_args }}
      ghcr_image_name: ${{ matrix.spec.name }}
      quay_image_name: ${{ matrix.spec.name }}
      platforms: ${{ matrix.spec.platforms }}
