# Security and Disclosure Information Policy for the Docling Project

The Docling team and community take security bugs seriously. We appreciate your efforts to responsibly disclose your findings, and will make every effort to acknowledge your contributions.

## Reporting a Vulnerability

If you think you've identified a security issue in an Docling project repository, please DO NOT report the issue publicly via the GitHub issue tracker, etc.

Instead, send an email with as many details as possible to [<EMAIL>](mailto:<EMAIL>). This is a private mailing list for the maintainers team.

Please do not create a public issue.

## Security Vulnerability Response

Each report is acknowledged and analyzed by the core maintainers within 3 working days.

Any vulnerability information shared with core maintainers stays within the Docling project and will not be disseminated to other projects unless it is necessary to get the issue fixed.

After the initial reply to your report, the security team will keep you informed of the progress towards a fix and full announcement, and may ask for additional information or guidance.

## Security Alerts

We will send announcements of security vulnerabilities and steps to remediate on the [Docling announcements](https://github.com/docling-project/docling/discussions/categories/announcements).
