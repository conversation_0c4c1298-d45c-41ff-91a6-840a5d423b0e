"""
Perplexica Function Definitions for Open WebUI
These functions can be imported directly into Open WebUI for LLM usage
"""

import requests
import json

# MCPO Configuration
MCPO_BASE_URL = "http://localhost:8000"
API_KEY = "acca-enhanced-rag-mcp-key-2025"

def perplexica_quick_search(query: str) -> str:
    """
    ⚡ Quick AI-powered search using Perplexica
    
    Perfect for immediate information needs and quick fact-checking.
    Returns comprehensive results with AI analysis and source citations.
    
    Args:
        query (str): Search query to find information about
        
    Returns:
        str: Structured search results with AI summary and sources
        
    Example:
        result = perplexica_quick_search("latest AI developments 2025")
    """
    try:
        response = requests.post(
            f"{MCPO_BASE_URL}/perplexica/perplexica_quick_search",
            headers={
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            },
            json={"query": query},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return f"Search failed: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error performing search: {str(e)}"

def perplexica_search(query: str, focus_mode: str = "webSearch", optimization_mode: str = "balanced") -> str:
    """
    🔍 Advanced AI-powered web search using Perplexica
    
    Comprehensive search with customizable focus modes and optimization settings.
    
    Args:
        query (str): Search query to find information about
        focus_mode (str): Search focus - webSearch, academicSearch, youtubeSearch, redditSearch
        optimization_mode (str): Search optimization - speed, balanced, quality
        
    Returns:
        str: Detailed search results with AI analysis and sources
        
    Example:
        result = perplexica_search("machine learning trends", "academicSearch", "quality")
    """
    try:
        response = requests.post(
            f"{MCPO_BASE_URL}/perplexica/perplexica_search",
            headers={
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            },
            json={
                "query": query,
                "focusMode": focus_mode,
                "optimizationMode": optimization_mode
            },
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return f"Search failed: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error performing search: {str(e)}"

def perplexica_academic_search(query: str, optimization_mode: str = "quality") -> str:
    """
    🎓 Academic research search using Perplexica
    
    Find scholarly papers, research articles, and academic content with AI analysis.
    
    Args:
        query (str): Academic research query
        optimization_mode (str): Search optimization - speed, balanced, quality
        
    Returns:
        str: Academic search results with scholarly sources
        
    Example:
        result = perplexica_academic_search("neural networks in medical diagnosis")
    """
    try:
        response = requests.post(
            f"{MCPO_BASE_URL}/perplexica/perplexica_academic_search",
            headers={
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            },
            json={
                "query": query,
                "optimizationMode": optimization_mode
            },
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return f"Academic search failed: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error performing academic search: {str(e)}"

def perplexica_youtube_search(query: str) -> str:
    """
    📺 YouTube video search using Perplexica
    
    Find relevant videos, tutorials, and educational content with AI summaries.
    
    Args:
        query (str): Video search query
        
    Returns:
        str: YouTube search results with video information
        
    Example:
        result = perplexica_youtube_search("Python programming tutorial")
    """
    try:
        response = requests.post(
            f"{MCPO_BASE_URL}/perplexica/perplexica_youtube_search",
            headers={
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            },
            json={"query": query},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return f"YouTube search failed: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error performing YouTube search: {str(e)}"

def perplexica_reddit_search(query: str) -> str:
    """
    💬 Reddit discussion search using Perplexica
    
    Find community discussions, opinions, and real user experiences with AI analysis.
    
    Args:
        query (str): Topic to search for in Reddit discussions
        
    Returns:
        str: Reddit search results with community insights
        
    Example:
        result = perplexica_reddit_search("best programming languages 2025")
    """
    try:
        response = requests.post(
            f"{MCPO_BASE_URL}/perplexica/perplexica_reddit_search",
            headers={
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            },
            json={"query": query},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return f"Reddit search failed: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error performing Reddit search: {str(e)}"

def perplexica_chat(message: str, focus_mode: str = "webSearch", chat_id: str = None) -> str:
    """
    🤖 Interactive AI chat with Perplexica
    
    Have intelligent conversations with real-time web search capabilities.
    
    Args:
        message (str): Message to discuss with Perplexica AI
        focus_mode (str): Chat focus - webSearch, academicSearch, writingAssistant
        chat_id (str): Chat session ID to maintain context (optional)
        
    Returns:
        str: AI chat response with search context
        
    Example:
        result = perplexica_chat("What are the latest developments in renewable energy?")
    """
    try:
        payload = {
            "message": message,
            "focusMode": focus_mode
        }
        
        if chat_id:
            payload["chatId"] = chat_id
            
        response = requests.post(
            f"{MCPO_BASE_URL}/perplexica/perplexica_chat",
            headers={
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            },
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return f"Chat failed: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error in chat: {str(e)}"

# Function registry for Open WebUI
PERPLEXICA_FUNCTIONS = {
    "perplexica_quick_search": perplexica_quick_search,
    "perplexica_search": perplexica_search,
    "perplexica_academic_search": perplexica_academic_search,
    "perplexica_youtube_search": perplexica_youtube_search,
    "perplexica_reddit_search": perplexica_reddit_search,
    "perplexica_chat": perplexica_chat
}

# Usage examples for testing
if __name__ == "__main__":
    print("🧪 Testing Perplexica Functions")
    print("=" * 40)
    
    # Test quick search
    print("Testing quick search...")
    result = perplexica_quick_search("artificial intelligence")
    print(f"Result: {result[:200]}...")
    
    print("\n✅ Perplexica functions are ready for Open WebUI integration!")
