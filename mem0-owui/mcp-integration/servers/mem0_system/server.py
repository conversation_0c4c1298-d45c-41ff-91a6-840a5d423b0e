#!/usr/bin/env python3
"""
MCP Server for Mem0 Memory System Integration
Provides tools to interact with the AccA Mem0 memory system
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel,
    ServerCapabilities
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("mem0-mcp-server")

# Oracle Advanced Memory Pipeline - Mem0/Qdrant Configuration
QDRANT_HOST = "qdrant"  # Container name in acca-network
QDRANT_PORT = "6333"
MEM0_COLLECTION = "mem0_gemini_gemi_768"  # From pipeline config
EMBEDDING_MODEL = "text-embedding-004"
EMBEDDING_DIMENSIONS = 768

# Memory API Base URL - Fix for MEMORY_API_BASE error (MCPO runs on 8000)
MEMORY_API_BASE = "http://localhost:8000/api/v1/memory"

class Mem0MCPServer:
    def __init__(self):
        self.server = Server("mem0-system")
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.qdrant_client = None
        self.mem0_client = None
        self.setup_handlers()

    async def init_qdrant_connection(self):
        """Initialize Qdrant connection following Oracle Advanced Memory Pipeline"""
        try:
            from qdrant_client import QdrantClient
            from qdrant_client.http import models

            # Connect to Qdrant container
            self.qdrant_client = QdrantClient(
                host=QDRANT_HOST,
                port=int(QDRANT_PORT),
                timeout=30
            )

            # Check if collection exists
            collections = self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if MEM0_COLLECTION not in collection_names:
                logger.warning(f"Collection {MEM0_COLLECTION} not found. Available: {collection_names}")
                return False

            logger.info(f"✅ Connected to Qdrant: {QDRANT_HOST}:{QDRANT_PORT}, collection: {MEM0_COLLECTION}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to connect to Qdrant: {e}")
            return False

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available Mem0 tools"""
            return [
                Tool(
                    name="mem0_add_memory",
                    description="Add new memory from conversation messages with enhanced metadata",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "messages": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "role": {"type": "string", "enum": ["user", "assistant", "system"]},
                                        "content": {"type": "string"}
                                    },
                                    "required": ["role", "content"]
                                },
                                "description": "List of conversation messages to store as memory"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID for memory storage",
                                "default": "default_user"
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Optional session ID for grouping memories"
                            },
                            "conversation_id": {
                                "type": "string", 
                                "description": "Optional conversation ID for context"
                            },
                            "metadata": {
                                "type": "object",
                                "description": "Additional metadata to attach to memory"
                            }
                        },
                        "required": ["messages"]
                    }
                ),
                Tool(
                    name="mem0_search_memories",
                    description="Search for relevant memories with metadata filtering",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query to find relevant memories"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID to search memories for",
                                "default": "default_user"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of memories to return",
                                "default": 3,
                                "minimum": 1,
                                "maximum": 20
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Filter by session ID"
                            },
                            "source": {
                                "type": "string",
                                "description": "Filter by source (user, assistant, system)"
                            },
                            "tags": {
                                "type": "string",
                                "description": "Filter by tags (comma-separated)"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="mem0_enhance_prompt",
                    description="Enhance conversation with relevant memories using metadata",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "messages": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "role": {"type": "string", "enum": ["user", "assistant", "system"]},
                                        "content": {"type": "string"}
                                    },
                                    "required": ["role", "content"]
                                },
                                "description": "Conversation messages to enhance"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID for memory retrieval",
                                "default": "default_user"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of memories to use for enhancement",
                                "default": 3,
                                "minimum": 1,
                                "maximum": 10
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Session ID for context-aware enhancement"
                            }
                        },
                        "required": ["messages"]
                    }
                ),
                Tool(
                    name="mem0_get_recent_memories",
                    description="Get recent memories for a user (Oracle Advanced Memory Pipeline - optimized for LLM)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "User ID to get memories for",
                                "default": "default_user"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of memories to return (default: 10, max: 100)",
                                "default": 10,
                                "minimum": 1,
                                "maximum": 100
                            },
                            "category": {
                                "type": "string",
                                "description": "Filter by memory category (optional)",
                                "enum": ["personal", "work", "learning", "preferences", "facts"]
                            }
                        }
                    }
                ),
                Tool(
                    name="mem0_add_feedback",
                    description="Add user feedback to a memory for learning purposes",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "memory_id": {
                                "type": "string",
                                "description": "ID of the memory to provide feedback for"
                            },
                            "feedback": {
                                "type": "string",
                                "enum": ["positive", "negative", "neutral"],
                                "description": "Type of feedback"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID providing the feedback",
                                "default": "default_user"
                            }
                        },
                        "required": ["memory_id", "feedback"]
                    }
                ),
                Tool(
                    name="mem0_get_analytics",
                    description="Get analytics about user's memory usage",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "User ID to get analytics for",
                                "default": "default_user"
                            }
                        }
                    }
                ),
                Tool(
                    name="mem0_delete_memory",
                    description="Delete a specific memory",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "memory_id": {
                                "type": "string",
                                "description": "ID of the memory to delete"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID who owns the memory",
                                "default": "default_user"
                            }
                        },
                        "required": ["memory_id"]
                    }
                ),
                Tool(
                    name="mem0_get_settings",
                    description="Get current Mem0 system settings",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="mem0_update_settings",
                    description="Update Mem0 system settings",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "settings": {
                                "type": "object",
                                "description": "Settings object to update",
                                "properties": {
                                    "enabled": {"type": "boolean"},
                                    "enable_metadata": {"type": "boolean"},
                                    "auto_keyword_extraction": {"type": "boolean"},
                                    "auto_tag_extraction": {"type": "boolean"},
                                    "track_user_feedback": {"type": "boolean"},
                                    "embedding_provider": {
                                        "type": "object",
                                        "properties": {
                                            "provider": {"type": "string"},
                                            "model": {"type": "string"},
                                            "api_key": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "required": ["settings"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "mem0_add_memory":
                    return await self._add_memory(arguments)
                elif name == "mem0_search_memories":
                    return await self._search_memories(arguments)
                elif name == "mem0_enhance_prompt":
                    return await self._enhance_prompt(arguments)
                elif name == "mem0_get_recent_memories":
                    return await self._get_recent_memories(arguments)
                elif name == "mem0_add_feedback":
                    return await self._add_feedback(arguments)
                elif name == "mem0_get_analytics":
                    return await self._get_analytics(arguments)
                elif name == "mem0_delete_memory":
                    return await self._delete_memory(arguments)
                elif name == "mem0_get_settings":
                    return await self._get_settings(arguments)
                elif name == "mem0_update_settings":
                    return await self._update_settings(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _add_memory(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Add memory via API"""
        try:
            headers = {"Content-Type": "application/json"}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            payload = {
                "messages": arguments["messages"],
                "session_id": arguments.get("session_id"),
                "conversation_id": arguments.get("conversation_id"),
                "metadata": arguments.get("metadata")
            }
            
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/memories/add",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Memory added successfully: {result.get('message', 'Success')}"
            )]
        except Exception as e:
            logger.error(f"Error adding memory: {str(e)}")
            return [TextContent(type="text", text=f"Error adding memory: {str(e)}")]

    async def _search_memories(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Search memories in Qdrant - Oracle Advanced Memory Pipeline"""
        try:
            query = arguments.get("query", "")
            user_id = arguments.get("user_id", "default_user")
            limit = min(arguments.get("limit", 10), 100)

            if not query:
                return [TextContent(type="text", text="❌ Query is required for memory search")]

            # Initialize Qdrant connection if not done
            if not self.qdrant_client:
                await self.init_qdrant_connection()

            if not self.qdrant_client:
                return [TextContent(type="text", text="❌ Qdrant connection not available")]

            # Use Gemini to create embedding for search query
            try:
                import google.generativeai as genai
                genai.configure(api_key="AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")

                # Create embedding for search query - FIXED API usage
                embedding_result = genai.embed_content(
                    model="models/text-embedding-004",
                    content=query
                )
                query_vector = embedding_result['embedding']

            except Exception as e:
                logger.error(f"Failed to create embedding: {e}")
                return [TextContent(type="text", text=f"❌ Embedding error: {str(e)}")]

            # Search in Qdrant with vector similarity
            from qdrant_client.http import models

            search_result = self.qdrant_client.search(
                collection_name=MEM0_COLLECTION,
                query_vector=query_vector,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="user_id",
                            match=models.MatchValue(value=user_id)
                        )
                    ]
                ),
                limit=limit,
                with_payload=True,
                with_vectors=False
            )

            if not search_result:
                return [TextContent(type="text", text=f"No relevant memories found for query: '{query}'")]

            # Format results for LLM
            memory_text = f"🔍 **Search Results for '{query}' ({len(search_result)} items)** - REAL DATA\n\n"

            for i, scored_point in enumerate(search_result, 1):
                payload = scored_point.payload
                memory_content = payload.get("memory", payload.get("text", ""))
                memory_id = str(scored_point.id)
                score = scored_point.score
                category = payload.get("category", "general")

                # Truncate long memories
                if len(memory_content) > 200:
                    memory_content = memory_content[:200] + "..."

                memory_text += f"**{i}.** `{memory_id}` [Score: {score:.3f}] [{category}]\n"
                memory_text += f"   {memory_content}\n\n"

            return [TextContent(type="text", text=memory_text)]
            
            return [TextContent(type="text", text=memory_text)]
        except Exception as e:
            logger.error(f"Error searching memories: {str(e)}")
            return [TextContent(type="text", text=f"Error searching memories: {str(e)}")]

    async def _enhance_prompt(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Enhance prompt with memories via API"""
        try:
            headers = {"Content-Type": "application/json"}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            params = {
                "limit": arguments.get("limit", 10)
            }
            if arguments.get("session_id"):
                params["session_id"] = arguments["session_id"]
            
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/memories/enhance",
                json=arguments["messages"],
                params=params,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            enhanced_messages = result.get("enhanced_messages", arguments["messages"])
            
            return [TextContent(
                type="text",
                text=f"Enhanced conversation with {len(enhanced_messages)} messages:\n\n" +
                     json.dumps(enhanced_messages, indent=2, ensure_ascii=False)
            )]
        except Exception as e:
            logger.error(f"Error enhancing prompt: {str(e)}")
            return [TextContent(type="text", text=f"Error enhancing prompt: {str(e)}")]

    async def _get_recent_memories(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get recent memories from Qdrant - Oracle Advanced Memory Pipeline"""
        try:
            user_id = arguments.get("user_id", "default_user")
            limit = min(arguments.get("limit", 10), 100)
            category = arguments.get("category")

            # Initialize Qdrant connection if not done
            if not self.qdrant_client:
                await self.init_qdrant_connection()

            if not self.qdrant_client:
                return [TextContent(type="text", text="❌ Qdrant connection not available")]

            # Search memories in Qdrant
            from qdrant_client.http import models

            # Build filter for user_id and category
            filter_conditions = [
                models.FieldCondition(
                    key="user_id",
                    match=models.MatchValue(value=user_id)
                )
            ]

            if category:
                filter_conditions.append(
                    models.FieldCondition(
                        key="category",
                        match=models.MatchValue(value=category)
                    )
                )

            # Search recent memories
            search_result = self.qdrant_client.scroll(
                collection_name=MEM0_COLLECTION,
                scroll_filter=models.Filter(
                    must=filter_conditions
                ),
                limit=limit,
                with_payload=True,
                with_vectors=False
            )

            points = search_result[0]  # Get points from scroll result

            if not points:
                return [TextContent(type="text", text=f"No recent memories found for user: {user_id}")]

            # Format for LLM consumption - concise and structured
            memory_text = f"📋 **Recent Memories ({len(points)} items)** - REAL DATA\n\n"

            for i, point in enumerate(points, 1):
                payload = point.payload
                memory_content = payload.get("memory", payload.get("text", ""))
                memory_id = str(point.id)
                category = payload.get("category", "general")
                created_at = payload.get("created_at", payload.get("timestamp", ""))

                # Truncate long memories for LLM efficiency
                if len(memory_content) > 200:
                    memory_content = memory_content[:200] + "..."

                memory_text += f"**{i}.** `{memory_id}` [{category}]\n"
                memory_text += f"   {memory_content}\n"
                if created_at:
                    memory_text += f"   *{created_at}*\n"
                memory_text += "\n"

            return [TextContent(type="text", text=memory_text)]

        except Exception as e:
            logger.error(f"Error getting recent memories from Qdrant: {str(e)}")
            # Fallback to demo mode if Qdrant fails
            return [TextContent(type="text", text=f"❌ Qdrant error: {str(e)}\nUsing demo mode fallback")]

    async def _add_feedback(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Add feedback via API"""
        try:
            headers = {"Content-Type": "application/json"}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            payload = {
                "memory_id": arguments["memory_id"],
                "feedback": arguments["feedback"]
            }
            
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/memories/feedback",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Feedback added successfully: {result.get('message', 'Success')}"
            )]
        except Exception as e:
            logger.error(f"Error adding feedback: {str(e)}")
            return [TextContent(type="text", text=f"Error adding feedback: {str(e)}")]

    async def _get_analytics(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get analytics via API"""
        try:
            headers = {}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            response = await self.http_client.get(
                f"{MEMORY_API_BASE}/memories/analytics",
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            analytics_text = "Memory Analytics:\n\n"
            analytics_text += f"Total Memories: {result.get('total_memories', 0)}\n"
            
            if result.get('languages'):
                analytics_text += f"Languages: {json.dumps(result['languages'], indent=2)}\n"
            
            if result.get('sources'):
                analytics_text += f"Sources: {json.dumps(result['sources'], indent=2)}\n"
            
            if result.get('generated_at'):
                analytics_text += f"Generated at: {result['generated_at']}\n"
            
            return [TextContent(type="text", text=analytics_text)]
        except Exception as e:
            logger.error(f"Error getting analytics: {str(e)}")
            return [TextContent(type="text", text=f"Error getting analytics: {str(e)}")]

    async def _delete_memory(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Delete memory via API"""
        try:
            headers = {}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            response = await self.http_client.delete(
                f"{MEMORY_API_BASE}/memories/{arguments['memory_id']}",
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Memory deleted successfully: {result.get('message', 'Success')}"
            )]
        except Exception as e:
            logger.error(f"Error deleting memory: {str(e)}")
            return [TextContent(type="text", text=f"Error deleting memory: {str(e)}")]

    async def _get_settings(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get settings via API"""
        try:
            response = await self.http_client.get(f"{MEMORY_API_BASE}/settings")
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Current Mem0 Settings:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
        except Exception as e:
            logger.error(f"Error getting settings: {str(e)}")
            return [TextContent(type="text", text=f"Error getting settings: {str(e)}")]

    async def _update_settings(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Update settings via API"""
        try:
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/settings",
                json=arguments["settings"],
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Settings updated successfully:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
        except Exception as e:
            logger.error(f"Error updating settings: {str(e)}")
            return [TextContent(type="text", text=f"Error updating settings: {str(e)}")]

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="mem0-system",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = Mem0MCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())