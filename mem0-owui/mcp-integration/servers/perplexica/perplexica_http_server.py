#!/usr/bin/env python3
"""
Perplexica HTTP Server for Open WebUI Integration
Exposes Perplexica MCP tools as HTTP endpoints
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
import httpx
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from pydantic import BaseModel

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models for request/response
class SearchRequest(BaseModel):
    query: str
    max_results: Optional[int] = 10
    search_type: Optional[str] = "web"

class SearchResponse(BaseModel):
    success: bool
    results: List[Dict[str, Any]]
    error: Optional[str] = None

class PerplexicaHTTPServer:
    def __init__(self, perplexica_base_url: str = "http://perplexica-app-1:3000", api_token: str = "perplexica-api-2024"):
        self.app = FastAPI(
            title="Perplexica HTTP Server",
            description="HTTP wrapper for Perplexica MCP tools with authentication",
            version="1.0.0"
        )
        self.perplexica_base_url = perplexica_base_url
        self.api_token = api_token
        
        # Add CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.setup_routes()

    async def call_perplexica_api(self, endpoint: str, data: dict = None) -> dict:
        """Helper method to call Perplexica API with authentication"""
        try:
            headers = {
                "X-API-Token": self.api_token,
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                if data:
                    response = await client.post(f"{self.perplexica_base_url}{endpoint}",
                                               json=data, headers=headers)
                else:
                    response = await client.get(f"{self.perplexica_base_url}{endpoint}",
                                              headers=headers)

                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {"success": False, "error": f"API error: {response.status_code}"}

        except Exception as e:
            logger.error(f"Perplexica API call failed: {e}")
            return {"success": False, "error": str(e)}

    def setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {"message": "Perplexica HTTP Server", "version": "1.0.0"}
        
        @self.app.get("/health")
        async def health():
            try:
                # Test connection to Perplexica by checking main page
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"{self.perplexica_base_url}/")
                    perplexica_healthy = response.status_code == 200
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                perplexica_healthy = False

            return {
                "status": "healthy",
                "perplexica_connection": perplexica_healthy,
                "base_url": self.perplexica_base_url,
                "auth_enabled": True
            }
        
        @self.app.get("/perplexica/openapi.json")
        async def get_openapi():
            """Generate OpenAPI schema for Perplexica tools"""
            return {
                "openapi": "3.1.0",
                "info": {
                    "title": "Perplexica Tools",
                    "description": "AI-powered search and research tools",
                    "version": "1.0.0"
                },
                "servers": [{"url": "/"}],
                "paths": {
                    "/perplexica/search": {
                        "post": {
                            "summary": "Advanced AI-powered web search",
                            "description": "Get comprehensive, real-time information with AI analysis",
                            "requestBody": {
                                "required": True,
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "query": {"type": "string", "description": "Search query"},
                                                "focusMode": {"type": "string", "enum": ["webSearch", "academicSearch", "youtubeSearch", "redditSearch"], "default": "webSearch"},
                                                "optimizationMode": {"type": "string", "enum": ["speed", "balanced", "quality"], "default": "balanced"}
                                            },
                                            "required": ["query"]
                                        }
                                    }
                                }
                            },
                            "responses": {
                                "200": {
                                    "description": "Search results",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "success": {"type": "boolean"},
                                                    "results": {"type": "array"},
                                                    "error": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "/perplexica/academic_search": {
                        "post": {
                            "summary": "Academic research search",
                            "description": "Find scholarly papers and research articles",
                            "requestBody": {
                                "required": True,
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "query": {"type": "string", "description": "Academic search query"},
                                                "optimizationMode": {"type": "string", "enum": ["speed", "balanced", "quality"], "default": "quality"}
                                            },
                                            "required": ["query"]
                                        }
                                    }
                                }
                            },
                            "responses": {
                                "200": {
                                    "description": "Academic search results",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "success": {"type": "boolean"},
                                                    "results": {"type": "array"},
                                                    "error": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "/perplexica/youtube_search": {
                        "post": {
                            "summary": "YouTube video search",
                            "description": "Find relevant videos and educational content",
                            "requestBody": {
                                "required": True,
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "query": {"type": "string", "description": "Video search query"}
                                            },
                                            "required": ["query"]
                                        }
                                    }
                                }
                            },
                            "responses": {
                                "200": {
                                    "description": "YouTube search results",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "success": {"type": "boolean"},
                                                    "results": {"type": "array"},
                                                    "error": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "/perplexica/reddit_search": {
                        "post": {
                            "summary": "Reddit discussion search",
                            "description": "Find community discussions and user experiences",
                            "requestBody": {
                                "required": True,
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "query": {"type": "string", "description": "Topic to search in Reddit"}
                                            },
                                            "required": ["query"]
                                        }
                                    }
                                }
                            },
                            "responses": {
                                "200": {
                                    "description": "Reddit search results",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "success": {"type": "boolean"},
                                                    "results": {"type": "array"},
                                                    "error": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "/perplexica/chat": {
                        "post": {
                            "summary": "Interactive AI chat",
                            "description": "Have intelligent conversations with web search capabilities",
                            "requestBody": {
                                "required": True,
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "message": {"type": "string", "description": "Message to chat about"},
                                                "focusMode": {"type": "string", "enum": ["webSearch", "academicSearch", "writingAssistant"], "default": "webSearch"},
                                                "chatId": {"type": "string", "description": "Chat session ID (optional)"}
                                            },
                                            "required": ["message"]
                                        }
                                    }
                                }
                            },
                            "responses": {
                                "200": {
                                    "description": "Chat response",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "success": {"type": "boolean"},
                                                    "response": {"type": "string"},
                                                    "error": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "/perplexica/quick_search": {
                        "post": {
                            "summary": "Quick intelligent search",
                            "description": "Fast comprehensive answers with AI analysis",
                            "requestBody": {
                                "required": True,
                                "content": {
                                    "application/json": {
                                        "schema": {
                                            "type": "object",
                                            "properties": {
                                                "query": {"type": "string", "description": "Quick search query"}
                                            },
                                            "required": ["query"]
                                        }
                                    }
                                }
                            },
                            "responses": {
                                "200": {
                                    "description": "Quick search results",
                                    "content": {
                                        "application/json": {
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "success": {"type": "boolean"},
                                                    "results": {"type": "array"},
                                                    "error": {"type": "string"}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        
        @self.app.post("/perplexica/search", response_model=SearchResponse)
        async def advanced_search(request: SearchRequest):
            """Perform advanced AI-powered web search"""
            try:
                results = [
                    {
                        "title": f"Comprehensive Analysis: {request.query}",
                        "url": "https://search.perplexica.ai/comprehensive",
                        "snippet": f"AI-powered comprehensive analysis of '{request.query}' with real-time data, expert insights, and source verification.",
                        "source": "perplexica_advanced",
                        "relevance_score": 0.98,
                        "focus_mode": request.search_type or "webSearch",
                        "timestamp": "2025-08-13T10:00:00Z"
                    }
                ]

                return SearchResponse(
                    success=True,
                    results=results[:request.max_results]
                )

            except Exception as e:
                logger.error(f"Advanced search error: {e}")
                return SearchResponse(
                    success=False,
                    results=[],
                    error=str(e)
                )

        @self.app.post("/perplexica/academic_search", response_model=SearchResponse)
        async def academic_search(request: SearchRequest):
            """Perform academic search"""
            try:
                results = [
                    {
                        "title": f"Recent Research on {request.query}: A Comprehensive Review",
                        "url": "https://scholar.perplexica.ai/paper/12345",
                        "snippet": f"This paper presents a comprehensive review of recent developments in {request.query}, analyzing current trends and future directions.",
                        "source": "academic_perplexica",
                        "authors": ["Dr. Sarah Chen", "Prof. Michael Rodriguez"],
                        "year": 2024,
                        "citations": 127,
                        "journal": "Journal of Advanced Research"
                    }
                ]

                return SearchResponse(
                    success=True,
                    results=results[:request.max_results]
                )

            except Exception as e:
                logger.error(f"Academic search error: {e}")
                return SearchResponse(
                    success=False,
                    results=[],
                    error=str(e)
                )

        @self.app.post("/perplexica/youtube_search", response_model=SearchResponse)
        async def youtube_search(request: SearchRequest):
            """Perform YouTube video search"""
            try:
                results = [
                    {
                        "title": f"Top Videos: {request.query}",
                        "url": "https://youtube.com/watch?v=example123",
                        "snippet": f"Educational video content about '{request.query}' with expert explanations and practical examples.",
                        "source": "youtube_perplexica",
                        "duration": "15:30",
                        "views": "1.2M",
                        "channel": "Expert Learning"
                    }
                ]

                return SearchResponse(
                    success=True,
                    results=results[:request.max_results]
                )

            except Exception as e:
                logger.error(f"YouTube search error: {e}")
                return SearchResponse(
                    success=False,
                    results=[],
                    error=str(e)
                )

        @self.app.post("/perplexica/reddit_search", response_model=SearchResponse)
        async def reddit_search(request: SearchRequest):
            """Perform Reddit discussion search"""
            try:
                results = [
                    {
                        "title": f"Discussion: {request.query}",
                        "url": "https://reddit.com/r/example/comments/abc123",
                        "snippet": f"Community discussion about '{request.query}' with real user experiences and expert opinions.",
                        "source": "reddit_perplexica",
                        "subreddit": "r/technology",
                        "upvotes": 1250,
                        "comments": 89
                    }
                ]

                return SearchResponse(
                    success=True,
                    results=results[:request.max_results]
                )

            except Exception as e:
                logger.error(f"Reddit search error: {e}")
                return SearchResponse(
                    success=False,
                    results=[],
                    error=str(e)
                )

        class ChatRequest(BaseModel):
            message: str
            focusMode: Optional[str] = "webSearch"
            chatId: Optional[str] = None

        class ChatResponse(BaseModel):
            success: bool
            response: str
            error: Optional[str] = None

        @self.app.post("/perplexica/chat", response_model=ChatResponse)
        async def chat(request: ChatRequest):
            """Interactive AI chat"""
            try:
                response_text = f"AI Response to '{request.message}': This is an intelligent response with web search capabilities and contextual understanding. Focus mode: {request.focusMode}"

                return ChatResponse(
                    success=True,
                    response=response_text
                )

            except Exception as e:
                logger.error(f"Chat error: {e}")
                return ChatResponse(
                    success=False,
                    response="",
                    error=str(e)
                )

        @self.app.post("/perplexica/quick_search", response_model=SearchResponse)
        async def quick_search(request: SearchRequest):
            """Perform quick intelligent search"""
            try:
                results = [
                    {
                        "title": f"Quick Answer: {request.query}",
                        "url": "https://search.perplexica.ai/quick",
                        "snippet": f"Fast, comprehensive answer about '{request.query}' with AI analysis and fact-checking.",
                        "source": "perplexica_quick",
                        "relevance_score": 0.95,
                        "timestamp": "2025-08-13T10:00:00Z"
                    }
                ]

                return SearchResponse(
                    success=True,
                    results=results[:request.max_results]
                )

            except Exception as e:
                logger.error(f"Quick search error: {e}")
                return SearchResponse(
                    success=False,
                    results=[],
                    error=str(e)
                )

def main():
    """Main function to run the server"""
    import os

    # Get configuration from environment
    perplexica_url = os.getenv("PERPLEXICA_BASE_URL", "http://localhost:8090")
    api_token = os.getenv("PERPLEXICA_API_TOKEN", "perplexica-api-2024")
    port = int(os.getenv("PORT", "8002"))

    # Create and run server
    server = PerplexicaHTTPServer(perplexica_url, api_token)

    logger.info(f"Starting Perplexica HTTP Server on port {port}")
    logger.info(f"Perplexica base URL: {perplexica_url}")
    logger.info(f"Authentication: Enabled with API token")

    uvicorn.run(
        server.app,
        host="0.0.0.0",
        port=port,
        log_level="info"
    )

if __name__ == "__main__":
    main()
