#!/usr/bin/env python3
"""
Perplexica MCP Server - Python wrapper for Node.js Perplexica MCP Server
Integrates Perplexica AI-powered search into the MCPO ecosystem
"""

import asyncio
import json
import logging
import subprocess
import sys
import os
from typing import Any, Dict, List, Optional
from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.types import (
    Resource, Tool, TextContent, ImageContent, EmbeddedResource
)
import mcp.types as types
import mcp.server.stdio
import requests
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("perplexica-mcp-server")

class PerplexicaMCPServer:
    def __init__(self):
        self.server = Server("perplexica-mcp-server")
        self.node_process = None
        self.perplexica_base_url = os.getenv("PERPLEXICA_BASE_URL", "http://localhost:3001")
        self.node_server_path = "/home/<USER>/perplexica/perplexica-mcp-server/dist/index.js"
        
        # Setup handlers
        self.setup_handlers()
        
    def setup_handlers(self):
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available Perplexica tools"""
            return [
                Tool(
                    name="perplexica_search",
                    description="🔍 Advanced AI-powered web search using Perplexica - Get comprehensive, real-time information with AI analysis and source citations. Perfect for current events, research, and fact-finding.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query to find comprehensive information about"
                            },
                            "focusMode": {
                                "type": "string",
                                "enum": ["webSearch", "academicSearch", "writingAssistant", "wolframAlphaSearch", "youtubeSearch", "redditSearch"],
                                "default": "webSearch",
                                "description": "Search focus: webSearch for general info, academicSearch for scholarly content, youtubeSearch for videos, redditSearch for discussions"
                            },
                            "optimizationMode": {
                                "type": "string",
                                "enum": ["speed", "balanced", "quality"],
                                "default": "balanced",
                                "description": "Search optimization: speed for quick results, balanced for good quality, quality for thorough analysis"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="perplexica_academic_search",
                    description="🎓 Academic research search using Perplexica - Find scholarly papers, research articles, and academic content with AI-powered analysis and proper citations.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Academic research query to find scholarly information about"
                            },
                            "optimizationMode": {
                                "type": "string",
                                "enum": ["speed", "balanced", "quality"],
                                "default": "quality",
                                "description": "Search optimization: quality recommended for academic research"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="perplexica_youtube_search",
                    description="📺 YouTube video search using Perplexica - Find relevant videos, tutorials, and educational content with AI-powered summaries and insights.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Video search query to find YouTube content about"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="perplexica_reddit_search",
                    description="💬 Reddit discussion search using Perplexica - Find community discussions, opinions, and real user experiences with AI analysis.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Topic to search for in Reddit discussions"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="perplexica_chat",
                    description="🤖 Interactive AI chat with Perplexica - Have intelligent conversations with real-time web search capabilities and contextual responses.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "message": {
                                "type": "string",
                                "description": "Question or message to discuss with Perplexica AI"
                            },
                            "focusMode": {
                                "type": "string",
                                "enum": ["webSearch", "academicSearch", "writingAssistant", "wolframAlphaSearch", "youtubeSearch", "redditSearch"],
                                "default": "webSearch",
                                "description": "Chat focus: webSearch for general topics, academicSearch for research, writingAssistant for content creation"
                            },
                            "chatId": {
                                "type": "string",
                                "description": "Chat session ID to maintain conversation context (optional)"
                            }
                        },
                        "required": ["message"]
                    }
                ),
                Tool(
                    name="perplexica_quick_search",
                    description="⚡ Quick intelligent search using Perplexica - Fast, comprehensive answers with AI analysis. Perfect for immediate information needs and quick fact-checking.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Quick search query to get immediate comprehensive information about"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="perplexica_health_check",
                    description="Check if Perplexica server is running and accessible",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent | types.ImageContent | types.EmbeddedResource]:
            """Handle tool calls by proxying to Node.js MCP server"""
            try:
                if name == "perplexica_health_check":
                    return await self.handle_health_check()
                elif name == "perplexica_quick_search":
                    # Quick search uses webSearch mode with balanced optimization
                    quick_args = {
                        "query": arguments.get("query", ""),
                        "focusMode": "webSearch",
                        "optimizationMode": "balanced"
                    }
                    result = await self.call_perplexica_api("perplexica_search", quick_args)
                else:
                    # For other tools, call Perplexica API directly
                    result = await self.call_perplexica_api(name, arguments)
                
                return [types.TextContent(
                    type="text",
                    text=result
                )]
                
            except Exception as e:
                logger.error(f"Error calling tool {name}: {e}")
                return [types.TextContent(
                    type="text", 
                    text=f"Error: {str(e)}"
                )]

    async def handle_health_check(self) -> List[types.TextContent]:
        """Check Perplexica server health"""
        try:
            response = requests.get(f"{self.perplexica_base_url}/", timeout=5)
            is_healthy = response.status_code == 200
            
            status_text = f"""# Perplexica Health Check

**Status:** {'✅ Healthy' if is_healthy else '❌ Unhealthy'}
**Base URL:** {self.perplexica_base_url}
**Response Code:** {response.status_code if is_healthy else 'Connection failed'}
**Node.js MCP Server:** {'✅ Available' if os.path.exists(self.node_server_path) else '❌ Not found'}
"""
            
            return [types.TextContent(type="text", text=status_text)]
            
        except Exception as e:
            return [types.TextContent(
                type="text",
                text=f"# Perplexica Health Check\n\n**Status:** ❌ Unhealthy\n**Error:** {str(e)}"
            )]

    async def call_perplexica_api(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Call Perplexica API directly"""
        try:
            if tool_name == "perplexica_search" or tool_name == "perplexica_quick_search":
                return await self.handle_search_api(arguments)
            elif tool_name == "perplexica_academic_search":
                return await self.handle_search_api({**arguments, "focusMode": "academicSearch"})
            elif tool_name == "perplexica_youtube_search":
                return await self.handle_search_api({**arguments, "focusMode": "youtubeSearch"})
            elif tool_name == "perplexica_reddit_search":
                return await self.handle_search_api({**arguments, "focusMode": "redditSearch"})
            elif tool_name == "perplexica_chat":
                return await self.handle_chat_api(arguments)
            else:
                return f"Unknown tool: {tool_name}"

        except Exception as e:
            logger.error(f"Error calling Perplexica API: {e}")
            return f"Error: {str(e)}"

    async def handle_search_api(self, arguments: Dict[str, Any]) -> str:
        """Handle search API call"""
        try:
            query = arguments.get("query", "")
            focus_mode = arguments.get("focusMode", "webSearch")
            optimization_mode = arguments.get("optimizationMode", "balanced")

            # Prepare request payload
            payload = {
                "query": query,
                "focusMode": focus_mode
            }

            # Make API call to Perplexica
            response = requests.post(
                f"{self.perplexica_base_url}/api/search",
                json=payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code != 200:
                return f"Perplexica API Error: {response.status_code} - {response.text}"

            data = response.json()

            # Format response like Gemini Search Engine
            response_data = {
                "query": query,
                "search_type": f"perplexica_{focus_mode}",
                "optimization_mode": optimization_mode,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
                "source": "Perplexica AI Search Engine",
                "ai_summary": data.get("message", ""),
                "sources_count": len(data.get("sources", [])),
                "sources": []
            }

            # Add sources with proper formatting
            if "sources" in data and data["sources"]:
                for i, source in enumerate(data["sources"][:10]):  # Limit to 10 sources
                    metadata = source.get("metadata", {})
                    source_info = {
                        "rank": i + 1,
                        "title": metadata.get("title", "Unknown Title"),
                        "url": metadata.get("url", "No URL"),
                        "content_preview": source.get("pageContent", "")[:300],
                        "relevance": "high" if i < 3 else "medium" if i < 7 else "low"
                    }
                    response_data["sources"].append(source_info)

            # Return structured JSON like Gemini Search Engine
            import json
            result = f"Perplexica Search Results:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            return result

        except requests.exceptions.RequestException as e:
            return f"Network Error: {str(e)}"
        except Exception as e:
            return f"Error: {str(e)}"

    async def handle_chat_api(self, arguments: Dict[str, Any]) -> str:
        """Handle chat API call"""
        try:
            message = arguments.get("message", "")
            focus_mode = arguments.get("focusMode", "webSearch")
            chat_id = arguments.get("chatId", f"chat_{int(time.time())}")

            # Prepare request payload
            payload = {
                "message": {
                    "messageId": f"msg_{int(time.time())}",
                    "chatId": chat_id,
                    "content": message
                },
                "focusMode": focus_mode
            }

            # Make API call
            response = requests.post(
                f"{self.perplexica_base_url}/api/chat",
                json=payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code != 200:
                return f"API Error: {response.status_code} - {response.text}"

            data = response.json()

            # Format response
            result = f"# Perplexica Chat Response\n\n"
            result += f"**Chat ID:** {chat_id}\n"
            result += f"**Focus Mode:** {focus_mode}\n\n"

            if "message" in data:
                result += f"## Response\n{data['message']}\n\n"

            if "sources" in data and data["sources"]:
                result += f"## Sources Referenced\n\n"
                for i, source in enumerate(data["sources"][:5]):  # Limit to 5 sources
                    metadata = source.get("metadata", {})
                    title = metadata.get("title", "Unknown Title")
                    url = metadata.get("url", "No URL")
                    result += f"{i + 1}. [{title}]({url})\n"

            return result

        except requests.exceptions.RequestException as e:
            return f"Network Error: {str(e)}"
        except Exception as e:
            return f"Error: {str(e)}"



async def main():
    # Initialize server
    server_instance = PerplexicaMCPServer()

    # Run server
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server_instance.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="perplexica-mcp-server",
                server_version="1.0.0",
                capabilities=server_instance.server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={}
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
