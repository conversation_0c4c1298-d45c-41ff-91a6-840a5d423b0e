#!/usr/bin/env python3
"""
🏛️ ORACLE DATABASE MCP SERVER
Advanced Oracle Memory Database Access via MCP
Oracle Advanced Memory Pipeline Compatible
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional, Sequence
from pathlib import Path
import hashlib
import pickle
from datetime import datetime

# MCP imports - Compatible with MCPO container
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Tool,
    TextContent,
)

# Oracle imports
try:
    import oracledb
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    logging.warning("oracledb not available. Install with: pip install oracledb")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("oracle-mcp-server")

class OracleSchemaCache:
    """Smart schema caching for Oracle databases"""
    
    def __init__(self, cache_dir: str = ".cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "oracle_schema_cache.pkl"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._cache = {}
        self._metadata = {}
        self.load_cache()
    
    def _get_cache_key(self, connection_string: str, schema: str) -> str:
        """Generate cache key from connection and schema"""
        key_data = f"{connection_string}:{schema}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def load_cache(self):
        """Load cache from disk"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    self._metadata = json.load(f)
            logger.info(f"Loaded cache with {len(self._cache)} entries")
        except Exception as e:
            logger.warning(f"Failed to load cache: {e}")
            self._cache = {}
            self._metadata = {}
    
    def save_cache(self):
        """Save cache to disk"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._cache, f)
            with open(self.metadata_file, 'w') as f:
                json.dump(self._metadata, f, indent=2)
            logger.info(f"Saved cache with {len(self._cache)} entries")
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
    
    def get_schema_cache(self, connection_string: str, schema: str) -> Optional[Dict]:
        """Get cached schema data"""
        cache_key = self._get_cache_key(connection_string, schema)
        return self._cache.get(cache_key)
    
    def set_schema_cache(self, connection_string: str, schema: str, data: Dict):
        """Set cached schema data"""
        cache_key = self._get_cache_key(connection_string, schema)
        self._cache[cache_key] = data
        self._metadata[cache_key] = {
            "timestamp": datetime.now().isoformat(),
            "schema": schema,
            "table_count": len(data.get("tables", {}))
        }
        self.save_cache()

class OracleDBServer:
    """Oracle Database MCP Server - Oracle Advanced Memory Pipeline Compatible"""

    def __init__(self):
        # Oracle Advanced Memory Pipeline Configuration
        self.oracle_user = os.getenv("ORACLE_USER", "ADMIN")
        self.oracle_password = os.getenv("ORACLE_PASSWORD", "Twilv0zera@123")
        self.oracle_dsn = os.getenv("ORACLE_DSN", "(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))")
        self.oracle_wallet_location = os.getenv("ORACLE_WALLET_LOCATION", "/home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T")
        self.oracle_tns_alias = os.getenv("ORACLE_TNS_ALIAS", "swiv8hv5y96iwo2t_high")
        self.oracle_wallet_password = os.getenv("ORACLE_WALLET_PASSWORD", "Twilv0zera@123")

        self.target_schema = os.getenv("TARGET_SCHEMA", "ADMIN")
        self.cache_dir = os.getenv("CACHE_DIR", ".cache")

        if not ORACLE_AVAILABLE:
            logger.warning("oracledb package not available - using demo mode")

        self.cache = OracleSchemaCache(self.cache_dir)
        self.pool = None
        self.demo_mode = False  # Force real connection
        # Initialize connection immediately
        asyncio.create_task(self.initialize_oracle_pool())
        
    async def initialize_oracle_pool(self):
        """Initialize Oracle connection pool following Oracle Advanced Memory Pipeline"""
        if not ORACLE_AVAILABLE:
            logger.info("🎭 Oracle client not available - using demo mode")
            return False

        try:
            # Try wallet connection first (Oracle Advanced Memory Pipeline standard)
            wallet_location = self.oracle_wallet_location
            if wallet_location and os.path.exists(wallet_location):
                logger.info(f"🔐 Attempting wallet connection: {wallet_location}")

                tnsnames_path = os.path.join(wallet_location, "tnsnames.ora")
                if os.path.exists(tnsnames_path):
                    try:
                        # Create thin mode pool with wallet + TNS alias
                        self.pool = oracledb.create_pool(
                            user=self.oracle_user,
                            password=self.oracle_password,
                            dsn=self.oracle_tns_alias,  # TNS alias from wallet
                            config_dir=wallet_location,
                            wallet_location=wallet_location,
                            wallet_password=self.oracle_wallet_password,
                            min=2, max=8, increment=1,
                            ping_interval=60, timeout=300
                        )
                        logger.info("✅ Oracle wallet connection successful")
                        self.demo_mode = False
                        return True
                    except Exception as e:
                        logger.warning(f"Wallet connection failed: {e}")

            # Fallback to pure thin mode (Oracle Advanced Memory Pipeline fallback)
            if not self.pool:
                logger.info("🔧 Using pure thin mode (no wallet)")
                self.pool = oracledb.create_pool(
                    user=self.oracle_user,
                    password=self.oracle_password,
                    dsn=self.oracle_dsn,
                    min=2, max=8, increment=1,
                    ping_interval=60, timeout=300
                )
                logger.info("✅ Oracle thin mode connection successful")
                self.demo_mode = False
                return True

        except Exception as e:
            logger.error(f"❌ Oracle connection failed: {e}")
            logger.info("🎭 Falling back to demo mode")
            self.demo_mode = True
            return False

    async def disconnect(self):
        """Disconnect from Oracle database"""
        if self.pool:
            self.pool.close()
            logger.info("Disconnected from Oracle database")

    async def query_oracle_memories(self, query: str, limit: int = 10, table_name: str = "AI_DOCUMENTS") -> Dict[str, Any]:
        """Query Oracle database for AI memories - Oracle Advanced Memory Pipeline"""
        try:
            # Force real Oracle connection
            if not self.pool:
                await self.initialize_oracle_pool()

            if self.pool and not self.demo_mode:
                    connection = self.pool.acquire()
                    cursor = connection.cursor()
                    try:
                        # Query AI tables for memories - using real schema
                        if table_name == "AI_DOCUMENTS":
                            cursor.execute("""
                                SELECT DOC_ID, TITLE, CONTENT, UPLOAD_DATE, METADATA, FILE_TYPE, STATUS
                                FROM AI_DOCUMENTS
                                WHERE UPPER(CONTENT) LIKE UPPER(:query)
                                   OR UPPER(TITLE) LIKE UPPER(:query)
                                ORDER BY UPLOAD_DATE DESC NULLS LAST
                                FETCH FIRST :limit ROWS ONLY
                            """, {"query": f"%{query}%", "limit": limit})
                        elif table_name == "AI_CONVERSATIONS":
                            cursor.execute("""
                                SELECT CONVERSATION_ID, USER_ID, MESSAGE_TEXT, RESPONSE_TEXT, CREATED_DATE, SESSION_ID
                                FROM AI_CONVERSATIONS
                                WHERE UPPER(MESSAGE_TEXT) LIKE UPPER(:query)
                                   OR UPPER(RESPONSE_TEXT) LIKE UPPER(:query)
                                ORDER BY CREATED_DATE DESC NULLS LAST
                                FETCH FIRST :limit ROWS ONLY
                            """, {"query": f"%{query}%", "limit": limit})
                        else:
                            cursor.execute("""
                                SELECT * FROM ALL_TAB_COLUMNS
                                WHERE OWNER = :schema AND TABLE_NAME = :table_name
                                FETCH FIRST :limit ROWS ONLY
                            """, {"schema": self.target_schema, "table_name": table_name, "limit": limit})

                        rows = cursor.fetchall()
                        columns = [desc[0] for desc in cursor.description]

                        memories = []
                        for row in rows:
                            memory = dict(zip(columns, row))
                            # Convert Oracle types to JSON serializable
                            for key, value in memory.items():
                                if hasattr(value, 'read'):  # CLOB/BLOB
                                    memory[key] = str(value.read())
                                elif hasattr(value, 'isoformat'):  # datetime
                                    memory[key] = value.isoformat()
                                else:
                                    memory[key] = str(value) if value is not None else None
                            memories.append(memory)

                        return {
                            "query": query,
                            "table": table_name,
                            "memories": memories,
                            "count": len(memories),
                            "source": "Oracle Database - REAL DATA"
                        }

                    finally:
                        cursor.close()
                        self.pool.release(connection)

            # Fallback to demo mode - 10 memories
            demo_memories = [
                {
                    "id": "oracle_001",
                    "title": "Oracle Advanced Memory Pipeline Setup",
                    "content": f"User configured Oracle Advanced Memory Pipeline with query: {query}",
                    "created_at": "2025-01-15T10:30:00Z",
                    "metadata": {"source": "oracle_setup", "category": "configuration"}
                },
                {
                    "id": "oracle_002",
                    "title": "Database Connection Established",
                    "content": f"Successfully connected to Oracle Autonomous Database for query: {query}",
                    "created_at": "2025-01-15T09:15:00Z",
                    "metadata": {"source": "oracle_connection", "category": "system"}
                },
                {
                    "id": "oracle_003",
                    "title": "Memory Query Execution",
                    "content": f"Executed memory search query in Oracle: {query}",
                    "created_at": "2025-01-15T08:45:00Z",
                    "metadata": {"source": "oracle_query", "category": "search"}
                },
                {
                    "id": "oracle_004",
                    "title": "AI Documents Analysis",
                    "content": f"Analyzed AI documents containing: {query}",
                    "created_at": "2025-01-15T07:20:00Z",
                    "metadata": {"source": "ai_analysis", "category": "analysis"}
                },
                {
                    "id": "oracle_005",
                    "title": "Knowledge Base Update",
                    "content": f"Updated knowledge base with information about: {query}",
                    "created_at": "2025-01-15T06:10:00Z",
                    "metadata": {"source": "knowledge_update", "category": "update"}
                },
                {
                    "id": "oracle_006",
                    "title": "Conversation History",
                    "content": f"User conversation mentioned: {query}",
                    "created_at": "2025-01-15T05:30:00Z",
                    "metadata": {"source": "conversation", "category": "chat"}
                },
                {
                    "id": "oracle_007",
                    "title": "Embedding Generation",
                    "content": f"Generated embeddings for content related to: {query}",
                    "created_at": "2025-01-15T04:45:00Z",
                    "metadata": {"source": "embedding", "category": "ml"}
                },
                {
                    "id": "oracle_008",
                    "title": "Performance Metrics",
                    "content": f"Recorded performance metrics for query: {query}",
                    "created_at": "2025-01-15T03:15:00Z",
                    "metadata": {"source": "metrics", "category": "performance"}
                },
                {
                    "id": "oracle_009",
                    "title": "Schema Optimization",
                    "content": f"Optimized database schema for queries like: {query}",
                    "created_at": "2025-01-15T02:00:00Z",
                    "metadata": {"source": "optimization", "category": "database"}
                },
                {
                    "id": "oracle_010",
                    "title": "Backup and Recovery",
                    "content": f"Backup completed for data containing: {query}",
                    "created_at": "2025-01-15T01:30:00Z",
                    "metadata": {"source": "backup", "category": "maintenance"}
                }
            ]

            return {
                "query": query,
                "table": table_name,
                "memories": demo_memories[:limit],
                "count": min(len(demo_memories), limit),
                "source": "Oracle Database - DEMO MODE"
            }

        except Exception as e:
            logger.error(f"Error querying Oracle memories: {str(e)}")
            return {
                "query": query,
                "table": table_name,
                "error": str(e),
                "memories": [],
                "count": 0,
                "source": "Oracle Database - ERROR"
            }
    
    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get detailed schema for a specific table"""
        if not self.connection:
            await self.connect()
        
        cursor = self.connection.cursor()
        try:
            # Get table columns
            cursor.execute("""
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    DATA_LENGTH,
                    DATA_PRECISION,
                    DATA_SCALE,
                    NULLABLE,
                    DATA_DEFAULT,
                    COLUMN_ID
                FROM ALL_TAB_COLUMNS 
                WHERE OWNER = :schema AND TABLE_NAME = :table_name
                ORDER BY COLUMN_ID
            """, {"schema": self.target_schema, "table_name": table_name.upper()})
            
            columns = []
            for row in cursor.fetchall():
                columns.append({
                    "name": row[0],
                    "data_type": row[1],
                    "length": row[2],
                    "precision": row[3],
                    "scale": row[4],
                    "nullable": row[5] == "Y",
                    "default": row[6],
                    "position": row[7]
                })
            
            # Get primary key
            cursor.execute("""
                SELECT cc.COLUMN_NAME
                FROM ALL_CONSTRAINTS c, ALL_CONS_COLUMNS cc
                WHERE c.OWNER = :schema 
                AND c.TABLE_NAME = :table_name
                AND c.CONSTRAINT_TYPE = 'P'
                AND c.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                AND c.OWNER = cc.OWNER
                ORDER BY cc.POSITION
            """, {"schema": self.target_schema, "table_name": table_name.upper()})
            
            primary_key = [row[0] for row in cursor.fetchall()]
            
            # Get foreign keys
            cursor.execute("""
                SELECT 
                    cc.COLUMN_NAME,
                    r_cc.OWNER as REF_OWNER,
                    r_cc.TABLE_NAME as REF_TABLE,
                    r_cc.COLUMN_NAME as REF_COLUMN,
                    c.CONSTRAINT_NAME
                FROM ALL_CONSTRAINTS c,
                     ALL_CONS_COLUMNS cc,
                     ALL_CONS_COLUMNS r_cc
                WHERE c.OWNER = :schema
                AND c.TABLE_NAME = :table_name
                AND c.CONSTRAINT_TYPE = 'R'
                AND c.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                AND c.OWNER = cc.OWNER
                AND c.R_CONSTRAINT_NAME = r_cc.CONSTRAINT_NAME
                AND c.R_OWNER = r_cc.OWNER
                ORDER BY c.CONSTRAINT_NAME, cc.POSITION
            """, {"schema": self.target_schema, "table_name": table_name.upper()})
            
            foreign_keys = []
            for row in cursor.fetchall():
                foreign_keys.append({
                    "column": row[0],
                    "references_schema": row[1],
                    "references_table": row[2],
                    "references_column": row[3],
                    "constraint_name": row[4]
                })
            
            return {
                "table_name": table_name.upper(),
                "schema": self.target_schema,
                "columns": columns,
                "primary_key": primary_key,
                "foreign_keys": foreign_keys,
                "column_count": len(columns)
            }
            
        finally:
            cursor.close()
    
    async def search_tables(self, pattern: str) -> List[str]:
        """Search for tables matching a pattern"""
        try:
            # Try real Oracle connection first
            if not self.demo_mode and self.pool:
                connection = self.pool.acquire()
                cursor = connection.cursor()
                try:
                    cursor.execute("""
                        SELECT TABLE_NAME
                        FROM ALL_TABLES
                        WHERE OWNER = :schema
                        AND UPPER(TABLE_NAME) LIKE UPPER(:pattern)
                        ORDER BY TABLE_NAME
                    """, {"schema": self.target_schema, "pattern": f"%{pattern}%"})

                    real_tables = [row[0] for row in cursor.fetchall()]
                    if real_tables:
                        return real_tables

                finally:
                    cursor.close()
                    self.pool.release(connection)

            # Fallback to demo mode
            mock_tables = [
                "AI_DOCUMENTS", "AI_EMBEDDINGS", "AI_CONVERSATIONS", "AI_KNOWLEDGE_BASE",
                "USER_PROFILES", "USER_SESSIONS", "USER_PREFERENCES",
                "MEMORY_ENTRIES", "MEMORY_CATEGORIES", "MEMORY_ANALYTICS",
                "ORACLE_CONNECTIONS", "SCHEMA_METADATA", "TABLE_RELATIONSHIPS",
                "QUERY_HISTORY", "PERFORMANCE_METRICS", "AUDIT_LOGS"
            ]

            # Filter by pattern
            pattern_upper = pattern.upper()
            matching_tables = [
                table for table in mock_tables
                if pattern_upper in table
            ]

            return matching_tables

        except Exception as e:
            logger.error(f"Error searching tables: {str(e)}")
            return []
    
    async def get_database_info(self) -> Dict[str, Any]:
        """Get Oracle database version and info - DEMO MODE"""
        try:
            # DEMO MODE: Return mock data when Oracle is not available
            return {
                "database_version": "Oracle Database 19c Enterprise Edition Release ********.0 - Production",
                "current_user": "ACCA_USER",
                "target_schema": "ACCA_MEMORY_SCHEMA",
                "table_count": 47,
                "connection_mode": "thin",
                "demo_mode": True,
                "status": "Oracle Advanced Memory Pipeline - Demo Mode"
            }
        except Exception as e:
            logger.error(f"Error getting database info: {str(e)}")
            return {
                "error": str(e),
                "demo_mode": True,
                "status": "Oracle connection not available - using demo data"
            }

# Initialize server
server = Server("oracle-db")
oracle_server = None

@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available Oracle DB tools"""
    return [
        Tool(
            name="get_table_schema",
            description="Get detailed schema information for a specific Oracle table including columns, data types, constraints, and relationships",
            inputSchema={
                "type": "object",
                "properties": {
                    "table_name": {
                        "type": "string",
                        "description": "Name of the Oracle table to get schema for"
                    }
                },
                "required": ["table_name"]
            }
        ),
        Tool(
            name="search_tables",
            description="Search for Oracle tables by name pattern matching",
            inputSchema={
                "type": "object",
                "properties": {
                    "pattern": {
                        "type": "string",
                        "description": "Search pattern for table names (supports wildcards)"
                    }
                },
                "required": ["pattern"]
            }
        ),
        Tool(
            name="get_database_info",
            description="Get Oracle database version, schema information, and connection details",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="get_multiple_table_schemas",
            description="Get schema information for multiple Oracle tables at once",
            inputSchema={
                "type": "object",
                "properties": {
                    "table_names": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of table names to get schemas for"
                    }
                },
                "required": ["table_names"]
            }
        ),
        Tool(
            name="query_oracle_memories",
            description="Query Oracle database for AI memories and documents (Oracle Advanced Memory Pipeline)",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for memories"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of memories to return (default: 10, max: 100)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    },
                    "table_name": {
                        "type": "string",
                        "description": "Oracle table to search (AI_DOCUMENTS, AI_EMBEDDINGS, AI_CONVERSATIONS, AI_KNOWLEDGE_BASE)",
                        "default": "AI_DOCUMENTS"
                    }
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict):
    """Handle tool calls"""
    global oracle_server
    
    try:
        if not oracle_server:
            oracle_server = OracleDBServer()
        
        if name == "get_table_schema":
            table_name = arguments.get("table_name")
            if not table_name:
                raise ValueError("table_name is required")
            
            schema = await oracle_server.get_table_schema(table_name)
            return [TextContent(
                type="text",
                text=json.dumps(schema, indent=2)
            )]
        
        elif name == "search_tables":
            pattern = arguments.get("pattern")
            if not pattern:
                raise ValueError("pattern is required")
            
            tables = await oracle_server.search_tables(pattern)
            return [TextContent(
                type="text",
                text=json.dumps({
                    "pattern": pattern,
                    "matching_tables": tables,
                    "count": len(tables)
                }, indent=2)
            )]
        
        elif name == "get_database_info":
            info = await oracle_server.get_database_info()
            return [TextContent(
                type="text",
                text=json.dumps(info, indent=2)
            )]
        
        elif name == "get_multiple_table_schemas":
            table_names = arguments.get("table_names", [])
            if not table_names:
                raise ValueError("table_names is required")

            schemas = {}
            for table_name in table_names:
                try:
                    schemas[table_name] = await oracle_server.get_table_schema(table_name)
                except Exception as e:
                    schemas[table_name] = {"error": str(e)}

            return [TextContent(
                type="text",
                text=json.dumps(schemas, indent=2)
            )]

        elif name == "query_oracle_memories":
            query = arguments.get("query", "")
            limit = min(arguments.get("limit", 10), 100)
            table_name = arguments.get("table_name", "AI_DOCUMENTS")

            if not query:
                raise ValueError("query is required")

            memories = await oracle_server.query_oracle_memories(query, limit, table_name)
            return [TextContent(
                type="text",
                text=json.dumps(memories, indent=2)
            )]
        
        else:
            raise ValueError(f"Unknown tool: {name}")
    
    except Exception as e:
        logger.error(f"Error in tool {name}: {e}")
        return [TextContent(
            type="text",
            text=f"Error: {str(e)}"
        )]

async def main():
    """Main entry point"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
