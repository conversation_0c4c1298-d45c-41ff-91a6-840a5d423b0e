#!/usr/bin/env python3
"""
Configure Open WebUI to use Perplexica tools via MCPO
"""

import requests
import json
import time
import sys

def check_mcpo_server():
    """Check if MCPO server is running and has Perplexica tools"""
    try:
        # Check MCPO health (try docs endpoint since /health may not exist)
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ MCPO server not responding")
            return False
        
        # Check Perplexica tools
        response = requests.post(
            "http://localhost:8000/perplexica/perplexica_health_check",
            headers={"Authorization": "Bearer acca-enhanced-rag-mcp-key-2025"},
            json={},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ MCPO server with Perplexica tools is running")
            return True
        else:
            print(f"⚠️ MCPO server running but Perplexica tools may have issues: {response.status_code}")
            return True  # Still try to configure
            
    except Exception as e:
        print(f"❌ Cannot connect to MCPO server: {e}")
        return False

def test_perplexica_tools():
    """Test Perplexica tools functionality"""
    print("\n🧪 Testing Perplexica tools...")
    
    tools_to_test = [
        ("perplexica_quick_search", {"query": "test search"}),
        ("perplexica_search", {"query": "artificial intelligence", "focusMode": "webSearch"}),
        ("perplexica_academic_search", {"query": "machine learning research"}),
    ]
    
    for tool_name, payload in tools_to_test:
        try:
            response = requests.post(
                f"http://localhost:8000/perplexica/{tool_name}",
                headers={
                    "Authorization": "Bearer acca-enhanced-rag-mcp-key-2025",
                    "Content-Type": "application/json"
                },
                json=payload,
                timeout=15
            )
            
            if response.status_code == 200:
                print(f"  ✅ {tool_name}: Working")
            else:
                print(f"  ❌ {tool_name}: Error {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {tool_name}: Exception - {e}")

def create_openwebui_tool_config():
    """Create Open WebUI tool configuration for Perplexica"""
    
    config = {
        "name": "Perplexica AI Search Tools",
        "description": "Advanced AI-powered search tools with real-time web access, academic research, and intelligent analysis",
        "url": "http://localhost:8000",
        "api_key": "acca-enhanced-rag-mcp-key-2025",
        "enabled": True,
        "tools": [
            {
                "name": "perplexica_quick_search",
                "description": "⚡ Quick AI search for immediate comprehensive information",
                "endpoint": "/perplexica/perplexica_quick_search",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query for quick information"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "perplexica_search",
                "description": "🔍 Advanced web search with AI analysis and source citations",
                "endpoint": "/perplexica/perplexica_search",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "focusMode": {
                            "type": "string",
                            "enum": ["webSearch", "academicSearch", "youtubeSearch", "redditSearch"],
                            "default": "webSearch",
                            "description": "Search focus mode"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "perplexica_academic_search",
                "description": "🎓 Academic research search for scholarly content",
                "endpoint": "/perplexica/perplexica_academic_search",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Academic research query"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "perplexica_youtube_search",
                "description": "📺 YouTube video search with AI summaries",
                "endpoint": "/perplexica/perplexica_youtube_search",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Video search query"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "perplexica_reddit_search",
                "description": "💬 Reddit discussion search for community insights",
                "endpoint": "/perplexica/perplexica_reddit_search",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Topic to search in Reddit"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "perplexica_chat",
                "description": "🤖 Interactive AI chat with search capabilities",
                "endpoint": "/perplexica/perplexica_chat",
                "method": "POST",
                "schema": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "description": "Message to chat with AI"},
                        "focusMode": {
                            "type": "string",
                            "enum": ["webSearch", "academicSearch", "writingAssistant"],
                            "default": "webSearch"
                        }
                    },
                    "required": ["message"]
                }
            }
        ]
    }
    
    # Save configuration
    with open("perplexica_openwebui_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Created Open WebUI tool configuration: perplexica_openwebui_config.json")
    return config

def print_integration_instructions():
    """Print instructions for integrating with Open WebUI"""
    
    print("\n" + "="*60)
    print("🚀 PERPLEXICA TOOLS INTEGRATION GUIDE")
    print("="*60)
    
    print("\n📋 OPTION 1: Manual Configuration in Open WebUI")
    print("-" * 50)
    print("1. Open your Open WebUI interface: http://localhost:3000")
    print("2. Go to Settings → Tools → Add Tool Server")
    print("3. Configure as follows:")
    print("   • Name: Perplexica AI Search Tools")
    print("   • URL: http://localhost:8000")
    print("   • API Key: acca-enhanced-rag-mcp-key-2025")
    print("   • Click 'Save'")
    
    print("\n📋 OPTION 2: OpenAPI Import")
    print("-" * 50)
    print("1. Go to Settings → Tools → Import OpenAPI")
    print("2. Enter URL: http://localhost:8000/openapi.json")
    print("3. Or use local file: perplexica_openapi_spec.json")
    
    print("\n📋 OPTION 3: Function Calling (Recommended)")
    print("-" * 50)
    print("1. Go to Settings → Tools → Function Calling")
    print("2. Enable Function Calling")
    print("3. Set Endpoint: http://localhost:8000")
    print("4. Set API Key: acca-enhanced-rag-mcp-key-2025")
    
    print("\n🎯 AVAILABLE TOOLS:")
    print("-" * 50)
    tools = [
        "⚡ perplexica_quick_search - Fast comprehensive search",
        "🔍 perplexica_search - Advanced web search with focus modes",
        "🎓 perplexica_academic_search - Scholarly research search",
        "📺 perplexica_youtube_search - Video content search",
        "💬 perplexica_reddit_search - Community discussion search",
        "🤖 perplexica_chat - Interactive AI chat with search"
    ]
    
    for tool in tools:
        print(f"   {tool}")
    
    print("\n✨ USAGE EXAMPLES:")
    print("-" * 50)
    print("• 'Search for the latest AI developments in 2025'")
    print("• 'Find academic papers about machine learning in healthcare'")
    print("• 'What are people saying on Reddit about Python vs JavaScript?'")
    print("• 'Show me YouTube tutorials for React development'")
    
    print("\n🔗 ENDPOINTS:")
    print("-" * 50)
    print("• MCPO Server: http://localhost:8000")
    print("• OpenAPI Spec: http://localhost:8000/openapi.json")
    print("• Swagger UI: http://localhost:8000/docs")
    
    print("\n" + "="*60)

def main():
    print("🚀 Perplexica Tools Configuration for Open WebUI")
    print("=" * 60)
    
    # Check MCPO server
    if not check_mcpo_server():
        print("\n❌ Please start MCPO server first!")
        print("Run: docker restart mcpo-servers-8000-standard")
        sys.exit(1)
    
    # Test tools
    test_perplexica_tools()
    
    # Create configuration
    create_openwebui_tool_config()
    
    # Print instructions
    print_integration_instructions()
    
    print("\n🎉 Configuration completed! Your Perplexica tools are ready to use in Open WebUI.")

if __name__ == "__main__":
    main()
