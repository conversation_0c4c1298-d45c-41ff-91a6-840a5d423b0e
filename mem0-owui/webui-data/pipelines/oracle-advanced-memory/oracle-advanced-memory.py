"""
title: oracle-advanced-memory
author: AccA System
date: 2025-08-14
version: 2.1
license: MIT
description: Oracle Advanced Memory System with wallet connection and Mem0 coordination
requirements: oracledb, pydantic, requests, asyncio
"""

import os
import asyncio
import json
import hashlib
import logging
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import requests
import uuid
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OracleThinModeMemoryClient:
    """Pure Oracle thin mode memory client - no wallet dependencies"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.pool = None
        self.initialized = False
        
    async def initialize(self):
        """Initialize Oracle thin mode connection pool with wallet fallback"""
        if self.initialized:
            return
            
        try:
            import oracledb
            import os
            
            logger.info("🔧 Initializing Oracle thin mode connection...")

            # Get wallet configuration
            wallet_location = self.config.get('oracle_wallet_location')
            tns_alias = self.config.get('oracle_tns_alias')

            logger.info(f"🔍 DEBUG: wallet_location = {wallet_location}")
            logger.info(f"🔍 DEBUG: tns_alias = {tns_alias}")
            logger.info(f"🔍 DEBUG: config keys = {list(self.config.keys())}")
            
            # Try wallet first if configured (no thick client required)
            if wallet_location and tns_alias:
                logger.info(f"📁 Checking wallet: {wallet_location}")
                logger.info(f"🔍 DEBUG: wallet_location exists = {os.path.exists(wallet_location)}")

                # Check if wallet files exist
                tnsnames_path = os.path.join(wallet_location, 'tnsnames.ora')
                logger.info(f"🔍 DEBUG: tnsnames_path = {tnsnames_path}")
                logger.info(f"🔍 DEBUG: tnsnames exists = {os.path.exists(tnsnames_path)}")

                if os.path.exists(tnsnames_path):
                    try:
                        logger.info("🔐 Attempting wallet connection (thin mode)...")
                        # Create thin mode pool with wallet + TNS alias
                        self.pool = oracledb.create_pool(
                            user=self.config['oracle_user'],
                            password=self.config['oracle_password'],
                            dsn=tns_alias,  # TNS alias from wallet tnsnames.ora
                            config_dir=wallet_location,
                            wallet_location=wallet_location,
                            wallet_password=self.config.get('oracle_wallet_password', self.config['oracle_password']),
                            min=2, max=8, increment=1,
                            ping_interval=60, timeout=300
                        )
                        logger.info("✅ Oracle thin mode pool with wallet created successfully")
                    except Exception as wallet_error:
                        logger.error(f"❌ Wallet connection failed: {wallet_error}")
                        logger.error(f"❌ Error type: {type(wallet_error).__name__}")
                        import traceback
                        logger.error(f"❌ Full traceback: {traceback.format_exc()}")
                        logger.info("🔄 Falling back to DSN connection...")
                        self.pool = None
                else:
                    logger.warning(f"⚠️ Wallet files not found at: {wallet_location}")
                    logger.info("🔄 Falling back to DSN connection...")
            
            # Fallback to pure thin mode if wallet failed or not configured
            if not self.pool:
                logger.error("❌ WALLET CONNECTION FAILED - FALLING BACK TO DSN")
                logger.info("🔧 Using pure thin mode (no wallet) - UPDATED VERSION")
                # Use full DSN string for pure thin mode
                dsn_string = self.config.get('oracle_dsn') or "(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_raxcblotwgf3qzgh_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))"
                
                self.pool = oracledb.create_pool(
                    user=self.config['oracle_user'],
                    password=self.config['oracle_password'],
                    dsn=dsn_string,
                    min=2, max=8, increment=1,
                    ping_interval=60, timeout=300
                )
                logger.info("✅ Oracle pure thin mode pool created successfully")
            
            # Test connection
            await self._test_connection()
            
            # Create tables if needed
            await self._ensure_tables_exist()
            
            self.initialized = True
            logger.info("✅ Oracle thin mode memory client initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Oracle thin mode client: {e}")
            logger.error(f"❌ Error details: {str(e)}")
            logger.warning("⚠️ Continuing without Oracle memory functionality")
            # Don't raise - continue without Oracle memory
            self.initialized = False
            self.pool = None
    
    async def _test_connection(self):
        """Test Oracle connection"""
        try:
            conn = self.pool.acquire()
            cursor = conn.cursor()
            cursor.execute("SELECT 'Oracle Thin Mode OK' FROM DUAL")
            result = cursor.fetchone()
            
            if result and result[0] == 'Oracle Thin Mode OK':
                logger.info("✅ Oracle thin mode connection test successful")
            else:
                raise Exception("Connection test failed")
                
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Oracle connection test failed: {e}")
            raise
    
    async def _ensure_tables_exist(self):
        """Create required tables if they don't exist"""
        try:
            conn = self.pool.acquire()
            cursor = conn.cursor()
            
            # Create USER_MEMORY table
            try:
                cursor.execute("""
                CREATE TABLE USER_MEMORY (
                    memory_id VARCHAR2(100) PRIMARY KEY,
                    user_id VARCHAR2(100) NOT NULL,
                    memory_type VARCHAR2(50) DEFAULT 'conversation',
                    memory_content CLOB NOT NULL,
                    confidence_score NUMBER(3,2) DEFAULT 0.8,
                    session_id VARCHAR2(100),
                    created_date DATE DEFAULT SYSDATE,
                    status VARCHAR2(20) DEFAULT 'ACTIVE'
                )
                """)
                logger.info("✅ USER_MEMORY table created")
            except Exception as e:
                if "name is already used" in str(e).lower():
                    logger.info("ℹ️ USER_MEMORY table already exists")
                else:
                    logger.warning(f"⚠️ Error creating USER_MEMORY table: {e}")
            
            # Create USER_PATTERNS table
            try:
                cursor.execute("""
                CREATE TABLE USER_PATTERNS (
                    pattern_id VARCHAR2(100) PRIMARY KEY,
                    user_id VARCHAR2(100) NOT NULL,
                    pattern_type VARCHAR2(50) NOT NULL,
                    pattern_data CLOB,
                    strength NUMBER(3,2) DEFAULT 0.5,
                    created_date DATE DEFAULT SYSDATE
                )
                """)
                logger.info("✅ USER_PATTERNS table created")
            except Exception as e:
                if "name is already used" in str(e).lower():
                    logger.info("ℹ️ USER_PATTERNS table already exists")
                else:
                    logger.warning(f"⚠️ Error creating USER_PATTERNS table: {e}")
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error ensuring tables exist: {e}")
    
    async def search_memories(self, user_id: str, query: str, limit: int = 5) -> List[Dict]:
        """Search Oracle memories using thin mode"""
        logger.info(f"🔍 DEBUG: Oracle search called - User: {user_id}, Query: {query[:50]}...")

        # Check if this is a conversation history query
        history_keywords = ['nói chuyện gì', 'nói gì', 'chuyện gì', 'conversation', 'history', 'chat gì']
        is_history_query = any(keyword in query.lower() for keyword in history_keywords)
        logger.info(f"🔍 DEBUG: History query detected: {is_history_query}")

        logger.info(f"🔍 DEBUG: Oracle initialized status: {self.initialized}")
        if not self.initialized:
            logger.info(f"🔍 DEBUG: Oracle not initialized, initializing...")
            await self.initialize()
        else:
            logger.info(f"🔍 DEBUG: Oracle already initialized")

        try:
            logger.info(f"🔍 DEBUG: Pool status: {self.pool}")
            logger.info(f"🔍 DEBUG: Acquiring Oracle connection...")
            conn = self.pool.acquire()
            cursor = conn.cursor()
            logger.info(f"🔍 DEBUG: Oracle connection acquired successfully")
            
            # Choose search strategy based on query type
            if is_history_query:
                # For conversation history queries, get recent memories
                sql = """
                SELECT memory_id, memory_content, confidence_score, created_date, memory_type
                FROM USER_MEMORY
                WHERE user_id = :user_id
                AND status = 'ACTIVE'
                ORDER BY created_date DESC
                FETCH FIRST :limit ROWS ONLY
                """
                query_params = {
                    'user_id': user_id,
                    'limit': limit * 2  # Get more for history queries
                }
                logger.info(f"🔍 DEBUG: Using history query strategy")
            else:
                # Regular semantic search
                sql = """
                SELECT memory_id, memory_content, confidence_score, created_date, memory_type
                FROM USER_MEMORY
                WHERE user_id = :user_id
                AND status = 'ACTIVE'
                AND (
                    UPPER(memory_content) LIKE UPPER(:query1)
                    OR UPPER(memory_content) LIKE UPPER(:query2)
                )
                ORDER BY confidence_score DESC, created_date DESC
                FETCH FIRST :limit ROWS ONLY
                """
                query_pattern1 = f"%{query}%"
                query_pattern2 = f"%{' '.join(query.split()[:3])}%"
                query_params = {
                    'user_id': user_id,
                    'query1': query_pattern1,
                    'query2': query_pattern2,
                    'limit': limit
                }
                logger.info(f"🔍 DEBUG: Using semantic search strategy")

            if not is_history_query:
                logger.info(f"🔍 DEBUG: Search patterns - P1: {query_pattern1[:50]}..., P2: {query_pattern2}")

            cursor.execute(sql, query_params)

            logger.info(f"🔍 DEBUG: SQL executed successfully")
            
            raw_results = cursor.fetchall()
            logger.info(f"🔍 DEBUG: Raw SQL results: {len(raw_results)} rows")

            results = []
            for i, row in enumerate(raw_results):
                try:
                    # Handle CLOB content properly
                    content = row[1].read() if hasattr(row[1], 'read') else str(row[1])
                    results.append({
                        'memory_id': row[0],
                        'content': content,
                        'confidence': float(row[2]),
                        'created_date': row[3].isoformat() if row[3] else '',
                        'memory_type': row[4],
                        'source': 'oracle'
                    })
                    logger.info(f"🔍 DEBUG: Processed row {i+1}: {row[0]}")
                except Exception as e:
                    logger.error(f"❌ Error processing row {i+1}: {e}")

            cursor.close()
            self.pool.release(conn)

            logger.info(f"🏛️ Found {len(results)} Oracle memories for user {user_id}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error searching Oracle memories: {e}")
            logger.error(f"❌ Search details - User: {user_id}, Query: {query[:50]}...")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            return []
    
    async def store_memory(self, user_id: str, content: str, memory_type: str = "conversation",
                          session_id: str = None, confidence: float = 0.8) -> str:
        """Store memory in Oracle using thin mode"""
        if not self.initialized:
            await self.initialize()
            
        try:
            conn = self.pool.acquire()
            cursor = conn.cursor()
            
            memory_id = hashlib.md5(
                f"{user_id}_{content}_{datetime.now().isoformat()}".encode()
            ).hexdigest()
            
            sql = """
            INSERT INTO USER_MEMORY (
                memory_id, user_id, memory_type, memory_content,
                confidence_score, session_id, created_date, status
            ) VALUES (
                :memory_id, :user_id, :memory_type, :content,
                :confidence, :session_id, SYSDATE, 'ACTIVE'
            )
            """
            
            cursor.execute(sql, {
                'memory_id': memory_id,
                'user_id': user_id,
                'memory_type': memory_type,
                'content': content,
                'confidence': confidence,
                'session_id': session_id
            })
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"💾 Stored memory {memory_id[:8]}... for user {user_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"❌ Error storing Oracle memory: {e}")
            return ""
    
    async def get_user_patterns(self, user_id: str) -> Dict:
        """Get user patterns from Oracle using thin mode"""
        if not self.initialized:
            await self.initialize()
            
        try:
            conn = self.pool.acquire()
            cursor = conn.cursor()
            
            sql = """
            SELECT pattern_type, pattern_data, strength
            FROM USER_PATTERNS
            WHERE user_id = :user_id
            AND strength > 0.3
            ORDER BY strength DESC
            """
            
            cursor.execute(sql, {'user_id': user_id})
            
            patterns = {}
            for row in cursor.fetchall():
                patterns[row[0]] = {
                    'data': json.loads(row[1]) if row[1] else {},
                    'strength': float(row[2])
                }
            
            cursor.close()
            conn.close()
            
            logger.info(f"📊 Retrieved {len(patterns)} patterns for user {user_id}")
            return patterns
            
        except Exception as e:
            logger.error(f"❌ Error getting user patterns: {e}")
            return {}

class Mem0Coordinator:
    """Coordinator for existing Mem0 system integration"""
    
    def __init__(
        self,
        qdrant_host: str,
        qdrant_port: str,
        collection_name: str,
        embedding_model: Optional[str] = None,
        embedding_dimensions: Optional[int] = None,
        embedding_provider: Optional[str] = None,
        use_openai_compatible: Optional[bool] = None,
        gemini_api_key: Optional[str] = None,
        llm_provider: Optional[str] = None,
        llm_model: Optional[str] = None,
    ):
        self.qdrant_host = qdrant_host
        self.qdrant_port = qdrant_port
        self.collection_name = collection_name
        self.base_url = f"http://{qdrant_host}:{qdrant_port}"

        logger.info(f"🔍 DEBUG: Mem0Coordinator initialized with collection: {collection_name}")
        logger.info(f"🔍 DEBUG: Qdrant URL: {self.base_url}")
        # Embedding/LLM configuration (for future integration)
        self.embedding_model = embedding_model
        self.embedding_dimensions = embedding_dimensions
        self.embedding_provider = embedding_provider
        self.use_openai_compatible = use_openai_compatible
        self.gemini_api_key = gemini_api_key
        self.llm_provider = llm_provider
        self.llm_model = llm_model
    
    def _embed_text(self, text: str) -> Optional[List[float]]:
        """Create embedding for text using configured provider."""
        logger.info(f"🔍 DEBUG: Embedding text - Provider: {self.embedding_provider}, API key exists: {bool(self.gemini_api_key)}")
        try:
            # Prefer Gemini text-embedding-004 when api key provided
            if (self.embedding_provider and self.embedding_provider.lower() == 'gemini') or (
                self.embedding_model and self.embedding_model.startswith('text-embedding')
            ):
                if self.gemini_api_key:
                    endpoint = "https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent"
                    headers = {"Content-Type": "application/json"}
                    params = {"key": self.gemini_api_key}
                    body = {
                        "model": "text-embedding-004",
                        "content": {"parts": [{"text": text}]}
                    }
                    resp = requests.post(endpoint, params=params, headers=headers, data=json.dumps(body), timeout=10)
                    resp.raise_for_status()
                    data = resp.json()
                    vec = data.get("embedding", {}).get("value") or data.get("embedding", {}).get("values")
                    if vec and isinstance(vec, list):
                        return vec
            # Add other providers here if needed (openai-compatible, etc.)
        except Exception as e:
            logger.error(f"❌ Embedding failed: {e}")
            logger.error(f"❌ Embedding details - Text length: {len(text)}, Provider: {self.embedding_provider}")
        return None

    def _qdrant_url(self, path: str) -> str:
        return f"{self.base_url}{path}"

    def _extract_payload_text(self, payload: Dict[str, Any]) -> Optional[str]:
        for key in ["text", "content", "body", "message", "chunk", "data"]:
            val = payload.get(key)
            if isinstance(val, str) and val.strip():
                return val
        # Fallback: join string-like fields
        parts = []
        for k, v in payload.items():
            if isinstance(v, str) and len(v) > 0:
                parts.append(v)
        return "; ".join(parts)[:500] if parts else None

    async def search_memories(self, user_id: str, query: str, limit: int = 3) -> List[Dict]:
        """Search existing Mem0 memories in Qdrant using vector similarity."""
        logger.info(f"🔍 DEBUG: Mem0 search called - Collection: {self.collection_name}, User: {user_id}, Query: {query[:50]}...")
        try:
            vec = self._embed_text(query)
            if not vec:
                return []
            search_body: Dict[str, Any] = {
                "vector": vec,
                "limit": limit,
                "with_payload": True,
                "with_vector": False
            }
            # Filter by user if configured
            if hasattr(self, 'mem0_filter_by_user') and self.mem0_filter_by_user:
                search_body["filter"] = {
                    "must": [
                        {"key": getattr(self, 'mem0_user_id_field', 'user_id'), "match": {"value": user_id}}
                    ]
                }
            url = self._qdrant_url(f"/collections/{self.collection_name}/points/search")
            resp = requests.post(url, json=search_body, timeout=10)
            resp.raise_for_status()
            data = resp.json()
            results = []
            for pt in data.get("result", []) or []:
                payload = pt.get("payload", {}) or {}
                content = self._extract_payload_text(payload)
                if not content:
                    continue
                results.append({
                    "content": content,
                    "score": pt.get("score"),
                    "source": "mem0",
                    "payload": payload
                })
            return results
        except Exception as e:
            logger.error(f"❌ Error searching Mem0 memories: {e}")
            return []
    
    async def store_conversation(self, user_id: str, user_message: str, ai_response: str):
        """Store conversation in Mem0 (Qdrant) with basic payload and vector."""
        logger.info(f"🔍 DEBUG: Mem0 store called - Collection: {self.collection_name}, User: {user_id}")
        try:
            points = []
            for role, text in [("user", user_message), ("assistant", ai_response)]:
                logger.info(f"🔍 DEBUG: Creating embedding for {role} - Text: {text[:50]}...")
                vec = self._embed_text(text)
                if not vec:
                    logger.warning(f"⚠️ No embedding for {role} message")
                    continue
                logger.info(f"🔍 DEBUG: Embedding created for {role} - Vector length: {len(vec)}")
                points.append({
                    "id": str(uuid.uuid4()),
                    "vector": vec,
                    "payload": {
                        "user_id": user_id,
                        "role": role,
                        "text": text,
                        "source": "oracle_hybrid_pipeline",
                    }
                })
            if not points:
                logger.info(f"🔍 DEBUG: No points to store in Mem0")
                return

            url = self._qdrant_url(f"/collections/{self.collection_name}/points")
            logger.info(f"🔍 DEBUG: Storing {len(points)} points to Qdrant URL: {url}")
            resp = requests.put(url, json={"points": points}, timeout=10)
            resp.raise_for_status()
            logger.info(f"🔍 DEBUG: Successfully stored {len(points)} points to Mem0")
        except Exception as e:
            logger.error(f"❌ Error storing to Mem0: {e}")

class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]  # Apply to all models
        priority: int = 0
        
        # Oracle Configuration
        oracle_user: str = Field(
            default=os.getenv("ORACLE_USER", "ADMIN"),
            description="Oracle database user"
        )
        oracle_password: str = Field(
            default=os.getenv("ORACLE_PASSWORD", "Twilv0zera@123"),
            description="Oracle database password"
        )
        oracle_dsn: str = Field(
            default=os.getenv("ORACLE_DSN", ""),
            description="Oracle database DSN"
        )
        oracle_wallet_location: str = Field(
            default=os.getenv("ORACLE_WALLET_LOCATION", "/app/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"),
            description="Oracle wallet location for mTLS authentication"
        )
        oracle_tns_alias: str = Field(
            default=os.getenv("ORACLE_TNS_ALIAS", "swiv8hv5y96iwo2t_high"),
            description="TNS alias from tnsnames.ora (e.g., swiv8hv5y96iwo2t_high)"
        )
        oracle_wallet_password: str = Field(
            default=os.getenv("ORACLE_WALLET_PASSWORD", "Twilv0zera@123"),
            description="Oracle wallet password for mTLS authentication"
        )

        # Memory System Configuration
        enable_oracle_memory: bool = Field(
            default=True,
            description="Enable Oracle advanced memory system"
        )
        enable_mem0_coordination: bool = Field(
            default=True,
            description="Enable coordination with existing Mem0 system"
        )
        enable_pattern_learning: bool = Field(
            default=True,
            description="Enable user pattern learning and personalization"
        )
        
        # Mem0 Integration Settings
        qdrant_host: str = Field(
            default="qdrant",
            description="Qdrant host for Mem0 integration"
        )
        qdrant_port: str = Field(
            default="6333",
            description="Qdrant port for Mem0 integration"
        )
        mem0_collection: str = Field(
            default=os.getenv("MEM0_COLLECTION", "mem0_gemini_gemi_768"),
            description="Mem0 collection name"
        )
        # Embedding/LLM selection (for Mem0 and coordination)
        embedding_dimensions: int = Field(
            default=int(os.getenv("EMBEDDING_DIMENSIONS", "768")),
            description="Embedding vector dimensions for Mem0 collection"
        )
        embedding_model: str = Field(
            default=os.getenv("EMBEDDING_MODEL", "text-embedding-004"),
            description="Embedding model name used for Mem0"
        )
        embedding_provider: str = Field(
            default=os.getenv("EMBEDDING_PROVIDER", "gemini"),
            description="Embedding provider (e.g., openai-compatible, gemini)"
        )
        use_openai_compatible: bool = Field(
            default=bool(int(os.getenv("USE_OPENAI_COMPATIBLE", "1"))),
            description="Whether to use OpenAI-compatible endpoints for embeddings"
        )
        gemini_api_key: Optional[str] = Field(
            default=os.getenv("GEMINI_API_KEY", None),
            description="API key for Gemini when used as embedding provider"
        )
        llm_provider: str = Field(
            default=os.getenv("LLM_PROVIDER", ""),
            description="LLM provider hint for coordination (optional)"
        )
        llm_model: str = Field(
            default=os.getenv("LLM_MODEL", ""),
            description="LLM model hint for coordination (optional)"
        )
        # Mem0 search/store behavior
        mem0_top_k: int = Field(
            default=int(os.getenv("MEM0_TOP_K", "3")),
            description="Top K results to fetch from Mem0"
        )
        mem0_filter_by_user: bool = Field(
            default=bool(int(os.getenv("MEM0_FILTER_BY_USER", "1"))),
            description="Filter Mem0 search results by current user id"
        )
        mem0_user_id_field: str = Field(
            default=os.getenv("MEM0_USER_ID_FIELD", "user_id"),
            description="Payload field name in Mem0/Qdrant that stores the user id"
        )
        
        # Memory Behavior
        max_oracle_memories: int = Field(
            default=3,
            description="Maximum Oracle memories to inject into context"
        )
        max_mem0_memories: int = Field(
            default=2,
            description="Maximum Mem0 memories to inject into context"
        )
        memory_relevance_threshold: float = Field(
            default=0.4,
            description="Minimum relevance threshold for memory inclusion"
        )
        auto_store_conversations: bool = Field(
            default=True,
            description="Automatically store conversations in memory"
        )
        enable_memory_dedup: bool = Field(
            default=True,
            description="Enable deduplication between Oracle and Mem0 context"
        )
        max_total_memories_injected: int = Field(
            default=6,
            description="Cap total number of memories injected (Oracle+Mem0)"
        )
        
        # Performance Settings
        enable_debug_logging: bool = Field(
            default=True,
            description="Enable detailed debug logging"
        )
        memory_search_timeout: int = Field(
            default=2,
            description="Memory search timeout in seconds"
        )
        
        # User Settings
        default_user_id: str = Field(
            default="default_user",
            description="Default user ID when user info is not available"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        
        # Memory clients
        self.oracle_client = None
        self.mem0_coordinator = None
        
        # Session tracking
        self.current_session_id = None
        self.conversation_turn = 0
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'oracle_searches': 0,
            'mem0_searches': 0,
            'memories_injected': 0,
            'patterns_applied': 0,
            'avg_response_time': 0.0
        }

    def pipelines(self):
        """Return list of pipelines (required by pipeline loader)"""
        return [
            {
                "id": "oracle-advanced-memory",
                "name": "Oracle Advanced Memory",
                "type": self.type
            }
        ]

    async def on_startup(self):
        """Initialize the pipeline"""
        logger.info("🚀 Starting Oracle Advanced Memory Pipeline (Thin Mode)...")
        
        try:
            # Initialize Oracle client
            if self.valves.enable_oracle_memory:
                oracle_config = {
                    'oracle_user': self.valves.oracle_user,
                    'oracle_password': self.valves.oracle_password,
                    'oracle_dsn': self.valves.oracle_dsn,
                    'oracle_wallet_location': self.valves.oracle_wallet_location,
                    'oracle_tns_alias': self.valves.oracle_tns_alias,
                    'oracle_wallet_password': self.valves.oracle_wallet_password
                }
                
                self.oracle_client = OracleThinModeMemoryClient(oracle_config)
                await self.oracle_client.initialize()
                logger.info("✅ Oracle thin mode memory client ready")
            
            # Initialize Mem0 coordinator
            if self.valves.enable_mem0_coordination:
                logger.info(f"🔍 DEBUG: Initializing Mem0 with collection: {self.valves.mem0_collection}")
                self.mem0_coordinator = Mem0Coordinator(
                    self.valves.qdrant_host,
                    self.valves.qdrant_port,
                    self.valves.mem0_collection,
                    embedding_model=self.valves.embedding_model,
                    embedding_dimensions=self.valves.embedding_dimensions,
                    embedding_provider=self.valves.embedding_provider,
                    use_openai_compatible=self.valves.use_openai_compatible,
                    gemini_api_key=self.valves.gemini_api_key,
                    llm_provider=self.valves.llm_provider,
                    llm_model=self.valves.llm_model,
                )
                # Pass filter settings
                self.mem0_coordinator.mem0_filter_by_user = self.valves.mem0_filter_by_user
                self.mem0_coordinator.mem0_user_id_field = self.valves.mem0_user_id_field
                logger.info("✅ Mem0 coordinator ready")
            
            logger.info("🧠 Oracle Advanced Memory Pipeline (Thin Mode) initialized successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize pipeline: {e}")
            logger.warning("⚠️ Continuing without Oracle memory features")
            # Continue without memory features if initialization fails
            self.valves.enable_oracle_memory = False
            self.valves.enable_mem0_coordination = False
            self.oracle_client = None

    async def on_shutdown(self):
        """Cleanup pipeline resources"""
        logger.info("🛑 Shutting down Oracle Advanced Memory Pipeline...")
        
        try:
            if self.oracle_client and hasattr(self.oracle_client, 'pool'):
                if self.oracle_client.pool:
                    self.oracle_client.pool.close()
        
            logger.info("✅ Pipeline shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """
        Process incoming messages and inject memory context
        This is where the LLM gets enhanced with memory context
        """
        start_time = time.time()
        
        try:
            self.metrics['total_requests'] += 1
            
            if self.valves.enable_debug_logging:
                logger.info("🧠 Oracle Advanced Memory Pipeline (Thin Mode) - Processing request")
            
            messages = body.get("messages", [])
            if not messages:
                return body
            
            # Skip if this is a task or system operation
            if body.get("metadata", {}).get("task") is not None:
                return body
            
            # Get user information
            user_id = self._get_user_id(user)
            
            # Generate session ID
            self.current_session_id = self._generate_session_id(user_id, messages)
            self.conversation_turn += 1
            
            # Extract user message
            user_message = self._extract_user_message(messages)
            if not user_message:
                return body
            
            if self.valves.enable_debug_logging:
                logger.info(f"👤 Processing for user: {user_id}")
                logger.info(f"💬 User message: {user_message[:100]}...")
            
            # === PARALLEL HYBRID MEMORY RETRIEVAL ===

            oracle_memories = []
            mem0_memories = []
            user_patterns = {}

            # 🚀 PARALLEL OPTIMIZATION: Run Oracle and Mem0 searches simultaneously
            search_tasks = []

            # Task 1: Oracle search
            if self.valves.enable_oracle_memory and self.oracle_client:
                oracle_task = asyncio.create_task(
                    self._safe_oracle_search(user_id, user_message),
                    name="oracle_search"
                )
                search_tasks.append(oracle_task)

            # Task 2: Mem0 search
            logger.info(f"🔍 DEBUG: Mem0 coordination check - enabled: {self.valves.enable_mem0_coordination}, coordinator: {self.mem0_coordinator is not None}")
            if self.valves.enable_mem0_coordination and self.mem0_coordinator:
                mem0_task = asyncio.create_task(
                    self._safe_mem0_search(user_id, user_message),
                    name="mem0_search"
                )
                search_tasks.append(mem0_task)

            # Execute all search tasks in parallel
            if search_tasks:
                if self.valves.enable_debug_logging:
                    logger.info(f"🚀 Starting {len(search_tasks)} parallel memory searches...")

                search_start_time = time.time()

                try:
                    results = await asyncio.wait_for(
                        asyncio.gather(*search_tasks, return_exceptions=True),
                        timeout=self.valves.memory_search_timeout
                    )

                    # Parse results
                    for i, result in enumerate(results):
                        task_name = search_tasks[i].get_name()

                        if isinstance(result, Exception):
                            logger.error(f"❌ {task_name} failed: {result}")
                            continue

                        if task_name == "oracle_search":
                            oracle_memories = result or []
                            self.metrics['oracle_searches'] += 1
                            if self.valves.enable_debug_logging:
                                logger.info(f"🏛️ Found {len(oracle_memories)} Oracle memories")

                        elif task_name == "mem0_search":
                            mem0_memories = result or []
                            self.metrics['mem0_searches'] += 1
                            if self.valves.enable_debug_logging:
                                logger.info(f"🔄 Found {len(mem0_memories)} Mem0 memories")

                    search_duration = time.time() - search_start_time
                    if self.valves.enable_debug_logging:
                        logger.info(f"✅ Parallel search completed in {search_duration:.2f}s")

                except asyncio.TimeoutError:
                    logger.warning(f"⏰ Parallel memory search timeout after {self.valves.memory_search_timeout}s")
                except Exception as e:
                    logger.error(f"❌ Parallel memory search error: {e}")
            else:
                if self.valves.enable_debug_logging:
                    logger.info("ℹ️ No memory search tasks configured")
            
            # Get user patterns for personalization
            if self.valves.enable_pattern_learning and self.oracle_client:
                try:
                    user_patterns = await asyncio.wait_for(
                        self.oracle_client.get_user_patterns(user_id),
                        timeout=self.valves.memory_search_timeout
                    )
                    
                    if user_patterns and self.valves.enable_debug_logging:
                        logger.info(f"📊 Retrieved {len(user_patterns)} user patterns")
                        
                except Exception as e:
                    logger.error(f"❌ Pattern retrieval error: {e}")
            
            # === CONTEXT COORDINATION AND INJECTION ===
            
            memory_context = self._create_unified_context(
                oracle_memories, mem0_memories, user_patterns, user_message
            )
            
            if memory_context:
                # Inject memory context into system message
                system_message = next(
                    (msg for msg in messages if msg.get("role") == "system"), None
                )
                
                if system_message:
                    system_message["content"] += f"\n\n{memory_context}"
                else:
                    messages.insert(0, {
                        "role": "system",
                        "content": f"You are an AI assistant with access to user memory and behavioral patterns.\n\n{memory_context}"
                    })
                
                self.metrics['memories_injected'] += len(oracle_memories) + len(mem0_memories)
                if user_patterns:
                    self.metrics['patterns_applied'] += 1
                
                if self.valves.enable_debug_logging:
                    logger.info("✅ Memory context injected into LLM")
            
            body["messages"] = messages
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.metrics['avg_response_time'] = (
                (self.metrics['avg_response_time'] * (self.metrics['total_requests'] - 1) + processing_time) /
                self.metrics['total_requests']
            )
            
        except Exception as e:
            logger.error(f"❌ Error in inlet processing: {e}")
            import traceback
            traceback.print_exc()
        
        return body

    async def outlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """
        Process outgoing messages and store conversation in memory
        This is where we learn from the conversation
        """
        try:
            if not self.valves.auto_store_conversations:
                return body
            
            # Extract AI response
            messages = body.get("messages", [])
            ai_response = None
            user_message = None
            
            # Find the latest AI response and user message
            for msg in reversed(messages):
                if msg.get("role") == "assistant" and not ai_response:
                    ai_response = msg.get("content")
                elif msg.get("role") == "user" and not user_message:
                    user_message = msg.get("content")
            
            if not ai_response or not user_message:
                return body
            
            user_id = self._get_user_id(user)
            
            if self.valves.enable_debug_logging:
                logger.info(f"💾 Storing conversation for user: {user_id}")

            # 🚀 PARALLEL OPTIMIZATION: Run Oracle and Mem0 storage simultaneously
            storage_tasks = []

            # Task 1: Oracle storage
            if self.valves.enable_oracle_memory and self.oracle_client:
                oracle_storage_task = asyncio.create_task(
                    self._safe_oracle_storage(user_id, user_message, ai_response),
                    name="oracle_storage"
                )
                storage_tasks.append(oracle_storage_task)

            # Task 2: Mem0 storage
            logger.info(f"🔍 DEBUG: Mem0 store check - enabled: {self.valves.enable_mem0_coordination}, coordinator: {self.mem0_coordinator is not None}")
            if self.valves.enable_mem0_coordination and self.mem0_coordinator:
                mem0_storage_task = asyncio.create_task(
                    self._safe_mem0_storage(user_id, user_message, ai_response),
                    name="mem0_storage"
                )
                storage_tasks.append(mem0_storage_task)

            # Execute all storage tasks in parallel
            if storage_tasks:
                if self.valves.enable_debug_logging:
                    logger.info(f"🚀 Starting {len(storage_tasks)} parallel memory storage operations...")

                storage_start_time = time.time()

                try:
                    results = await asyncio.wait_for(
                        asyncio.gather(*storage_tasks, return_exceptions=True),
                        timeout=15  # Storage timeout
                    )

                    # Parse results
                    for i, result in enumerate(results):
                        task_name = storage_tasks[i].get_name()

                        if isinstance(result, Exception):
                            logger.error(f"❌ {task_name} failed: {result}")
                            continue

                        if task_name == "oracle_storage" and result:
                            if self.valves.enable_debug_logging:
                                logger.info("✅ Conversation stored in Oracle memory")

                        elif task_name == "mem0_storage" and result:
                            if self.valves.enable_debug_logging:
                                logger.info("✅ Conversation stored in Mem0 system")

                    storage_duration = time.time() - storage_start_time
                    if self.valves.enable_debug_logging:
                        logger.info(f"✅ Parallel storage completed in {storage_duration:.2f}s")

                except asyncio.TimeoutError:
                    logger.warning("⏰ Parallel memory storage timeout")
                except Exception as e:
                    logger.error(f"❌ Parallel memory storage error: {e}")
            else:
                if self.valves.enable_debug_logging:
                    logger.info("ℹ️ No memory storage tasks configured")
            
        except Exception as e:
            logger.error(f"❌ Error in outlet processing: {e}")
        
        return body

    def _get_user_id(self, user: Optional[dict]) -> str:
        """Extract user ID from user object"""
        if user and "id" in user:
            return str(user["id"])
        return self.valves.default_user_id

    def _generate_session_id(self, user_id: str, messages: List[Dict]) -> str:
        """Generate session ID for conversation tracking"""
        if not messages:
            return f"{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Use first message to create consistent session ID
        first_msg = messages[0].get('content', '')[:50]
        session_hash = hashlib.md5(f"{user_id}_{first_msg}".encode()).hexdigest()[:8]
        return f"{user_id}_{session_hash}"

    def _extract_user_message(self, messages: List[Dict]) -> Optional[str]:
        """Extract the latest user message"""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                return msg.get("content")
        return None

    def _create_unified_context(self, oracle_memories: List[Dict], mem0_memories: List[Dict],
                               user_patterns: Dict, current_query: str) -> str:
        """Create unified memory context for LLM"""
        context_parts = []
        
        # Add Oracle long-term memories
        if oracle_memories:
            context_parts.append("🏛️ Long-term Memory (Oracle Thin Mode):")
            for i, memory in enumerate(oracle_memories[:self.valves.max_oracle_memories], 1):
                content = memory.get('content', '')[:150]
                confidence = memory.get('confidence', 0.0)
                memory_type = memory.get('memory_type', 'unknown')
                context_parts.append(f"  {i}. [{memory_type}] {content}... (confidence: {confidence:.2f})")
        
        # Add Mem0 recent memories
        if mem0_memories:
            context_parts.append("\n🔄 Recent Context (Mem0):")
            for i, memory in enumerate(mem0_memories[:self.valves.max_mem0_memories], 1):
                content = memory.get('content', '')[:150]
                context_parts.append(f"  {i}. {content}...")
        
        # Add user patterns for personalization
        if user_patterns and self.valves.enable_pattern_learning:
            context_parts.append("\n👤 User Patterns:")
            
            if 'communication_style' in user_patterns:
                style_data = user_patterns['communication_style']['data']
                formality = style_data.get('formality', 'balanced')
                length_pref = style_data.get('length_preference', 'moderate')
                context_parts.append(f"  • Communication: {formality}, prefers {length_pref} responses")
            
            if 'topic_interest' in user_patterns:
                interests = user_patterns['topic_interest']['data']
                top_interests = sorted(interests.items(), key=lambda x: x[1], reverse=True)[:3]
                if top_interests:
                    interest_list = ', '.join([topic for topic, _ in top_interests])
                    context_parts.append(f"  • Interests: {interest_list}")
            
            if 'response_style' in user_patterns:
                response_data = user_patterns['response_style']['data']
                detail_level = response_data.get('detail_level', 'medium')
                wants_examples = response_data.get('wants_examples', False)
                context_parts.append(f"  • Prefers: {detail_level} detail" + 
                                   (" with examples" if wants_examples else ""))
        
        if context_parts:
            context_parts.append("\n💡 Use this context to provide personalized, contextually-aware responses that match the user's communication style and interests.")
            return "\n".join(context_parts)
        
        return ""

    def get_metrics(self) -> Dict:
        """Get pipeline performance metrics"""
        return {
            **self.metrics,
            'oracle_enabled': self.valves.enable_oracle_memory,
            'mem0_enabled': self.valves.enable_mem0_coordination,
            'pattern_learning_enabled': self.valves.enable_pattern_learning,
            'current_session': self.current_session_id,
            'conversation_turn': self.conversation_turn,
            'mode': 'thin_mode_only'
        }

    # === PARALLEL OPTIMIZATION HELPER METHODS ===

    async def _safe_oracle_search(self, user_id: str, user_message: str) -> List[Dict]:
        """Safe Oracle search with error handling for parallel execution"""
        try:
            return await asyncio.wait_for(
                self.oracle_client.search_memories(
                    user_id, user_message, self.valves.max_oracle_memories
                ),
                timeout=self.valves.memory_search_timeout
            )
        except asyncio.TimeoutError:
            logger.warning("⏰ Oracle memory search timeout in parallel execution")
            return []
        except Exception as e:
            logger.error(f"❌ Oracle memory search error in parallel execution: {e}")
            return []

    async def _safe_mem0_search(self, user_id: str, user_message: str) -> List[Dict]:
        """Safe Mem0 search with error handling for parallel execution"""
        try:
            return await asyncio.wait_for(
                self.mem0_coordinator.search_memories(
                    user_id, user_message, self.valves.max_mem0_memories
                ),
                timeout=self.valves.memory_search_timeout
            )
        except asyncio.TimeoutError:
            logger.warning("⏰ Mem0 memory search timeout in parallel execution")
            return []
        except Exception as e:
            logger.error(f"❌ Mem0 memory search error in parallel execution: {e}")
            return []

    async def _safe_oracle_storage(self, user_id: str, user_message: str, ai_response: str) -> bool:
        """Safe Oracle storage with error handling for parallel execution"""
        try:
            # Store user message
            await self.oracle_client.store_memory(
                user_id, f"User: {user_message}",
                memory_type="user_message",
                session_id=self.current_session_id
            )

            # Store AI response
            await self.oracle_client.store_memory(
                user_id, f"Assistant: {ai_response}",
                memory_type="assistant_response",
                session_id=self.current_session_id
            )

            return True
        except Exception as e:
            logger.error(f"❌ Oracle storage error in parallel execution: {e}")
            return False

    async def _safe_mem0_storage(self, user_id: str, user_message: str, ai_response: str) -> bool:
        """Safe Mem0 storage with error handling for parallel execution"""
        try:
            await self.mem0_coordinator.store_conversation(
                user_id, user_message, ai_response
            )
            return True
        except Exception as e:
            logger.error(f"❌ Mem0 storage error in parallel execution: {e}")
            return False

# Pipeline instance
pipeline = Pipeline()