"""
title: mem0-owui-gemini-3072-fixed
author: Kilo Code
date: 2025-01-17
version: 1.1
license: MIT
description: Fixed mem0 filter with actual Gemini 3072 dimensions and Qdrant persistence
requirements: mem0ai==0.1.102, pydantic==2.7.4, requests
"""

import os
import requests
from typing import ClassVar, List, Optional, Dict, Any
from pydantic import BaseModel, Field
from schemas import OpenAIChatMessage
import asyncio
import json
import hashlib
from datetime import datetime

class GeminiEmbedding:
    """Gemini embedding provider (defaults to gemini-embedding-001)."""

    def __init__(self, api_key: str, model: str = "models/gemini-embedding-001"):
        self.api_key = api_key
        self.model = model
        self.url = f"https://generativelanguage.googleapis.com/v1beta/models/{model.split('/')[-1]}:embedContent"

    def embed(self, text: str):
        headers = {
            "Content-Type": "application/json",
            "x-goog-api-key": self.api_key,
        }

        data = {
            "model": self.model,
            "content": {"parts": [{"text": text}]},
        }

        response = requests.post(self.url, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            return result["embedding"]["values"]
        raise Exception(f"Gemini API error: {response.status_code} - {response.text}")


class GoogleEmbedding004:
    """Google text-embedding-004 provider (768 dimensions)."""

    def __init__(self, api_key: str, model: str = "models/text-embedding-004"):
        self.api_key = api_key
        self.model = model
        self.url = f"https://generativelanguage.googleapis.com/v1beta/models/{model.split('/')[-1]}:embedContent"

    def embed(self, text: str):
        headers = {
            "Content-Type": "application/json",
            "x-goog-api-key": self.api_key,
        }

        data = {
            "model": self.model,
            "content": {"parts": [{"text": text}]},
        }

        response = requests.post(self.url, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            return result["embedding"]["values"]
        raise Exception(f"Google Embedding API error: {response.status_code} - {response.text}")

class QdrantClient:
    """Simple Qdrant client for memory persistence"""
    
    def __init__(self, host: str, port: str, collection_name: str):
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.base_url = f"http://{host}:{port}"
        
    async def ensure_collection(self, vector_size: int, distance: str = "Cosine"):
        """Ensure collection exists with proper configuration and desired vector size."""
        try:
            # Check if collection exists
            response = requests.get(f"{self.base_url}/collections/{self.collection_name}")
            
            if response.status_code == 404:
                # Create collection with 3072 dimensions
                collection_config = {
                    "vectors": {
                        "size": vector_size,
                        "distance": distance
                    }
                }
                
                create_response = requests.put(
                    f"{self.base_url}/collections/{self.collection_name}",
                    json=collection_config
                )
                
                if create_response.status_code in [200, 201]:
                    print(f"✅ Created Qdrant collection: {self.collection_name}")
                else:
                    print(f"❌ Failed to create collection: {create_response.text}")
                    return False
            elif response.status_code == 200:
                # Verify existing vector size
                try:
                    cfg = response.json().get("result", {}).get("config", {}).get("params", {}).get("vectors", {})
                    current_size = cfg.get("size")
                    if current_size and int(current_size) != int(vector_size):
                        print(
                            f"⚠️ Qdrant collection '{self.collection_name}' vector size={current_size} != desired={vector_size}. "
                            f"Consider updating valve 'collection_name' to a new collection or migrating data."
                        )
                except Exception:
                    pass
            
            return True
            
        except Exception as e:
            print(f"❌ Error ensuring collection: {e}")
            return False
    
    async def store_memory(self, memory_id: str, content: str, embedding: List[float], 
                          user_id: str, session_id: str):
        """Store memory in Qdrant"""
        try:
            point = {
                "id": memory_id,
                "vector": embedding,
                "payload": {
                    "content": content,
                    "user_id": user_id,
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "dimensions": len(embedding)
                }
            }
            
            response = requests.put(
                f"{self.base_url}/collections/{self.collection_name}/points",
                json={"points": [point]}
            )
            
            if response.status_code in [200, 201]:
                return True
            else:
                print(f"❌ Failed to store memory: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error storing memory in Qdrant: {e}")
            return False
    
    async def search_memories(self, query_embedding: List[float], user_id: str, 
                            limit: int = 5, threshold: float = 0.2):
        """Search memories in Qdrant"""
        try:
            search_request = {
                "vector": query_embedding,
                "limit": limit,
                "score_threshold": threshold,
                "with_payload": True,
                "filter": {
                    "must": [
                        {
                            "key": "user_id",
                            "match": {"value": user_id}
                        }
                    ]
                }
            }
            
            response = requests.post(
                f"{self.base_url}/collections/{self.collection_name}/points/search",
                json=search_request
            )
            
            if response.status_code == 200:
                results = response.json().get("result", [])
                
                formatted_results = []
                for result in results:
                    payload = result.get("payload", {})
                    formatted_results.append({
                        'memory': payload.get('content', ''),
                        'score': result.get('score', 0),
                        'metadata': {
                            'session_id': payload.get('session_id', ''),
                            'timestamp': payload.get('timestamp', ''),
                            'dimensions': payload.get('dimensions', 3072)
                        }
                    })
                
                return formatted_results
            else:
                print(f"❌ Failed to search memories: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error searching memories in Qdrant: {e}")
            return []

class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(
            default="default_user", description="Default user ID for memory operations"
        )

        # Vector store config
        qdrant_host: str = Field(
            default="qdrant", description="Qdrant vector database host"
        )
        qdrant_port: str = Field(
            default="6333", description="Qdrant vector database port"
        )
        collection_name: str = Field(
            default="mem0_gemini_3072_fixed", description="Qdrant collection name for memory vectors"
        )
        qdrant_distance: str = Field(
            default="Cosine", description="Distance metric for Qdrant (Cosine, Dot, Euclid)"
        )

        # Embedding provider/model config (customizable)
        embedding_provider: str = Field(
            default="gemini", description="Embedding provider: 'gemini' or 'google'"
        )
        embedding_model: str = Field(
            default="models/gemini-embedding-001", description="Embedding model id for provider"
        )
        embedding_dimensions: int = Field(
            default=3072, description="Expected embedding dimensions (e.g., 3072 for Gemini, 768 for Google text-embedding-004)"
        )
        google_api_key: str = Field(
            default=os.getenv("GOOGLE_API_KEY", ""), description="Google API KEY for text-embedding-004"
        )

        # Gemini API config
        gemini_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", "YOUR_GEMINI_API_KEY_HERE"), 
            description="Gemini API key"
        )

        # Memory behavior config
        max_memories_to_inject: int = Field(
            default=5, description="Maximum number of memories to include in context"
        )
        memory_relevance_threshold: float = Field(
            default=0.2, description="Minimum relevance score for memory inclusion"
        )
        auto_store_messages: bool = Field(
            default=True, description="Automatically store user and assistant messages"
        )
        enable_debug_logging: bool = Field(
            default=True, description="Enable detailed debug logging"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.embedding_model = None
        self.qdrant_client = None  # Replace in-memory storage with Qdrant client
        self.current_session_id = None
        self._conversation_turn = 0

    async def on_startup(self):
        print(f"🚀 Starting up: {__name__}")
        # Prepare embedding provider based on valves
        if self.valves.embedding_provider.lower() == "google":
            self.embedding_model = GoogleEmbedding004(self.valves.google_api_key, self.valves.embedding_model)
            # If user didn't set explicit dimensions, default to 768 for Google
            if not self.valves.embedding_dimensions or int(self.valves.embedding_dimensions) <= 0:
                self.valves.embedding_dimensions = 768
            print(f"✅ Google embedding initialized: {self.valves.embedding_model} ({self.valves.embedding_dimensions} dims)")
        else:
            self.embedding_model = GeminiEmbedding(self.valves.gemini_api_key, self.valves.embedding_model)
            if not self.valves.embedding_dimensions or int(self.valves.embedding_dimensions) <= 0:
                self.valves.embedding_dimensions = 3072
            print(f"✅ Gemini embedding initialized: {self.valves.embedding_model} ({self.valves.embedding_dimensions} dims)")
        
        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(
            self.valves.qdrant_host,
            self.valves.qdrant_port,
            self.valves.collection_name
        )
        
        # Ensure collection exists
        collection_ready = await self.qdrant_client.ensure_collection(
            vector_size=int(self.valves.embedding_dimensions),
            distance=self.valves.qdrant_distance,
        )
        if collection_ready:
            print("✅ Qdrant collection ready for persistent storage")
        else:
            print("❌ Warning: Qdrant collection setup failed - falling back to logging only")

    async def on_shutdown(self):
        print(f"🛑 Shutting down: {__name__}")

    def generate_session_id(self, user_id: str, messages: List[Dict]) -> str:
        """Generate a unique session ID based on conversation context"""
        if not messages:
            return f"{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        first_msg = messages[0].get('content', '')[:100]
        session_hash = hashlib.md5(f"{user_id}_{first_msg}".encode()).hexdigest()[:8]
        return f"{user_id}_{session_hash}"

    async def store_memory(self, user_id: str, content: str, session_id: str):
        """Store memory in Qdrant using the configured embedding provider/model"""
        try:
            # Get embedding
            embedding = self.embedding_model.embed(content)
            
            # Generate unique memory ID
            memory_id = hashlib.md5(f"{user_id}_{content}_{session_id}_{datetime.now().isoformat()}".encode()).hexdigest()
            
            # Store in Qdrant
            if self.qdrant_client:
                success = await self.qdrant_client.store_memory(
                    memory_id, content, embedding, user_id, session_id
                )
                
                if success and self.valves.enable_debug_logging:
                    print(f"💾 Stored memory in Qdrant with {len(embedding)} dimensions: {content[:50]}...")
                elif not success:
                    print(f"❌ Failed to store memory in Qdrant: {content[:50]}...")
            else:
                print(f"❌ Qdrant client not available - memory not stored: {content[:50]}...")
                
        except Exception as e:
            if self.valves.enable_debug_logging:
                print(f"❌ Error storing memory: {e}")

    async def search_memories(self, user_id: str, query: str, limit: int = 5):
        """Search memories using Qdrant vector search"""
        try:
            if not self.qdrant_client:
                if self.valves.enable_debug_logging:
                    print("❌ Qdrant client not available for memory search")
                return []
            
            # Get query embedding
            query_embedding = self.embedding_model.embed(query)
            
            # Search in Qdrant
            results = await self.qdrant_client.search_memories(
                query_embedding, 
                user_id, 
                limit, 
                self.valves.memory_relevance_threshold
            )
            
            if self.valves.enable_debug_logging and results:
                print(f"🔍 Found {len(results)} relevant memories from Qdrant")
            
            return results
            
        except Exception as e:
            if self.valves.enable_debug_logging:
                print(f"❌ Error searching memories: {e}")
            return []

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Inject memory context into the prompt before sending to the model."""

        if self.valves.enable_debug_logging:
            print("🧠 Custom Gemini 3072 Memory Pipeline (Qdrant Persistent) - Inlet triggered")
            # Log the entire request body for debugging
            try:
                print(f"PIPE_BODY: {json.dumps(body, indent=2)}")
            except Exception as e:
                print(f"PIPE_BODY_ERROR: Could not dump body to JSON: {e}")


        messages = body.get("messages", [])
        if not messages or (body.get("metadata", {}).get("task") is not None):
            return body

        # Get user ID
        current_user_id = self.valves.user_id
        if user and "id" in user:
            current_user_id = user["id"]
        
        if self.valves.enable_debug_logging:
            print(f"👤 User ID: {current_user_id}")

        # Generate session ID
        self.current_session_id = self.generate_session_id(current_user_id, messages)
        self._conversation_turn += 1

        # Find latest user message
        user_message = None
        assistant_message = None
        
        for msg in reversed(messages):
            if msg.get("role") == "user" and not user_message:
                user_message = msg.get("content")
            elif msg.get("role") == "assistant" and not assistant_message:
                assistant_message = msg.get("content")

        if not user_message:
            return body

        try:
            # Search for relevant memories in Qdrant
            if self.valves.enable_debug_logging:
                print(f"🔍 Searching Qdrant memories for: {user_message[:50]}...")
            
            memories = await self.search_memories(
                current_user_id,
                user_message,
                self.valves.max_memories_to_inject
            )

            # Store messages if enabled
            if self.valves.auto_store_messages:
                if assistant_message:
                    asyncio.create_task(
                        self.store_memory(current_user_id, assistant_message, self.current_session_id)
                    )

                asyncio.create_task(
                    self.store_memory(current_user_id, user_message, self.current_session_id)
                )

            # Format memory context
            if memories:
                memory_context = self.format_memory_context(memories)
                
                if memory_context:
                    # Inject into system message
                    system_message = next(
                        (msg for msg in messages if msg["role"] == "system"), None
                    )
                    if system_message:
                        system_message["content"] += memory_context
                    else:
                        messages.insert(0, {
                            "role": "system",
                            "content": f"Use these memories to enhance your response:\n{memory_context}",
                        })

            body["messages"] = messages

        except Exception as e:
            if self.valves.enable_debug_logging:
                print(f"❌ Memory integration error: {str(e)}")
                import traceback
                traceback.print_exc()

        return body

    def format_memory_context(self, memories: List[Dict]) -> str:
        """Format memories for context injection"""
        if not memories:
            return ""

        context_parts = [f"\n🧠 Relevant Memories ({self.valves.embedding_dimensions}-dimensional, Qdrant-stored):"]
        
        for i, mem in enumerate(memories):
            memory_text = mem.get('memory', '')
            metadata = mem.get('metadata', {})
            score = mem.get('score', 0)
            
            context_part = f"\n📝 Memory {i+1}:"
            context_part += f"\n   Content: {memory_text}"
            context_part += f"\n   📊 Relevance: {score:.2f}"
            context_part += f"\n   📏 Dimensions: {metadata.get('dimensions', 'unknown')}"
            
            if 'timestamp' in metadata:
                try:
                    timestamp = datetime.fromisoformat(metadata['timestamp'].replace('Z', '+00:00'))
                    time_str = timestamp.strftime('%Y-%m-%d %H:%M')
                    context_part += f"\n   ⏰ Time: {time_str}"
                except:
                    pass
            
            context_parts.append(context_part)
        
        if len(context_parts) > 1:
            context_parts.append(
                f"\n💡 Use these {self.valves.embedding_dimensions}-dimensional memories from Qdrant to provide contextual responses."
            )
            return "\n".join(context_parts)
        
        return ""
