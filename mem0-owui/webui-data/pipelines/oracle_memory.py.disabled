"""
title: oracle-memory
author: AccA System
date: 2025-08-14
version: 1.0
license: MIT
description: Oracle Memory Pipeline with proven wallet connection
requirements: oracledb, pydantic
"""

import json
import logging
import os
from typing import List, Optional, Dict, Any

import oracledb
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Pipeline:
    class Valves(BaseModel):
        # Oracle Configuration
        oracle_user: str = "ADMIN"
        oracle_password: str = "Twilv0zera@123"
        oracle_tns_alias: str = "swiv8hv5y96iwo2t_high"
        oracle_wallet_location: str = "/app/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"
        oracle_wallet_password: str = "Twilv0zera@123"
        
        # Pipeline Settings
        enable_oracle_storage: bool = True
        memory_relevance_threshold: float = 0.7
        max_memories_to_inject: int = 5

    def __init__(self):
        self.type = "manifold"
        self.id = "oracle-memory"
        self.name = "Oracle Memory"
        self.valves = self.Valves()

        # Initialize Oracle pool
        self.oracle_pool = None

        logger.info("🚀 Starting Oracle Memory Pipeline...")

    def pipelines(self):
        """Return list of pipelines (required by pipeline loader)"""
        return [
            {
                "id": self.id,
                "name": self.name,
                "type": self.type
            }
        ]

    async def on_startup(self):
        """Initialize Oracle connection"""
        try:
            if self.valves.enable_oracle_storage:
                await self._init_oracle()
            
            logger.info("🎉 Oracle Memory Pipeline ready!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Oracle pipeline: {e}")

    async def _init_oracle(self):
        """Initialize Oracle connection pool with proven method"""
        try:
            logger.info("🔧 Initializing Oracle connection...")
            
            # Use proven connection parameters
            self.oracle_pool = oracledb.create_pool(
                user=self.valves.oracle_user,
                password=self.valves.oracle_password,
                dsn=self.valves.oracle_tns_alias,
                config_dir=self.valves.oracle_wallet_location,
                wallet_location=self.valves.oracle_wallet_location,
                wallet_password=self.valves.oracle_wallet_password,
                min=1, max=3, increment=1,
                ping_interval=60, timeout=300
            )
            
            # Test connection
            conn = self.oracle_pool.acquire()
            cursor = conn.cursor()
            cursor.execute("SELECT USER FROM DUAL")
            user = cursor.fetchone()[0]
            logger.info(f"✅ Oracle connected as: {user}")
            
            # Create memory table if not exists
            await self._create_memory_table(cursor, conn)
            
            cursor.close()
            self.oracle_pool.release(conn)
            
            logger.info("✅ Oracle connection pool initialized")
            
        except Exception as e:
            logger.error(f"❌ Oracle initialization failed: {e}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            self.oracle_pool = None

    async def _create_memory_table(self, cursor, conn):
        """Create Oracle memory table if not exists"""
        try:
            # Check if table exists
            cursor.execute("""
                SELECT COUNT(*) FROM USER_TABLES 
                WHERE TABLE_NAME = 'ORACLE_MEMORY'
            """)
            exists = cursor.fetchone()[0]
            
            if exists == 0:
                logger.info("🔧 Creating ORACLE_MEMORY table...")
                cursor.execute("""
                    CREATE TABLE ORACLE_MEMORY (
                        id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                        user_id VARCHAR2(100),
                        session_id VARCHAR2(100),
                        memory_content CLOB,
                        memory_type VARCHAR2(50) DEFAULT 'conversation',
                        relevance_score NUMBER(3,2),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                logger.info("✅ ORACLE_MEMORY table created")
            else:
                logger.info("✅ ORACLE_MEMORY table already exists")
                
        except Exception as e:
            logger.error(f"❌ Failed to create memory table: {e}")

    async def on_shutdown(self):
        """Cleanup resources"""
        if self.oracle_pool:
            self.oracle_pool.close()
            logger.info("🔄 Oracle connection pool closed")

    def pipe(self, user_message: str, model_id: str, messages: List[dict], body: dict) -> str:
        """Main pipeline processing"""
        try:
            user_id = body.get("user", {}).get("id", "anonymous")
            session_id = body.get("chat_id", "default")
            
            # Search and inject memories
            relevant_memories = self._search_memories(user_message, user_id)
            
            if relevant_memories:
                memory_context = self._format_memory_context(relevant_memories)
                
                # Inject memory context into system message
                if messages and messages[0].get("role") == "system":
                    messages[0]["content"] += f"\n\n📚 **Oracle Memories:**\n{memory_context}"
                else:
                    messages.insert(0, {
                        "role": "system",
                        "content": f"📚 **Oracle Memories:**\n{memory_context}"
                    })
                
                logger.info(f"💾 Injected {len(relevant_memories)} Oracle memories")
            
            # Store new memory
            self._store_memory(user_message, user_id, session_id)
            
            return user_message
            
        except Exception as e:
            logger.error(f"❌ Pipeline processing error: {e}")
            return user_message

    def _search_memories(self, query: str, user_id: str) -> List[Dict]:
        """Search for relevant memories in Oracle"""
        try:
            if not self.oracle_pool:
                return []
            
            conn = self.oracle_pool.acquire()
            cursor = conn.cursor()
            
            # Simple text search in Oracle
            cursor.execute("""
                SELECT memory_content, relevance_score, created_at
                FROM ORACLE_MEMORY 
                WHERE user_id = :1 
                AND UPPER(memory_content) LIKE UPPER(:2)
                ORDER BY created_at DESC
                FETCH FIRST :3 ROWS ONLY
            """, (user_id, f"%{query}%", self.valves.max_memories_to_inject))
            
            results = cursor.fetchall()
            
            memories = []
            for content, score, created_at in results:
                memories.append({
                    'memory': content,
                    'score': float(score) if score else 0.8,
                    'created_at': str(created_at)
                })
            
            cursor.close()
            self.oracle_pool.release(conn)
            
            logger.info(f"🔍 Found {len(memories)} Oracle memories")
            return memories
            
        except Exception as e:
            logger.error(f"❌ Oracle memory search error: {e}")
            return []

    def _format_memory_context(self, memories: List[Dict]) -> str:
        """Format memories for injection"""
        context_parts = []
        for i, memory in enumerate(memories, 1):
            content = memory.get('memory', '')
            score = memory.get('score', 0)
            context_parts.append(f"{i}. {content} (score: {score:.2f})")
        
        return "\n".join(context_parts)

    def _store_memory(self, content: str, user_id: str, session_id: str):
        """Store memory in Oracle DB"""
        try:
            if not self.oracle_pool:
                return
            
            conn = self.oracle_pool.acquire()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO ORACLE_MEMORY 
                (user_id, session_id, memory_content, memory_type, relevance_score)
                VALUES (:1, :2, :3, :4, :5)
            """, (user_id, session_id, content, 'conversation', 1.0))
            
            conn.commit()
            cursor.close()
            self.oracle_pool.release(conn)
            
            logger.info("✅ Memory stored in Oracle")
            
        except Exception as e:
            logger.error(f"❌ Oracle storage error: {e}")
