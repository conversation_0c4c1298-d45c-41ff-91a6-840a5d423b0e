#!/usr/bin/env python3
"""
Setup script to integrate jini_crawler (port 8009) with MCPO server
"""

import json
import subprocess
import time
import requests
import os

def check_jini_crawler_status():
    """Check if jini_crawler is running on port 8009"""
    try:
        response = requests.get("http://localhost:8009/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_jini_crawler_openapi():
    """Get OpenAPI schema from jini_crawler"""
    try:
        response = requests.get("http://localhost:8009/openapi.json", timeout=10)
        if response.status_code == 200:
            return response.json()
        return None
    except Exception as e:
        print(f"Error getting OpenAPI schema: {e}")
        return None

def create_mcpo_config_with_jini():
    """Create MCPO config that includes jini_crawler via HTTP proxy"""
    
    # Base config with existing tools
    config = {
        "mcpServers": {
            "document_processing": {
                "command": "/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/venv/bin/python",
                "args": ["/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/servers/document_processing/server.py"],
                "description": "Document processing and content extraction"
            },
            "vietnamese_language": {
                "command": "/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/venv/bin/python",
                "args": ["/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/servers/vietnamese_language/server.py"],
                "description": "Vietnamese language processing and analysis"
            },
            "time_utilities": {
                "command": "/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/venv/bin/python",
                "args": ["/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/servers/time_utilities/server.py"],
                "description": "Time and timezone management utilities"
            },
            "weather_service": {
                "command": "/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/venv/bin/python",
                "args": ["/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/servers/weather_service/server.py"],
                "description": "Weather data and forecasting services"
            },
            "filesystem": {
                "command": "/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/venv/bin/python",
                "args": ["/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/servers/filesystem/server.py"],
                "description": "File system operations and management"
            },
            "wikipedia": {
                "command": "/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/venv/bin/python",
                "args": ["/home/<USER>/AccA/AccA/mem0-owui/mcp-integration/servers/wikipedia/server.py"],
                "description": "Wikipedia search and content retrieval"
            },
            # NEW: jini_crawler via HTTP proxy
            "jini_crawler": {
                "type": "http",
                "url": "http://localhost:8009",
                "description": "Advanced web crawling with AI summarization and search"
            }
        },
        "server_info": {
            "name": "AccA MCPO Server - With Jini Crawler",
            "description": "MCPO server with new jini_crawler integration",
            "version": "1.1.0",
            "total_servers": 7
        }
    }
    
    return config

def save_config(config, filename="mcpo_with_jini.json"):
    """Save config to file"""
    config_path = f"config/{filename}"
    os.makedirs("config", exist_ok=True)
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Config saved to {config_path}")
    return config_path

def start_mcpo_with_jini(config_path):
    """Start MCPO server with jini_crawler config"""
    
    print("🚀 Starting MCPO server with jini_crawler...")
    
    # Stop existing MCPO processes
    subprocess.run(["pkill", "-f", "mcpo.*--port 8000"], capture_output=True)
    time.sleep(3)
    
    # Start new MCPO server
    cmd = [
        "mcpo", 
        "--port", "8000", 
        "--host", "0.0.0.0",
        "--config", config_path,
        "--api-key", "acca-enhanced-rag-mcp-key-2025"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    # Start in background
    process = subprocess.Popen(cmd)
    
    # Wait for server to start
    print("⏳ Waiting for MCPO server to start...")
    time.sleep(10)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/openapi.json", timeout=5)
        if response.status_code == 200:
            print("✅ MCPO server started successfully!")
            print("📖 API Documentation: http://localhost:8000/docs")
            return True
        else:
            print("❌ MCPO server failed to start properly")
            return False
    except Exception as e:
        print(f"❌ Error checking MCPO server: {e}")
        return False

def main():
    print("🔧 Setting up jini_crawler integration with MCPO...")
    
    # Check if jini_crawler is running
    if not check_jini_crawler_status():
        print("❌ jini_crawler is not running on port 8009")
        print("Please start jini_crawler first")
        return
    
    print("✅ jini_crawler is running on port 8009")
    
    # Get OpenAPI schema
    schema = get_jini_crawler_openapi()
    if schema:
        print(f"✅ Found {len(schema.get('paths', {}))} endpoints in jini_crawler")
        for path in schema.get('paths', {}):
            print(f"   - {path}")
    
    # Create config
    config = create_mcpo_config_with_jini()
    config_path = save_config(config)
    
    # Start MCPO server
    if start_mcpo_with_jini(config_path):
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Open OpenWebUI in browser")
        print("2. Go to Settings > Tools")
        print("3. Add tool server: http://localhost:8000/jini_crawler")
        print("4. Test jini_crawler tools in chat")
        
        print("\n🧪 Test commands:")
        print('curl -X POST "http://localhost:8000/jini_crawler/crawl_url" \\')
        print('  -H "Content-Type: application/json" \\')
        print('  -H "Authorization: Bearer acca-enhanced-rag-mcp-key-2025" \\')
        print('  -d \'{"url": "https://example.com"}\'')
    else:
        print("❌ Setup failed")

if __name__ == "__main__":
    main()
