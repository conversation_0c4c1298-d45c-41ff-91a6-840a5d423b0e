#!/usr/bin/env python3
"""
Fix jini_crawler connection in OpenWebUI database
Change from Docker internal URL to localhost URL
"""

import sqlite3
import json
import subprocess

def get_current_config():
    """Get current OpenWebUI config from database"""
    
    # Connect to database inside Docker container
    cmd = [
        "docker", "exec", "open-webui-mcpo", "python3", "-c",
        """
import sqlite3
import json

conn = sqlite3.connect('/app/backend/data/webui.db')
cursor = conn.cursor()

cursor.execute('SELECT data FROM config ORDER BY version DESC LIMIT 1')
result = cursor.fetchone()

if result:
    config_data = json.loads(result[0])
    print(json.dumps(config_data, indent=2))

conn.close()
        """
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        return json.loads(result.stdout)
    else:
        print(f"Error getting config: {result.stderr}")
        return None

def update_jini_crawler_url(config_data):
    """Update jini_crawler URL from Docker internal to localhost"""
    
    if 'tool_server' not in config_data:
        print("No tool_server config found")
        return config_data
    
    connections = config_data['tool_server']['connections']
    
    # Find and update jini_crawler connection
    for connection in connections:
        if 'jina-crawler-mcp:8009' in connection['url']:
            old_url = connection['url']
            connection['url'] = 'http://localhost:8009'
            print(f"Updated URL: {old_url} -> {connection['url']}")
            break
    else:
        # If not found, add new connection
        new_connection = {
            'url': 'http://localhost:8009',
            'path': 'openapi.json',
            'auth_type': 'bearer',
            'key': '',
            'config': {'enable': True, 'access_control': None},
            'info': {
                'name': 'Jini Crawler',
                'description': 'Advanced web crawling with AI summarization and search'
            }
        }
        connections.append(new_connection)
        print("Added new jini_crawler connection")
    
    return config_data

def save_updated_config(config_data):
    """Save updated config back to database"""
    
    config_json = json.dumps(config_data).replace("'", "''")  # Escape single quotes
    
    cmd = [
        "docker", "exec", "open-webui-mcpo", "python3", "-c",
        f"""
import sqlite3
import json

config_data = json.loads('''{config_json}''')

conn = sqlite3.connect('/app/backend/data/webui.db')
cursor = conn.cursor()

# Get current version
cursor.execute('SELECT version FROM config ORDER BY version DESC LIMIT 1')
current_version = cursor.fetchone()[0] if cursor.fetchone() else 0

# Insert new config with incremented version
new_version = current_version + 1
cursor.execute(
    'INSERT INTO config (data, version) VALUES (?, ?)',
    (json.dumps(config_data), new_version)
)

conn.commit()
conn.close()

print(f"Config updated to version {{new_version}}")
        """
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ Config updated successfully!")
        print(result.stdout)
        return True
    else:
        print(f"❌ Error updating config: {result.stderr}")
        return False

def restart_openwebui():
    """Restart OpenWebUI container to apply changes"""
    
    print("🔄 Restarting OpenWebUI container...")
    
    result = subprocess.run(["docker", "restart", "open-webui-mcpo"], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ OpenWebUI restarted successfully!")
        return True
    else:
        print(f"❌ Error restarting OpenWebUI: {result.stderr}")
        return False

def test_jini_crawler():
    """Test if jini_crawler is accessible"""
    
    import requests
    
    try:
        response = requests.get("http://localhost:8009/health", timeout=5)
        if response.status_code == 200:
            print("✅ jini_crawler is accessible at localhost:8009")
            return True
        else:
            print(f"❌ jini_crawler returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to jini_crawler: {e}")
        return False

def main():
    print("🔧 Fixing jini_crawler connection in OpenWebUI...")
    
    # Test jini_crawler first
    if not test_jini_crawler():
        print("Please make sure jini_crawler is running on port 8009")
        return
    
    # Get current config
    print("📖 Getting current OpenWebUI config...")
    config_data = get_current_config()
    
    if not config_data:
        print("❌ Failed to get current config")
        return
    
    # Update jini_crawler URL
    print("🔄 Updating jini_crawler URL...")
    updated_config = update_jini_crawler_url(config_data)
    
    # Save updated config
    print("💾 Saving updated config...")
    if save_updated_config(updated_config):
        
        # Restart OpenWebUI
        if restart_openwebui():
            print("\n🎉 Fix completed successfully!")
            print("\n📋 Next steps:")
            print("1. Wait for OpenWebUI to restart (30 seconds)")
            print("2. Open OpenWebUI in browser: http://localhost:3000")
            print("3. Check if jini_crawler tools appear in chat")
            print("4. Test with: 'Crawl this website: https://vnexpress.net'")
        else:
            print("❌ Failed to restart OpenWebUI")
    else:
        print("❌ Failed to save config")

if __name__ == "__main__":
    main()
