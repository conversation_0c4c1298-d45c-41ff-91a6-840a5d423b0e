#!/usr/bin/env python3
"""
Delete all Qdrant collections with 3072 dimensions
"""

from qdrant_client import QdrantClient

def delete_3072_dimension_collections():
    """Delete all Qdrant collections with 3072 dimensions"""
    
    try:
        client = QdrantClient(host='localhost', port=6333)
        
        # Get all collections
        collections = client.get_collections()
        print("🗂️  Available collections:")
        
        deleted_collections = []
        
        for collection in collections.collections:
            collection_name = collection.name
            print(f"\n📁 Checking collection: {collection_name}")
            
            try:
                # Get collection info
                info = client.get_collection(collection_name)
                vector_size = info.config.params.vectors.size
                
                print(f"   📏 Vector dimensions: {vector_size}")
                
                # Check if dimension is 3072
                if vector_size == 3072:
                    print(f"   🗑️  Deleting collection with 3072 dimensions...")
                    
                    # Delete the collection
                    client.delete_collection(collection_name)
                    deleted_collections.append(collection_name)
                    print(f"   ✅ Successfully deleted: {collection_name}")
                else:
                    print(f"   ℹ️  Skipping collection (not 3072 dimensions)")
                    
            except Exception as e:
                print(f"   ❌ Error processing collection {collection_name}: {e}")
                
        print(f"\n🎉 Deletion process completed!")
        print(f"🗑️  Deleted {len(deleted_collections)} collections with 3072 dimensions:")
        for collection in deleted_collections:
            print(f"   - {collection}")
                
    except Exception as e:
        print(f"❌ Error connecting to Qdrant: {e}")
        print("💡 Make sure Qdrant is running on localhost:6333")

if __name__ == "__main__":
    delete_3072_dimension_collections()