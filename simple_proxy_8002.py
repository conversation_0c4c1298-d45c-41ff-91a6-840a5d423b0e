#!/usr/bin/env python3
"""
Simple proxy server for port 8002 that forwards to working container at 8009
This avoids the complex crawler issues while maintaining compatibility
"""

import asyncio
import aiohttp
import json
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
import uvicorn
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Jina Crawler Simple Proxy",
    description="Simple proxy forwarding to working container at 8009",
    version="1.0.0"
)

# Target service URL
TARGET_SERVICE = "http://localhost:8009"

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TARGET_SERVICE}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    return {"status": "healthy", "proxy": "8002->8009", "target_status": "healthy"}
                else:
                    return {"status": "degraded", "proxy": "8002->8009", "target_status": f"HTTP {response.status}"}
    except Exception as e:
        return {"status": "unhealthy", "proxy": "8002->8009", "error": str(e)}

@app.post("/jina_crawler/ai_search")
async def ai_search_proxy(request: Request):
    """Proxy ai_search requests to the working container at 8009"""
    try:
        # Get request body
        body = await request.body()
        
        # Forward to target service
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{TARGET_SERVICE}/ai_search",
                data=body,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=120)  # 2 minutes for search
            ) as response:
                result = await response.json()
                return JSONResponse(content=result, status_code=response.status)
                
    except asyncio.TimeoutError:
        logger.error("❌ Timeout forwarding to target service")
        raise HTTPException(status_code=504, detail="Gateway timeout")
    except Exception as e:
        logger.error(f"❌ Error forwarding request: {e}")
        raise HTTPException(status_code=502, detail=f"Proxy error: {str(e)}")

@app.get("/")
async def root():
    """Root endpoint showing available tools"""
    return {
        "service": "Jina Crawler Simple Proxy",
        "version": "1.0.0",
        "proxy_target": TARGET_SERVICE,
        "available_endpoints": [
            "/jina_crawler/ai_search"
        ],
        "note": "search_web endpoint has been deprecated - use ai_search instead"
    }

if __name__ == "__main__":
    logger.info("🚀 Starting Simple Proxy Server on port 8002")
    logger.info(f"🔗 Forwarding to target service: {TARGET_SERVICE}")
    logger.info("📋 Available endpoint: /jina_crawler/ai_search")
    logger.info("⚠️ search_web endpoint deprecated")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        log_level="info"
    )
