#!/usr/bin/env python3
"""
Simple Re-embed Script: Add a few memories to test 768D collection
"""

import os
import asyncio
import requests
from mem0 import AsyncMemory

async def simple_reembed():
    print("🚀 Starting simple re-embedding test...")
    
    # Set environment variables
    os.environ['OPENAI_BASE_URL'] = 'https://generativelanguage.googleapis.com/v1beta/openai/'
    os.environ['OPENAI_API_KEY'] = 'AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec'
    
    print("✅ Environment variables set")
    
    # Get a few sample memories from source collection
    print("📤 Getting sample memories from source collection...")
    
    try:
        response = requests.post(
            'http://qdrant:6333/collections/mem0_gemini_3072_fixed/points/scroll',
            json={'limit': 5, 'with_payload': True, 'with_vector': False}
        )
        
        if response.status_code == 200:
            data = response.json()
            points = data['result']['points']
            print(f"✅ Got {len(points)} sample memories")
            
            # Setup memory client
            print("📦 Setting up memory client...")
            
            config = {
                'vector_store': {
                    'provider': 'qdrant',
                    'config': {
                        'host': 'qdrant',
                        'port': 6333,
                        'collection_name': 'mem0_openai_compatible_768'
                    }
                },
                'llm': {
                    'provider': 'openai',
                    'config': {
                        'model': 'gemini-2.5-flash',
                        'temperature': 0.1,
                        'max_tokens': 1000
                    }
                },
                'embedder': {
                    'provider': 'gemini',
                    'config': {
                        'api_key': 'AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec',
                        'model': 'text-embedding-004',
                        'embedding_dims': 768
                    }
                }
            }
            
            memory = await AsyncMemory.from_config(config)
            print("✅ Memory client created")
            
            # Re-embed sample memories
            for i, point in enumerate(points):
                try:
                    content = point['payload'].get('content', '')
                    user_id = point['payload'].get('user_id', 'default_user')
                    
                    if content:
                        print(f"🔄 Re-embedding memory {i+1}/{len(points)}: {content[:50]}...")
                        
                        result = await memory.add(
                            messages=content,
                            user_id=user_id,
                            metadata={
                                'original_id': point['id'],
                                'reembedded_768d': True,
                                'test_batch': True
                            }
                        )
                        
                        print(f"✅ Memory {i+1} re-embedded successfully")
                        
                        # Rate limiting
                        if i < len(points) - 1:
                            print("⏳ Waiting 2 seconds...")
                            await asyncio.sleep(2)
                    
                except Exception as e:
                    print(f"❌ Error re-embedding memory {i+1}: {e}")
                    continue
            
            # Check collection stats
            print("\n📊 Checking collection stats...")
            stats_response = requests.get('http://qdrant:6333/collections/mem0_openai_compatible_768')
            if stats_response.status_code == 200:
                stats = stats_response.json()['result']
                print(f"✅ Collection now has {stats['points_count']} memories")
                print(f"📏 Vector dimensions: {stats['config']['params']['vectors']['size']}")
            
            print("\n🎉 Simple re-embedding test completed!")
            print("💡 OpenAI-compatible endpoint works perfectly for re-embedding")
            
        else:
            print(f"❌ Error getting source memories: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_reembed())
