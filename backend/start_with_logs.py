#!/usr/bin/env python3
"""
Start Backend với Live Logging
"""
import sys
import os
import uvicorn
import logging
from pathlib import Path

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(name)s | %(levelname)s | %(message)s',
    handlers=[
        logging.FileHandler('backend.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    logger.info("🚀 Starting Enhanced Backend Server...")
    logger.info("📍 URL: http://localhost:8081")
    logger.info("📋 Live logging enabled")
    logger.info("=" * 50)
    
    # Start with uvicorn
    uvicorn.run(
        "run_server:app",
        host="0.0.0.0",
        port=8081,
        reload=False,
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    main() 