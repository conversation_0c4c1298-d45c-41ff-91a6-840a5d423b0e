fastapi>=0.68.0,<0.69.0
uvicorn>=0.15.0,<0.16.0
pydantic>=1.8.0,<2.0.0
pydantic-settings>=2.0.0
python-dotenv>=0.19.0
PyYAML>=6.0.0
mem0ai>=0.1.0
openai>=1.0.0
python-multipart>=0.0.5
requests>=2.26.0
aiofiles>=0.7.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
tenacity>=8.0.1
tiktoken>=0.5.1

# Memory Management
mem0ai>=0.1.113
sentence-transformers>=2.2.0
transformers>=4.30.0

# RAG Dependencies
langchain-text-splitters>=0.2.0
PyMuPDF>=1.23.0
python-docx>=1.1.0
openpyxl>=3.1.0
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
numpy>=1.24.0

Pillow

# Open WebUI RAG Enhancement Dependencies
rank-bm25>=0.2.2
transformers>=4.30.0
chromadb>=0.4.15
pypdf>=3.15.0
markdown>=3.4.4
beautifulsoup4>=4.12.0
html2text>=2020.1.16
youtube-transcript-api>=0.6.0

# Advanced RAG Components
torch>=2.0.0
tokenizers>=0.13.0
datasets>=2.14.0

# Document Processing Enhancement  
python-magic>=0.4.27
filetype>=1.2.0
chardet>=5.2.0
tesserocr>=2.5.2
rapidocr-onnxruntime>=1.3.2

# Performance & Monitoring
psutil>=5.9.0
