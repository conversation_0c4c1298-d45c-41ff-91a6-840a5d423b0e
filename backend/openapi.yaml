openapi: 3.0.0
info:
  title: Memory Tool Server
  description: OpenAPI Tool Server for memory management in AI applications
  version: 1.0.0
  
servers:
  - url: http://localhost:8011
    description: Local development server
  - url: http://**************:8011
    description: Remote development server

paths:
  /api/memory/settings:
    get:
      operationId: getMemorySettings
      summary: Get memory settings
      responses:
        '200':
          description: Memory settings retrieved successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorySettings'
    post:
      operationId: updateMemorySettings
      summary: Update memory settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemorySettings'
      responses:
        '200':
          description: Settings updated successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorySettings'
          
  /api/memory/settings/test:
    post:
      operationId: testMemorySettings
      summary: Test memory settings
      responses:
        '200':
          description: Settings tested successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string

  /api/models/settings:
    get:
      operationId: getModelSettings
      summary: Get model settings
      responses:
        '200':
          description: Model settings retrieved successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ModelSettings'
    post:
      operationId: updateModelSettings
      summary: Update model settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ModelSettings'
      responses:
        '200':
          description: Model settings updated successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ModelSettings'

  /api/models/settings/test:
    post:
      operationId: testModelConnection
      summary: Test connection to a model endpoint
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                endpoint:
                  type: string
                  description: The model endpoint URL
                api_key:
                  type: string
                  description: API key for the endpoint
                model:
                  type: string
                  description: Model identifier
              required:
                - endpoint
                - model
      responses:
        '200':
          description: Connection test result
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /api/performance/settings:
    get:
      operationId: getPerformanceSettings
      summary: Get performance settings
      responses:
        '200':
          description: Performance settings retrieved successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceSettings'
    post:
      operationId: updatePerformanceSettings
      summary: Update performance settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PerformanceSettings'
      responses:
        '200':
          description: Performance settings updated successfully
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: string
            Access-Control-Allow-Credentials:
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceSettings'

components:
  schemas:
    MemorySettings:
      type: object
      properties:
        enabled:
          type: boolean
          description: Whether memory service is enabled
        openai_api_key:
          type: string
          description: OpenAI API key for embeddings
        max_memories:
          type: integer
          description: Maximum number of memories to store
        memories_per_query:
          type: integer
          description: Number of memories to return per query
        memory_ttl:
          type: integer
          description: Time-to-live for memories in seconds
        min_relevance_score:
          type: number
          description: Minimum relevance score for memory retrieval
      required:
        - enabled
        - max_memories
        - memories_per_query
        - memory_ttl
        - min_relevance_score

    ModelSettings:
      type: object
      properties:
        selected:
          type: string
          description: Selected model identifier
          enum: [ultra-fast, fast, balanced, premium]
        context_length:
          type: integer
          description: Context length in tokens
          minimum: 1024
          maximum: 32768
        max_tokens:
          type: integer
          description: Maximum tokens for generation
          minimum: 256
          maximum: 4096
      required:
        - selected
        - context_length
        - max_tokens

    PerformanceSettings:
      type: object
      properties:
        max_concurrent_uploads:
          type: integer
          description: Maximum number of concurrent uploads
          minimum: 1
          maximum: 20
        max_concurrent_searches:
          type: integer
          description: Maximum number of concurrent searches
          minimum: 1
          maximum: 50
        max_workers:
          type: integer
          description: Maximum number of worker threads
          minimum: 1
          maximum: 8
        max_memory_usage:
          type: integer
          description: Maximum memory usage in MB
          minimum: 512
          maximum: 8192
        preset:
          type: string
          description: Performance preset configuration
          enum: [development, production, high_performance, high_quality]
      required:
        - max_concurrent_uploads
        - max_concurrent_searches
        - max_workers
        - max_memory_usage
        - preset 