import os
import json
import time
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
import google.generativeai as genai

app = FastAPI()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# OAuth2 configuration
OAUTH_CREDS_PATH = os.path.expanduser("~/.gemini/oauth_creds.json")

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: list[ChatMessage]
    stream: Optional[bool] = False
    temperature: Optional[float] = 0.7

def get_credentials():
    try:
        with open(OAUTH_CREDS_PATH) as f:
            creds_data = json.load(f)
            creds = Credentials(
                token=creds_data['access_token'],
                refresh_token=creds_data['refresh_token'],
                token_uri="https://oauth2.googleapis.com/token",
                client_id="681255809395-oo8ft2oprdrnp9e3aqf6av3hmdib135j.apps.googleusercontent.com",
                client_secret="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",
                scopes=creds_data['scope'].split()
            )
            if creds.expired:
                creds.refresh(Request())
            return creds
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"OAuth error: {str(e)}")

def configure_genai():
    creds = get_credentials()
    genai.configure(credentials=creds)

@app.get("/health")
async def health_check():
    try:
        configure_genai()
        model = genai.GenerativeModel('gemini-pro')
        response = model.generate_content("test")
        return {"status": "ok", "message": "API connection working"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/v1/models")
async def list_models():
    return {
        "data": [
            {
                "id": "gemini-pro",
                "object": "model",
                "created": **********,
                "owned_by": "google"
            }
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    if request.stream:
        return StreamingResponse(
            generate_stream(request),
            media_type='text/event-stream'
        )
    
    try:
        configure_genai()
        model = genai.GenerativeModel('gemini-pro')
        
        # Convert messages to prompt
        messages = request.messages
        prompt = "\n".join([f"{m.role}: {m.content}" for m in messages])
        
        response = model.generate_content(prompt)
        
        return {
            "id": "chatcmpl-" + os.urandom(12).hex(),
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "gemini-pro",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response.text
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": -1,
                "completion_tokens": -1,
                "total_tokens": -1
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def generate_stream(request: ChatCompletionRequest):
    try:
        configure_genai()
        model = genai.GenerativeModel('gemini-pro')
        
        messages = request.messages
        prompt = "\n".join([f"{m.role}: {m.content}" for m in messages])
        
        response = model.generate_content(prompt, stream=True)
        
        for chunk in response:
            yield f"data: {json.dumps({
                'id': 'chatcmpl-' + os.urandom(12).hex(),
                'object': 'chat.completion.chunk',
                'created': int(time.time()),
                'model': 'gemini-pro',
                'choices': [{
                    'index': 0,
                    'delta': {
                        'content': chunk.text
                    },
                    'finish_reason': None
                }]
            })}\n\n"
        
        # Send final chunk
        yield f"data: {json.dumps({
            'id': 'chatcmpl-' + os.urandom(12).hex(),
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': 'gemini-pro',
            'choices': [{
                'index': 0,
                'delta': {},
                'finish_reason': 'stop'
            }]
        })}\n\n"
        
        yield "data: [DONE]\n\n"
        
    except Exception as e:
        yield f"data: {json.dumps({'error': str(e)})}\n\n"

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8010) 