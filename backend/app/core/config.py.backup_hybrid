from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import List

class Settings(BaseSettings):
    APP_NAME: str = "AI Assistant Bot"
    APP_VERSION: str = "0.1.0"
    LOG_LEVEL: str = "INFO"

    # External LLM API Keys
    OPENAI_API_KEY: str | None = None
    GEMINI_API_KEY: str | None = None
    DEEPSEEK_API_KEY: str | None = None
    
    # HuggingFace token for gated models
    HF_TOKEN: str | None = None

    # Gemma 3 Local Model Configuration
    GEMMA_MODEL_ID: str | None = "google/gemma-2b-it"
    GEMMA_MODEL_PATH: str | None = None 
    GEMMA_DEVICE: str = "cpu"
    GEMMA_DTYPE: str = "auto"
    
    # Gemma 3 Model Configuration (Disabled due to transformers compatibility issues)
    GEMMA_3_4B_MODEL_ID: str | None = None
    GEMMA_3_4B_MODEL_PATH: str | None = None
    GEMMA_3_4B_DEVICE: str = "cpu"
    GEMMA_3_4B_DTYPE: str = "auto"

    # RAG Configuration
    RAG_FAISS_INDEX_PATH: str = "./data/faiss_index/index.faiss" # Path to save/load FAISS index
    RAG_DOCUMENTS_DIR: str = "./data/documents" # Directory to store uploaded documents for reference
    RAG_EMBEDDING_MODEL_NAME: str = "all-MiniLM-L6-v2" # Hugging Face model for sentence-transformers
    RAG_CHUNK_SIZE: int = 512
    RAG_CHUNK_OVERLAP: int = 64
    RAG_TOP_K_RESULTS: int = 3 # Number of relevant chunks to retrieve
    RAG_SUPPORTED_FILE_TYPES: List[str] = ["pdf", "docx", "txt", "xlsx", "xls"]

    # Open WebUI RAG Configuration
    OPENWEBUI_RAG_EMBEDDING_MODEL: str = "sentence-transformers/all-MiniLM-L6-v2"
    OPENWEBUI_RAG_RERANK_MODEL: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    OPENWEBUI_RAG_CHUNK_SIZE: int = 512
    OPENWEBUI_RAG_CHUNK_OVERLAP: int = 64
    OPENWEBUI_RAG_TOP_K: int = 3
    OPENWEBUI_RAG_SIMILARITY_THRESHOLD: float = 0.65
    OPENWEBUI_RAG_USE_HYBRID_SEARCH: bool = True
    OPENWEBUI_RAG_USE_RERANKING: bool = False
    OPENWEBUI_RAG_FULL_CONTEXT_MODE: bool = False
    OPENWEBUI_RAG_COLLECTIONS_DIR: str = "./data/openwebui_collections"
    OPENWEBUI_RAG_SUPPORTED_TYPES: List[str] = ["pdf", "docx", "doc", "xlsx", "xls", "txt", "md", "markdown", "html", "htm"]
    
    # Web Content RAG
    RAG_WEB_REQUEST_TIMEOUT: int = 10
    RAG_WEB_MAX_CONTENT_LENGTH: int = 50000

    # AI Edge Provider Settings
    AI_EDGE_MODELS_DIR: str = "backend/app/models/tflite/gemma3n"
    AI_EDGE_BASE_URL: str = "http://localhost:11435"
    AI_EDGE_ENABLE_MULTIMODAL: bool = True
    AI_EDGE_DEFAULT_CONTEXT_LENGTH: int = 32768
    AI_EDGE_DEFAULT_MAX_TOKENS: int = 4096

    # Open WebUI Integration
    OPENWEBUI_AI_EDGE_ENDPOINT: str = "http://localhost:8010/ai-edge"

    model_config = SettingsConfigDict(env_file=".env", env_file_encoding='utf-8', extra='ignore')

settings = Settings() 