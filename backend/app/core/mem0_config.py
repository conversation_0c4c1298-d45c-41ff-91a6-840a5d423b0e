from pydantic import BaseModel
from typing import Optional, Dict, Any, Literal

class EmbeddingProviderConfig(BaseModel):
    provider: Literal["openai", "jina", "google_ai"] = "openai"
    model: str = "text-embedding-3-small"
    api_key: Optional[str] = None
    dimensions: Optional[int] = None
    task: Optional[str] = None  # For Jina v3 models

class Mem0Config(BaseModel):
    enabled: bool = True
    max_memories: int = 1000
    memory_ttl_days: Optional[int] = None  # None means no expiration
    memories_per_query: int = 3
    min_relevance_score: float = 0.7
    
    # Legacy OpenAI support (deprecated)
    openai_api_key: Optional[str] = None
    
    # New embedding provider configuration
    embedding_provider: Optional[EmbeddingProviderConfig] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "enabled": True,
                "max_memories": 1000,
                "memory_ttl_days": 30,
                "memories_per_query": 3,
                "min_relevance_score": 0.7,
                "embedding_provider": {
                    "provider": "jina",
                    "model": "jina-embeddings-v3",
                    "api_key": "your_jina_api_key",
                    "task": "text-matching",
                    "dimensions": 1024
                }
            }
        } 