"""
Jan-nano Client for llama.cpp Server
OpenAI-compatible API client for Jan-nano running on llama.cpp
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
import httpx
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JanNanoLlamaCppClient:
    """Jan-nano client for llama.cpp server with OpenAI-compatible API"""
    
    def __init__(self, base_url: str = "http://localhost:11435"):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/v1"
        self.model_name = "jan-nano"
        self.timeout = 180
        
        # Vietnamese-optimized parameters
        self.default_params = {
            "temperature": 0.7,
            "top_p": 0.8,
            "top_k": 20,
            "max_tokens": 2048,
            "stop": ["<end_of_turn>", "</s>", "<|endoftext|>"],
            "repeat_penalty": 1.1
        }
        
        self.client = httpx.AsyncClient(timeout=self.timeout)
        
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def _check_server_health(self) -> bool:
        """Check if llama.cpp server is accessible"""
        try:
            response = await self.client.get(f"{self.api_base}/models")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Server health check failed: {e}")
            return False
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        try:
            response = await self.client.get(f"{self.api_base}/models")
            response.raise_for_status()
            
            data = response.json()
            return data.get('models', [])
            
        except Exception as e:
            logger.error(f"Failed to get models: {e}")
            return []
    
    async def _check_model_availability(self) -> bool:
        """Check if Jan-nano model is available"""
        try:
            models = await self.get_available_models()
            model_names = [model.get('name', '').lower() for model in models]
            
            # Check for jan-nano or similar names
            jan_nano_available = any(
                'jan' in name and 'nano' in name 
                for name in model_names
            )
            
            if jan_nano_available:
                logger.info("✅ Jan-nano model found in llama.cpp server")
                return True
            else:
                logger.warning(f"⚠️  Jan-nano not found. Available models: {model_names}")
                return False
                
        except Exception as e:
            logger.error(f"Model availability check failed: {e}")
            return False
    
    async def completion(
        self,
        prompt: str,
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate completion using llama.cpp /v1/completions endpoint"""
        
        # Merge parameters
        params = {**self.default_params, **kwargs}
        
        payload = {
            "model": model or self.model_name,
            "prompt": prompt,
            "stream": stream,
            **params
        }
        
        try:
            if stream:
                return await self._stream_completion(payload)
            else:
                response = await self.client.post(
                    f"{self.api_base}/completions",
                    json=payload
                )
                response.raise_for_status()
                return response.json()
                
        except Exception as e:
            logger.error(f"Completion failed: {e}")
            return {
                "choices": [{"text": f"Error: {str(e)}"}],
                "error": str(e)
            }
    
    async def _stream_completion(self, payload: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion responses"""
        try:
            async with self.client.stream(
                "POST",
                f"{self.api_base}/completions",
                json=payload
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            yield data
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Stream completion failed: {e}")
            yield {"error": str(e)}
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate chat completion using /v1/chat/completions endpoint"""
        
        # Convert messages to prompt for completion API if chat API not available
        prompt = self._messages_to_prompt(messages)
        return await self.completion(prompt, model, stream, **kwargs)
    
    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI messages format to prompt for Jan-nano"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"<start_of_turn>user\n{content}<end_of_turn>")
            elif role == "assistant":
                prompt_parts.append(f"<start_of_turn>model\n{content}<end_of_turn>")
        
        # Add model turn start
        prompt_parts.append("<start_of_turn>model")
        
        return "\n".join(prompt_parts)
    
    async def generate_with_context(
        self,
        question: str,
        context: str,
        model: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate answer with Vietnamese RAG context"""
        
        # Vietnamese-optimized prompt template
        prompt = f"""<start_of_turn>user
Dựa vào thông tin sau đây, hãy trả lời câu hỏi một cách chính xác và chi tiết:

THÔNG TIN THAM KHẢO:
{context}

CÂU HỎI: {question}

Hãy trả lời bằng tiếng Việt, sử dụng thông tin từ tài liệu tham khảo.<end_of_turn>
<start_of_turn>model"""
        
        try:
            result = await self.completion(
                prompt=prompt,
                model=model,
                **kwargs
            )
            
            if "choices" in result and result["choices"]:
                return result["choices"][0].get("text", "").strip()
            else:
                return "Không thể tạo phản hồi."
                
        except Exception as e:
            logger.error(f"Context generation failed: {e}")
            return f"Lỗi: {str(e)}"
    
    async def function_calling(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Simulate function calling with Jan-nano"""
        
        # Create function calling prompt
        functions_desc = "\n".join([
            f"- {func['name']}: {func.get('description', '')}" 
            for func in functions
        ])
        
        function_prompt = f"""<start_of_turn>user
Available functions:
{functions_desc}

User request: {prompt}

If you need to call a function, respond in this format:
FUNCTION_CALL: {{
  "name": "function_name",
  "arguments": {{"param": "value"}}
}}

Otherwise, respond normally.<end_of_turn>
<start_of_turn>model"""
        
        result = await self.completion(
            prompt=function_prompt,
            model=model,
            **kwargs
        )
        
        response_text = ""
        if "choices" in result and result["choices"]:
            response_text = result["choices"][0].get("text", "")
        
        # Parse function call if present
        if "FUNCTION_CALL:" in response_text:
            try:
                json_start = response_text.find("{")
                json_end = response_text.rfind("}") + 1
                
                if json_start >= 0 and json_end > json_start:
                    function_call = json.loads(response_text[json_start:json_end])
                    return {
                        "type": "function_call",
                        "function_call": function_call,
                        "raw_response": response_text
                    }
            except json.JSONDecodeError:
                pass
        
        return {
            "type": "text_response",
            "content": response_text,
            "raw_response": response_text
        }
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get Jan-nano model information"""
        try:
            models = await self.get_available_models()
            
            for model in models:
                if 'jan' in model.get('name', '').lower():
                    return {
                        "name": model.get('name', 'jan-nano'),
                        "status": "available",
                        "provider": "llama.cpp",
                        "endpoint": self.api_base,
                        "capabilities": ["text_generation", "tool_use", "vietnamese"],
                        "parameters": self.default_params,
                        "details": model
                    }
            
            return {
                "name": "jan-nano",
                "status": "not_found",
                "provider": "llama.cpp",
                "endpoint": self.api_base,
                "error": "Jan-nano model not found in server"
            }
            
        except Exception as e:
            return {
                "name": "jan-nano",
                "status": "error",
                "provider": "llama.cpp",
                "error": str(e)
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        health_info = {
            "server_accessible": False,
            "model_available": False,
            "api_functional": False,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # Check server
            health_info["server_accessible"] = await self._check_server_health()
            
            if health_info["server_accessible"]:
                # Check model
                health_info["model_available"] = await self._check_model_availability()
                
                if health_info["model_available"]:
                    # Test API
                    test_result = await self.completion(
                        "Test", 
                        max_tokens=10
                    )
                    health_info["api_functional"] = "choices" in test_result
        
        except Exception as e:
            health_info["error"] = str(e)
        
        health_info["overall_status"] = (
            "healthy" if all([
                health_info["server_accessible"],
                health_info["model_available"], 
                health_info["api_functional"]
            ]) else "unhealthy"
        )
        
        return health_info

# Demo function
async def demo_jan_nano_llamacpp():
    """Demo Jan-nano with llama.cpp"""
    client = JanNanoLlamaCppClient()
    
    try:
        print("🚀 Jan-nano llama.cpp Client Demo")
        print("=" * 40)
        
        # Health check
        print("\n📊 Health Check:")
        health = await client.health_check()
        print(f"Status: {health['overall_status']}")
        
        if health['overall_status'] != 'healthy':
            print("❌ Jan-nano not ready. Please check setup.")
            return
        
        # Basic completion
        print("\n💬 Basic Chat Test:")
        response = await client.completion(
            "Xin chào! Bạn có thể giúp tôi gì?",
            max_tokens=100
        )
        
        if "choices" in response:
            print(f"Jan-nano: {response['choices'][0]['text']}")
        
        print("\n✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(demo_jan_nano_llamacpp()) 