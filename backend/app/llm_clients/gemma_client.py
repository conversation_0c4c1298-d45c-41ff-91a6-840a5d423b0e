import logging
import torch
import os
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, BitsAndBytesConfig, GemmaForCausalLM, GemmaTokenizer
from PIL import Image # For multimodal input later
from core.config import settings
import asyncio

# Optimize PyTorch for multi-core ARM64
torch.set_num_threads(os.cpu_count())  # Use all available CPU cores
torch.set_num_interop_threads(os.cpu_count())  # Optimize inter-op parallelism

logger = logging.getLogger(__name__)

class GemmaLocalClient:
    _instance = None
    _model = None
    _tokenizer = None
    _device = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(GemmaLocalClient, cls).__new__(cls)
            # Initialization logic will be called only once
            cls._initialize_model()
        return cls._instance

    @classmethod
    def _initialize_model(cls):
        if not settings.GEMMA_MODEL_ID and not settings.GEMMA_MODEL_PATH:
            logger.warning("Gemma model ID or path is not configured. GemmaLocalClient will not be available.")
            return

        cls._device = settings.GEMMA_DEVICE
        model_identifier = settings.GEMMA_MODEL_PATH if settings.GEMMA_MODEL_PATH else settings.GEMMA_MODEL_ID
        dtype_str = getattr(settings, 'GEMMA_DTYPE', "auto") # Default to auto if not set
        
        # Get HuggingFace token from environment
        hf_token = os.getenv('HF_TOKEN') or getattr(settings, 'HF_TOKEN', None)
        
        dtype_map = {
            "float16": torch.float16,
            "bfloat16": torch.bfloat16,
            "float32": torch.float32,
            "auto": "auto"
        }
        torch_dtype = dtype_map.get(dtype_str, "auto")

        logger.info(f"Initializing Gemma model: {model_identifier} on device: {cls._device} with dtype: {dtype_str}")
        logger.info(f"PyTorch threads: {torch.get_num_threads()}, CPU cores: {os.cpu_count()}")
        if hf_token:
            logger.info("Using HuggingFace token for model access")

        try:
            # Note: Quantization (BitsAndBytesConfig) is highly experimental on ARM and might fail or not be supported.
            # For initial setup, especially on CPU, it's safer to avoid it or use model-specific quantization if available.
            # quantization_config = BitsAndBytesConfig(load_in_4bit=True, bnb_4bit_compute_dtype=torch.bfloat16) if cls._device == "cuda" else None
            
            # Using GemmaTokenizer specifically if available and recommended for Gemma models
            try:
                cls._tokenizer = GemmaTokenizer.from_pretrained(
                    model_identifier,
                    token=hf_token  # Add token for gated models
                )
            except Exception:
                logger.warning(f"Failed to load GemmaTokenizer for {model_identifier}, trying AutoTokenizer.")
                cls._tokenizer = AutoTokenizer.from_pretrained(
                    model_identifier,
                    token=hf_token  # Add token for gated models
                )

            # Using GemmaForCausalLM specifically if available for better compatibility
            try:
                cls._model = GemmaForCausalLM.from_pretrained(
                    model_identifier,
                    # quantization_config=quantization_config, # Disabled for now for broader compatibility
                    torch_dtype=torch_dtype,
                    low_cpu_mem_usage=True if cls._device == "cpu" else False, # Helps with large models on CPU
                    token=hf_token,  # Add token for gated models
                    # device_map=cls._device # device_map="auto" can be useful but explicit is often better
                ).to(cls._device)
            except Exception as e_gemma_model:
                logger.warning(f"Failed to load GemmaForCausalLM for {model_identifier} (Error: {e_gemma_model}), trying AutoModelForCausalLM.")
                cls._model = AutoModelForCausalLM.from_pretrained(
                    model_identifier,
                    torch_dtype=torch_dtype,
                    low_cpu_mem_usage=True if cls._device == "cpu" else False,
                    token=hf_token,  # Add token for gated models
                    # device_map=cls._device
                ).to(cls._device)

            cls._model.eval() # Set model to evaluation mode
            logger.info(f"Gemma model {model_identifier} loaded successfully on {cls._device}.")

        except Exception as e:
            cls._model = None # Ensure model is None if loading fails
            cls._tokenizer = None
            logger.error(f"Failed to load Gemma model {model_identifier}: {e}", exc_info=True)
            # raise RuntimeError(f"Could not initialize Gemma model: {e}") # Optionally re-raise

    @property
    def is_available(self) -> bool:
        """Check if the model and tokenizer are loaded."""
        return self._model is not None and self._tokenizer is not None

    async def generate_text_response(self, prompt: str, max_new_tokens: int = 250) -> str | None:
        if not self.is_available:
            logger.error("Gemma model is not available for text generation.")
            return "Error: Gemma model is not available."

        logger.info(f"Sending text request to local Gemma model: {prompt[:50]}...")
        try:
            # Run model inference in a separate thread to avoid blocking FastAPI's event loop
            def _inference():
                inputs = self._tokenizer(prompt, return_tensors="pt").to(self._device)
                with torch.no_grad(): # Ensure no gradients are computed during inference
                    # Optimized generation parameters for speed
                    outputs = self._model.generate(
                        **inputs, 
                        max_new_tokens=max_new_tokens,
                        do_sample=True, 
                        temperature=0.7,
                        top_k=40,  # Reduced from 50 for speed
                        top_p=0.9,  # Reduced from 0.95 for speed
                        repetition_penalty=1.1,
                        pad_token_id=self._tokenizer.eos_token_id,
                        num_beams=1,  # Use greedy decoding for speed
                        early_stopping=True
                    )
                return self._tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            response_text = await asyncio.to_thread(_inference)
            
            # Remove the original prompt from response (Gemma often repeats it)
            if response_text.startswith(prompt):
                response_text = response_text[len(prompt):].strip()
            
            logger.info(f"Received text response from local Gemma: {response_text[:50]}...")
            return response_text
        except Exception as e:
            logger.error(f"Error during Gemma text generation: {e}", exc_info=True)
            return f"Error during Gemma text generation: {str(e)}"

    async def generate_multimodal_response(self, text_prompt: str, image: Image.Image, max_new_tokens: int = 250) -> str | None:
        if not self.is_available:
            logger.error("Gemma model is not available for multimodal generation.")
            return "Error: Gemma model is not available or not multimodal."
        
        # This is a placeholder. Gemma's multimodal capabilities (e.g., PaliGemma) require specific model loading
        # and input processing (text + image pixels). The `google/gemma-2b-it` is text-only.
        # If you switch to a multimodal Gemma like PaliGemma, the _initialize_model and this method will need changes.
        # For example, PaliGemma uses a specific processor: PaliGemmaProcessor
        logger.info(f"Sending multimodal request to local Gemma: {text_prompt[:50]}... with image.")
        try:
            # Placeholder for actual multimodal processing with a capable Gemma model
            # Example for a hypothetical multimodal Gemma model that takes text and PIL image:
            # def _inference():
            #     inputs = self._processor(text=text_prompt, images=image, return_tensors="pt").to(self._device)
            #     with torch.no_grad():
            #         generated_ids = self._model.generate(**inputs, max_new_tokens=max_new_tokens)
            #     return self._processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            # response_text = await asyncio.to_thread(_inference)
            
            logger.warning("Multimodal generation with current Gemma model is a placeholder and likely won't work as expected.")
            response_text = f"Placeholder response for multimodal prompt: '{text_prompt}' with an image. Current model may not support this."
            return response_text

        except Exception as e:
            logger.error(f"Error during Gemma multimodal generation: {e}", exc_info=True)
            return f"Error during Gemma multimodal generation: {str(e)}"

# Instantiate the client (singleton). Model loading will happen here if not already loaded.
# This might take time on first startup if the model needs to be downloaded/loaded.
# Consider lazy loading or background loading for production apps if startup time is critical.
# gemma_local_client = GemmaLocalClient()

# For testing the client directly:
if __name__ == '''__main__''':
    async def test_gemma():
        print("Attempting to initialize GemmaLocalClient...")
        # Ensure GEMMA_MODEL_ID or GEMMA_MODEL_PATH is set in .env or settings
        if not (settings.GEMMA_MODEL_ID or settings.GEMMA_MODEL_PATH):
            print("Please set GEMMA_MODEL_ID or GEMMA_MODEL_PATH in .env to run this example.")
            return
        try:
            client = GemmaLocalClient() # Initialization happens here
            if client.is_available:
                print("Gemma client initialized and model loaded.")
                text_response = await client.generate_text_response("Explain what a Large Language Model is in simple terms.")
                print(f"\nGemma Text Response:\n{text_response}")
                
                # Placeholder for multimodal test
                # try:
                #     from PIL import Image
                #     # Create a dummy image for testing - replace with a real image path or generation
                #     dummy_image = Image.new('RGB', (60, 30), color = 'red') 
                #     print("\nTesting multimodal generation (placeholder)...")
                #     multimodal_response = await client.generate_multimodal_response(
                #         text_prompt="What is in this image?", 
                #         image=dummy_image
                #     )
                #     print(f"Gemma Multimodal Response:\n{multimodal_response}")
                # except ImportError:
                #     print("Pillow is not installed, skipping multimodal test.")
                # except Exception as e_multi:
                #     print(f"Error during multimodal test: {e_multi}")
            else:
                print("Gemma client is not available. Check logs for errors.")
        except Exception as e:
            print(f"An error occurred during GemmaLocalClient test: {e}")
            import traceback
            traceback.print_exc()

    asyncio.run(test_gemma()) 