"""
AI Edge Client for Open WebUI Integration
Provides Ollama-compatible API for Google AI Edge models (Gemma 3N E4B)
Supports multimodal capabilities with high performance
"""

import httpx
import asyncio
import logging
import json
import base64
import io
from typing import Optional, AsyncGenerator, Dict, List, Any, Union
from pathlib import Path
from PIL import Image
import zipfile
import tempfile
import sentencepiece as spm
import numpy as np

from models.llm_models import ChatResponse, LLMProvider

logger = logging.getLogger(__name__)

# Import AI Edge dependencies with fallback
try:
    from ai_edge_litert.interpreter import Interpreter
    AI_EDGE_LITERT_AVAILABLE = True
    logger.info("✅ AI Edge LiteRT available")
except ImportError:
    AI_EDGE_LITERT_AVAILABLE = False
    try:
        import tensorflow as tf
        TF_LITE_AVAILABLE = True
        logger.warning("⚠️ Using TensorFlow Lite fallback")
    except ImportError:
        TF_LITE_AVAILABLE = False
        logger.error("❌ Neither AI Edge LiteRT nor TensorFlow Lite available")

class AIEdgeClient:
    """
    AI Edge client providing Ollama-compatible API for Google AI Edge models
    Optimized for Open WebUI integration with multimodal support
    """
    
    def __init__(self, base_url: str = "http://localhost:11435"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=120.0)
        self._available_models = []
        self._loaded_models = {}
        
        # AI Edge Gallery standard configuration
        self.default_config = {
            "topK": 64,
            "topP": 0.95, 
            "temperature": 1.0,
            "maxTokens": 4096,
            "context_length": 32768,  # 32K context like Google AI Edge Gallery
            "randomSeed": 42
        }
        
        # Initialize models (will be loaded on first request)
        self._models_loaded = False
    
    async def _load_ai_edge_models(self):
        """Load available AI Edge models"""
        if self._models_loaded:
            return
            
        try:
            # Try different possible paths
            possible_paths = [
                Path("models/tflite/gemma3n"),  # From app/ directory
                Path("../../backend/app/models/tflite/gemma3n"),  # From backend/app to project root
                Path("/app/models/tflite/gemma3n"),  # Absolute container path
            ]
            
            models_dir = None
            for path in possible_paths:
                if path.exists():
                    models_dir = path
                    break
            
            if not models_dir:
                logger.warning(f"AI Edge models directory not found in any of: {[str(p) for p in possible_paths]}")
                return
            
            # Scan for .task files
            task_files = list(models_dir.glob("*.task"))
            logger.info(f"Found {len(task_files)} AI Edge model files")
            
            for task_file in task_files:
                model_name = task_file.stem
                
                # Create model info following Google AI Edge Gallery format
                model_info = {
                    "name": model_name,
                    "display_name": self._get_display_name(model_name),
                    "path": str(task_file),
                    "size": task_file.stat().st_size,
                    "multimodal": True,  # Gemma 3N supports vision
                    "context_length": 32768,
                    "description": "Google AI Edge model with multimodal capabilities"
                }
                
                self._available_models.append(model_info)
                logger.info(f"✅ Registered AI Edge model: {model_info['display_name']}")
            
            logger.info(f"✅ Total AI Edge models available: {len(self._available_models)}")
            self._models_loaded = True
            
        except Exception as e:
            logger.error(f"❌ Failed to load AI Edge models: {e}")
    
    def _get_display_name(self, model_name: str) -> str:
        """Convert model filename to display name"""
        display_names = {
            "gemma-3n-E2B-it-int4": "Gemma 3N E2B (AI Edge)",
            "gemma-3n-E4B-it-int4": "Gemma 3N E4B (AI Edge)", 
            "gemma-3n-E8B-it-int4": "Gemma 3N E8B (AI Edge)"
        }
        return display_names.get(model_name, f"{model_name} (AI Edge)")
    
    async def _load_model_interpreter(self, model_name: str) -> bool:
        """Load specific model interpreter"""
        try:
            if model_name in self._loaded_models:
                return True
            
            # Find model info
            model_info = None
            for model in self._available_models:
                if model["name"] == model_name or model["display_name"] == model_name:
                    model_info = model
                    break
            
            if not model_info:
                logger.error(f"Model not found: {model_name}")
                return False
            
            task_path = Path(model_info["path"])
            if not task_path.exists():
                logger.error(f"Model file not found: {task_path}")
                return False
            
            # Extract and load model from .task file
            with zipfile.ZipFile(task_path, 'r') as zip_ref:
                model_content = zip_ref.read("TF_LITE_PREFILL_DECODE")
                tokenizer_content = zip_ref.read("TOKENIZER_MODEL")
            
            # Create interpreter
            if AI_EDGE_LITERT_AVAILABLE:
                interpreter = Interpreter(model_content=model_content)
                logger.info(f"✅ Using AI Edge LiteRT for {model_name}")
            elif TF_LITE_AVAILABLE:
                interpreter = tf.lite.Interpreter(model_content=model_content)
                logger.warning(f"⚠️ Using TensorFlow Lite fallback for {model_name}")
            else:
                logger.error("No suitable interpreter available")
                return False
            
            interpreter.allocate_tensors()
            
            # Load tokenizer
            tokenizer = smp.SentencePieceProcessor()
            with tempfile.NamedTemporaryFile(delete=False, suffix='.model') as tmp_file:
                tmp_file.write(tokenizer_content)
                tmp_path = tmp_file.name
            
            try:
                tokenizer.Load(tmp_path)
                logger.info(f"✅ Tokenizer loaded for {model_name}")
            finally:
                Path(tmp_path).unlink()
            
            # Store loaded model
            self._loaded_models[model_name] = {
                "interpreter": interpreter,
                "tokenizer": tokenizer,
                "info": model_info,
                "input_details": interpreter.get_input_details(),
                "output_details": interpreter.get_output_details()
            }
            
            logger.info(f"✅ Model {model_name} loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model {model_name}: {e}")
            return False
    
    def _format_gemma_prompt(self, prompt: str) -> str:
        """Format prompt according to Gemma chat template"""
        return f"<start_of_turn>user\n{prompt}<end_of_turn>\n<start_of_turn>model\n"
    
    def _process_image_input(self, image_data: Union[str, bytes]) -> np.ndarray:
        """Process image input for multimodal models"""
        try:
            if isinstance(image_data, str):
                # Base64 encoded image
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',', 1)[1]
                image_bytes = base64.b64decode(image_data)
            else:
                image_bytes = image_data
            
            # Load and resize image to AI Edge Gallery standards
            image = Image.open(io.BytesIO(image_bytes))
            image = image.convert('RGB')
            
            # Resize to supported resolutions (256x256, 512x512, 768x768)
            target_size = 512  # Standard for Gemma 3N
            image = image.resize((target_size, target_size), Image.Resampling.LANCZOS)
            
            # Convert to numpy array and normalize
            image_array = np.array(image, dtype=np.float32) / 255.0
            
            logger.info(f"✅ Image processed: {image_array.shape}")
            return image_array
            
        except Exception as e:
            logger.error(f"❌ Failed to process image: {e}")
            raise
    
    async def _generate_response(self, model_name: str, prompt: str, 
                                image_data: Optional[Union[str, bytes]] = None,
                                **kwargs) -> str:
        """Generate response using AI Edge model"""
        try:
            # Load model if not already loaded
            if not await self._load_model_interpreter(model_name):
                raise Exception(f"Failed to load model: {model_name}")
            
            model_data = self._loaded_models[model_name]
            interpreter = model_data["interpreter"]
            tokenizer = model_data["tokenizer"]
            
            # Format prompt
            formatted_prompt = self._format_gemma_prompt(prompt)
            
            # Tokenize
            input_tokens = tokenizer.EncodeAsIds(formatted_prompt)
            
            # Prepare inputs
            input_details = model_data["input_details"]
            
            # Set text input
            text_input = np.array([input_tokens], dtype=np.int32)
            interpreter.set_tensor(input_details[0]['index'], text_input)
            
            # Handle image input if provided (multimodal)
            if image_data and len(input_details) > 1:
                image_array = self._process_image_input(image_data)
                # Add batch dimension and set tensor
                image_input = np.expand_dims(image_array, axis=0)
                interpreter.set_tensor(input_details[1]['index'], image_input)
                logger.info("✅ Multimodal input prepared")
            
            # Run inference
            interpreter.invoke()
            
            # Get output
            output_details = model_data["output_details"]
            output_tokens = interpreter.get_tensor(output_details[0]['index'])
            
            # Decode response
            if len(output_tokens.shape) > 1:
                output_tokens = output_tokens[0]
            
            response = tokenizer.DecodeIds(output_tokens.tolist())
            
            # Clean response
            response = self._clean_response(response)
            
            logger.info(f"✅ Generated response: {len(response)} characters")
            return response
            
        except Exception as e:
            logger.error(f"❌ Generation failed: {e}")
            return f"Error generating response: {str(e)}"
    
    def _clean_response(self, response: str) -> str:
        """Clean and format response"""
        if not response:
            return ""
        
        # Remove control tokens
        response = response.split("<end_of_turn>")[0]
        for token in ["<start_of_turn>", "user", "model"]:
            response = response.replace(token, "")
        
        response = response.strip()
        response = " ".join(response.split())
        
        return response
    
    async def is_model_available(self, model_name: str) -> bool:
        """Check if AI Edge model is available"""
        if not self._models_loaded:
            await self._load_ai_edge_models()
            
        return any(
            model_name in model["name"] or model_name in model["display_name"]
            for model in self._available_models
        )
    
    async def chat_completion(self, model: str, prompt: str, 
                             image_data: Optional[str] = None, **kwargs) -> ChatResponse:
        """
        Generate chat completion using AI Edge models
        Compatible with Ollama API format for Open WebUI
        """
        try:
            if not await self.is_model_available(model):
                return ChatResponse(
                    provider=LLMProvider.AI_EDGE,
                    model=model,
                    response=None,
                    error=f"AI Edge model {model} not available. Available: {[m['display_name'] for m in self._available_models]}"
                )
            
            logger.info(f"🚀 Generating AI Edge response with {model}")
            
            # Generate response with multimodal support
            response_text = await self._generate_response(
                model_name=model,
                prompt=prompt,
                image_data=image_data,
                **kwargs
            )
            
            return ChatResponse(
                provider=LLMProvider.AI_EDGE,
                model=model,
                response=response_text,
                error=None
            )
            
        except Exception as e:
            error_msg = f"AI Edge client error: {str(e)}"
            logger.error(error_msg)
            return ChatResponse(
                provider=LLMProvider.AI_EDGE,
                model=model,
                response=None,
                error=error_msg
            )
    
    async def streaming_chat_completion(self, model: str, prompt: str,
                                      image_data: Optional[str] = None, 
                                      **kwargs) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion using AI Edge models
        Simulates streaming for compatibility with Open WebUI
        """
        try:
            if not await self.is_model_available(model):
                yield f"Error: AI Edge model {model} not available"
                return
            
            # Generate full response
            response_text = await self._generate_response(
                model_name=model,
                prompt=prompt,
                image_data=image_data,
                **kwargs
            )
            
            # Simulate streaming by yielding chunks
            words = response_text.split()
            for i, word in enumerate(words):
                if i == 0:
                    yield word
                else:
                    yield f" {word}"
                
                # Small delay to simulate real streaming
                await asyncio.sleep(0.01)
                
        except Exception as e:
            yield f"Error: {str(e)}"
    
    async def get_available_models(self) -> List[Dict]:
        """Get list of available AI Edge models in Ollama format"""
        if not self._models_loaded:
            await self._load_ai_edge_models()
            
        ollama_format_models = []
        
        for model in self._available_models:
            ollama_format_models.append({
                "name": model["display_name"],
                "model": model["name"],
                "size": model["size"],
                "digest": f"ai-edge:{model['name']}",
                "details": {
                    "parent_model": "",
                    "format": "ai-edge-litert",
                    "family": "gemma",
                    "families": ["gemma"],
                    "parameter_size": "4.2B" if "E4B" in model["name"] else "2.6B",
                    "quantization_level": "int4"
                },
                "multimodal": model["multimodal"],
                "context_length": model["context_length"]
            })
        
        return ollama_format_models
    
    async def close(self):
        """Close the HTTP client and cleanup models"""
        await self.client.aclose()
        self._loaded_models.clear()
        logger.info("AI Edge client closed") 