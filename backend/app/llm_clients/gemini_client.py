import logging
import google.generativeai as genai
from core.config import settings

logger = logging.getLogger(__name__)

class GeminiClient:
    def __init__(self, api_key: str | None = settings.GEMINI_API_KEY):
        if not api_key:
            logger.error("Gemini API key is not configured.")
            raise ValueError("Gemini API key is required.")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-pro') # Or other suitable model like gemini-1.5-flash
        logger.info("Gemini client initialized with model gemini-pro.")

    async def generate_response(self, prompt: str) -> str | None:
        logger.info(f"Sending request to Gemini with prompt: {prompt[:50]}...")
        try:
            # The Gemini API library is synchronous by default. 
            # For use in an async FastAPI endpoint, it might be better to run this in a threadpool.
            # Or check if an async version of the library is available.
            # For simplicity here, we call it directly, but be mindful of blocking in a real async app.
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            # response = self.model.generate_content(prompt) # Synchronous call
            response_text = response.text
            logger.info(f"Received response from Gemini: {response_text[:50]}...")
            return response_text
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            # Check for specific Gemini API error types if available in SDK
            # For example, if e has a message attribute:
            # error_message = getattr(e, 'message', str(e))
            # return f"Error from Gemini: {error_message}"
            return f"An unexpected error occurred while communicating with Gemini: {str(e)}"

# Example usage (for testing)
if __name__ == '''__main__''':
    import asyncio
    # Ensure you have GEMINI_API_KEY in your .env file or environment
    if not settings.GEMINI_API_KEY:
        print("Please set your GEMINI_API_KEY in .env file to run this example.")
    else:
        async def main():
            client = GeminiClient()
            response = await client.generate_response("Hello, what is the capital of France?")
            print(f"Gemini Response: {response}")
        asyncio.run(main()) 