import logging
import torch
from transformers import <PERSON>Token<PERSON>, AutoModelForCausalLM, BitsAndBytesConfig
from PIL import Image
from core.config import settings
import asyncio

logger = logging.getLogger(__name__)

class Gemma3_4BLocalClient:
    _instance = None
    _model = None
    _tokenizer = None
    _device = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Gemma3_4BLocalClient, cls).__new__(cls)
            cls._initialize_model()
        return cls._instance

    @classmethod
    def _initialize_model(cls):
        if not settings.GEMMA_3_4B_MODEL_ID and not settings.GEMMA_3_4B_MODEL_PATH:
            logger.warning("Gemma 3 4B model ID or path is not configured. Gemma3_4BLocalClient will not be available.")
            return

        cls._device = settings.GEMMA_3_4B_DEVICE
        model_identifier = settings.GEMMA_3_4B_MODEL_PATH if settings.GEMMA_3_4B_MODEL_PATH else settings.GEMMA_3_4B_MODEL_ID
        dtype_str = getattr(settings, 'GEMMA_3_4B_DTYPE', "auto")
        
        dtype_map = {
            "float16": torch.float16,
            "bfloat16": torch.bfloat16,
            "float32": torch.float32,
            "auto": "auto"
        }
        torch_dtype = dtype_map.get(dtype_str, "auto")

        logger.info(f"Initializing Gemma 3 4B model: {model_identifier} on device: {cls._device} with dtype: {dtype_str}")

        try:
            # Load tokenizer - try different approaches for Gemma 3n
            try:
                cls._tokenizer = AutoTokenizer.from_pretrained(
                    model_identifier,
                    trust_remote_code=True
                )
            except Exception as e1:
                logger.warning(f"First tokenizer attempt failed: {e1}")
                try:
                    cls._tokenizer = AutoTokenizer.from_pretrained(
                        model_identifier,
                        use_fast=False,
                        trust_remote_code=True
                    )
                except Exception as e2:
                    logger.warning(f"Second tokenizer attempt failed: {e2}")
                    # Try with legacy=False for newer models
                    cls._tokenizer = AutoTokenizer.from_pretrained(
                        model_identifier,
                        use_fast=False,
                        trust_remote_code=True,
                        legacy=False
                    )
            
            # For the QAT int4 model, we load the unquantized version and can quantize it later
            # The QAT models are designed to maintain quality when quantized to int4
            cls._model = AutoModelForCausalLM.from_pretrained(
                model_identifier,
                torch_dtype=torch_dtype,
                low_cpu_mem_usage=True if cls._device == "cpu" else False,
                trust_remote_code=True,  # Might be needed for newer models
            ).to(cls._device)

            cls._model.eval()
            logger.info(f"Gemma 3 4B model {model_identifier} loaded successfully on {cls._device}.")

        except Exception as e:
            cls._model = None
            cls._tokenizer = None
            logger.error(f"Failed to load Gemma 3 4B model {model_identifier}: {e}", exc_info=True)

    @property
    def is_available(self) -> bool:
        """Check if the model and tokenizer are loaded."""
        return self._model is not None and self._tokenizer is not None

    async def generate_text_response(self, prompt: str, max_new_tokens: int = 250) -> str | None:
        if not self.is_available:
            logger.error("Gemma 3 4B model is not available for text generation.")
            return "Error: Gemma 3 4B model is not available."

        logger.info(f"Sending text request to local Gemma 3 4B model: {prompt[:50]}...")
        try:
            def _inference():
                # Format prompt for Gemma 3 instruction format
                formatted_prompt = f"<start_of_turn>user\n{prompt}<end_of_turn>\n<start_of_turn>model\n"
                
                inputs = self._tokenizer(formatted_prompt, return_tensors="pt").to(self._device)
                with torch.no_grad():
                    outputs = self._model.generate(
                        **inputs, 
                        max_new_tokens=max_new_tokens, 
                        do_sample=True, 
                        temperature=0.7, 
                        top_k=50, 
                        top_p=0.95,
                        pad_token_id=self._tokenizer.eos_token_id
                    )
                
                # Decode only the new tokens (response)
                response = self._tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
                return response.strip()
            
            response_text = await asyncio.to_thread(_inference)
            logger.info(f"Received text response from local Gemma 3 4B: {response_text[:50]}...")
            return response_text
            
        except Exception as e:
            logger.error(f"Error during Gemma 3 4B text generation: {e}", exc_info=True)
            return f"Error during Gemma 3 4B text generation: {str(e)}"

    async def generate_multimodal_response(self, text_prompt: str, image: Image.Image, max_new_tokens: int = 250) -> str | None:
        if not self.is_available:
            logger.error("Gemma 3 4B model is not available for multimodal generation.")
            return "Error: Gemma 3 4B model is not available."
        
        logger.info(f"Sending multimodal request to local Gemma 3 4B: {text_prompt[:50]}... with image.")
        try:
            # Gemma 3 models support multimodal input (text + images)
            # For now, we'll return a text-only response with image context mention
            # Full multimodal support would require proper image processing and encoding
            
            formatted_prompt = f"<start_of_turn>user\n{text_prompt}\n[Image provided but cannot be processed in current setup]<end_of_turn>\n<start_of_turn>model\n"
            
            def _inference():
                inputs = self._tokenizer(formatted_prompt, return_tensors="pt").to(self._device)
                with torch.no_grad():
                    outputs = self._model.generate(
                        **inputs, 
                        max_new_tokens=max_new_tokens, 
                        do_sample=True, 
                        temperature=0.7, 
                        top_k=50, 
                        top_p=0.95,
                        pad_token_id=self._tokenizer.eos_token_id
                    )
                
                response = self._tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
                return response.strip()
            
            response_text = await asyncio.to_thread(_inference)
            return response_text

        except Exception as e:
            logger.error(f"Error during Gemma 3 4B multimodal generation: {e}", exc_info=True)
            return f"Error during Gemma 3 4B multimodal generation: {str(e)}"

# For testing the client directly:
if __name__ == '__main__':
    async def test_gemma_3_4b():
        print("Attempting to initialize Gemma3_4BLocalClient...")
        if not (settings.GEMMA_3_4B_MODEL_ID or settings.GEMMA_3_4B_MODEL_PATH):
            print("Please set GEMMA_3_4B_MODEL_ID or GEMMA_3_4B_MODEL_PATH in .env to run this example.")
            return
        try:
            client = Gemma3_4BLocalClient()
            if client.is_available:
                print("Gemma 3 4B client initialized and model loaded.")
                text_response = await client.generate_text_response("Explain what artificial intelligence is in simple terms.")
                print(f"\nGemma 3 4B Text Response:\n{text_response}")
            else:
                print("Gemma 3 4B client is not available. Check logs for errors.")
        except Exception as e:
            print(f"An error occurred during Gemma3_4BLocalClient test: {e}")
            import traceback
            traceback.print_exc()

    asyncio.run(test_gemma_3_4b()) 