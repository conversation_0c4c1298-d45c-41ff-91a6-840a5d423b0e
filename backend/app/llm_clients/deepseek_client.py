import logging
import requests # Using requests for DeepSeek as there might not be a dedicated SDK
import json
from core.config import settings

logger = logging.getLogger(__name__)

# DeepSeek API endpoint - typically compatible with OpenAI's API structure
# You might need to verify the exact endpoint from DeepSeek's documentation.
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions" 

class DeepSeekClient:
    def __init__(self, api_key: str | None = settings.DEEPSEEK_API_KEY):
        if not api_key:
            logger.error("DeepSeek API key is not configured.")
            raise ValueError("DeepSeek API key is required.")
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        logger.info("DeepSeek client initialized.")

    async def generate_response(self, prompt: str, model: str = "deepseek-chat") -> str | None:
        logger.info(f"Sending request to DeepSeek model: {model} with prompt: {prompt[:50]}...")
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 2048, # Example, adjust as needed
            "temperature": 0.7   # Example, adjust as needed
        }
        try:
            # Using await asyncio.to_thread for the blocking requests.post call
            import asyncio
            response = await asyncio.to_thread(
                requests.post, DEEPSEEK_API_URL, headers=self.headers, json=payload, timeout=30
            )
            response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
            
            completion = response.json()
            response_content = completion.get('choices', [{}])[0].get('message', {}).get('content')
            
            if response_content:
                logger.info(f"Received response from DeepSeek: {response_content[:50]}...")
                return response_content
            else:
                logger.error(f"DeepSeek response did not contain expected content: {completion}")
                return "Error: DeepSeek response format is unexpected."
                
        except requests.exceptions.HTTPError as e:
            logger.error(f"DeepSeek HTTP error: {e.response.status_code} - {e.response.text}")
            return f"Error from DeepSeek: {e.response.status_code} - {e.response.text}"
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek request failed: {e}")
            return f"Error: Could not connect to DeepSeek API. {e}"
        except Exception as e:
            logger.error(f"An unexpected error occurred with DeepSeek: {e}")
            return "An unexpected error occurred while communicating with DeepSeek."

# Example usage (for testing)
if __name__ == '''__main__''':
    import asyncio
    # Ensure you have DEEPSEEK_API_KEY in your .env file or environment
    if not settings.DEEPSEEK_API_KEY:
        print("Please set your DEEPSEEK_API_KEY in .env file to run this example.")
    else:
        async def main():
            client = DeepSeekClient()
            response = await client.generate_response("Tell me a fun fact about programming.")
            print(f"DeepSeek Response: {response}")
        asyncio.run(main()) 