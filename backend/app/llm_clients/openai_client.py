import logging
from openai import OpenAI, APIError
from core.config import settings

logger = logging.getLogger(__name__)

class OpenAIClient:
    def __init__(self, api_key: str | None = settings.OPENAI_API_KEY):
        if not api_key:
            logger.error("OpenAI API key is not configured.")
            raise ValueError("OpenAI API key is required.")
        self.client = OpenAI(api_key=api_key)
        logger.info("OpenAI client initialized.")

    async def generate_response(self, prompt: str, model: str = "gpt-3.5-turbo") -> str | None:
        logger.info(f"Sending request to OpenAI model: {model} with prompt: {prompt[:50]}...")
        try:
            completion = await self.client.chat.completions.create( # Use await for async client if available, otherwise use sync
                model=model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": prompt}
                ]
            )
            response_content = completion.choices[0].message.content
            logger.info(f"Received response from OpenAI: {response_content[:50]}...")
            return response_content
        except APIError as e:
            logger.error(f"OpenAI API error: {e}")
            return f"Error from OpenAI: {e}"
        except Exception as e:
            logger.error(f"An unexpected error occurred with OpenAI: {e}")
            return "An unexpected error occurred while communicating with OpenAI."

# Example usage (for testing)
if __name__ == '''__main__''':
    import asyncio
    # Ensure you have OPENAI_API_KEY in your .env file or environment
    if not settings.OPENAI_API_KEY:
        print("Please set your OPENAI_API_KEY in .env file to run this example.")
    else:
        async def main():
            client = OpenAIClient()
            response = await client.generate_response("Hello, what is FastAPI?")
            print(f"OpenAI Response: {response}")
        asyncio.run(main()) 