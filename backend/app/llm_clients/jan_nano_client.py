"""
Jan-nano Client for Vietnamese RAG System
Optimized for Menlo/Jan-nano-gguf model with tool use capabilities
"""

import httpx
import asyncio
import logging
import json
from typing import Optional, AsyncGenerator, Dict, Any
from pathlib import Path
from models.llm_models import ChatResponse, LLMProvider

logger = logging.getLogger(__name__)

class JanNanoClient:
    """
    Jan-nano client for the Menlo/Jan-nano-gguf model
    Optimized for tool use and function calling capabilities
    """
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=180.0)  # Longer timeout for tool use
        self.model_name = "jan-nano"
        self.available_variants = [
            "jan-nano:q3_k_s",    # 1.89 GB - Ultra fast
            "jan-nano:q4_k_m",    # 2.5 GB - Balanced
            "jan-nano:q5_k_m",    # 2.89 GB - High quality
            "jan-nano:q6_k",      # 3.31 GB - Very high quality
            "jan-nano:q8_0",      # 4.28 GB - Maximum quality
            "jan-nano:latest"     # Default variant
        ]
        
        # Jan-nano specific configuration (from HuggingFace recommendations)
        self.default_config = {
            "temperature": 0.7,
            "top_p": 0.8,
            "top_k": 20,
            "min_p": 0.0,
            "num_predict": 2048,
            "stop": ["<|endoftext|>", "<|im_end|>"],
        }
        
        self._model_available = None
        
    async def _check_model_availability(self) -> bool:
        """Check if Jan-nano model is available in Ollama"""
        if self._model_available is not None:
            return self._model_available
            
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                available_models = [model["name"] for model in data.get("models", [])]
                
                # Check for any Jan-nano variant
                jan_nano_available = any(
                    "jan-nano" in model.lower() or "jan_nano" in model.lower()
                    for model in available_models
                )
                
                if jan_nano_available:
                    logger.info(f"✅ Jan-nano model found in Ollama")
                    self._model_available = True
                else:
                    logger.warning(f"⚠️  Jan-nano model not found. Available models: {available_models}")
                    self._model_available = False
                    
            else:
                logger.warning(f"❌ Failed to check Ollama models: {response.status_code}")
                self._model_available = False
                
        except Exception as e:
            logger.warning(f"❌ Ollama connection failed: {e}")
            self._model_available = False
            
        return self._model_available
    
    async def download_model(self, variant: str = "q4_k_m") -> bool:
        """Download Jan-nano model to Ollama"""
        model_tag = f"jan-nano:{variant}"
        
        try:
            logger.info(f"🔄 Downloading Jan-nano model: {model_tag}")
            
            # Pull model using Ollama API
            response = await self.client.post(
                f"{self.base_url}/api/pull",
                json={"name": model_tag},
                timeout=1800.0  # 30 minutes for download
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Jan-nano model {model_tag} downloaded successfully")
                self._model_available = True
                return True
            else:
                logger.error(f"❌ Failed to download Jan-nano: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return False
    
    async def install_from_gguf(self, gguf_path: str, variant: str = "q4_k_m") -> bool:
        """Install Jan-nano from local GGUF file using Ollama's create API"""
        if not Path(gguf_path).exists():
            logger.error(f"❌ GGUF file not found: {gguf_path}")
            return False
            
        model_tag = f"jan-nano:{variant}"
        
        # Create Modelfile for Jan-nano
        modelfile_content = f"""
FROM {gguf_path}

# Jan-nano specific template (Qwen3 architecture)
TEMPLATE \"\"\"<start_of_turn>user
{{{{ .Prompt }}}}<end_of_turn>
<start_of_turn>model
\"\"\"

# Optimized parameters for Jan-nano
PARAMETER temperature {self.default_config['temperature']}
PARAMETER top_p {self.default_config['top_p']}
PARAMETER top_k {self.default_config['top_k']}
PARAMETER num_predict {self.default_config['num_predict']}

# System message for tool use capability
SYSTEM \"\"\"You are Jan-nano, an AI assistant with excellent tool use and function calling capabilities. You are helpful, harmless, and honest. You can understand and execute complex tasks efficiently.\"\"\"
"""
        
        try:
            logger.info(f"🔧 Creating Jan-nano model from GGUF: {gguf_path}")
            
            response = await self.client.post(
                f"{self.base_url}/api/create",
                json={
                    "name": model_tag,
                    "modelfile": modelfile_content
                },
                timeout=600.0  # 10 minutes for model creation
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Jan-nano model {model_tag} created successfully")
                self._model_available = True
                return True
            else:
                logger.error(f"❌ Failed to create Jan-nano model: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Model creation failed: {e}")
            return False
    
    async def chat_completion(self, prompt: str, model_variant: str = "q4_k_m", **kwargs) -> ChatResponse:
        """
        Generate chat completion using Jan-nano
        Optimized for tool use and function calling
        """
        try:
            if not await self._check_model_availability():
                return ChatResponse(
                    provider=LLMProvider.OLLAMA,
                    model=f"jan-nano:{model_variant}",
                    response=None,
                    error="Jan-nano model not available. Please install it first."
                )
            
            model_tag = f"jan-nano:{model_variant}"
            
            # Merge default config with provided kwargs
            config = {**self.default_config, **kwargs}
            
            # Format prompt for Jan-nano (Qwen3 architecture)
            formatted_prompt = f"<start_of_turn>user\n{prompt}<end_of_turn>\n<start_of_turn>model\n"
            
            # Prepare request payload
            payload = {
                "model": model_tag,
                "prompt": formatted_prompt,
                "stream": False,
                "options": {
                    "temperature": config["temperature"],
                    "top_p": config["top_p"],
                    "top_k": config["top_k"],
                    "num_predict": config["num_predict"],
                    "stop": config["stop"]
                }
            }
            
            logger.info(f"🧠 Sending request to Jan-nano ({model_variant}): {prompt[:50]}...")
            
            # Make request to Ollama
            response = await self.client.post(
                f"{self.base_url}/api/generate",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")
                
                # Extract metrics
                eval_count = result.get("eval_count", 0)
                eval_duration = result.get("eval_duration", 0)
                
                tokens_per_second = 0
                if eval_duration > 0:
                    tokens_per_second = eval_count / (eval_duration / 1e9)  # Convert nanoseconds to seconds
                
                logger.info(f"✅ Jan-nano response: {len(response_text)} chars, {tokens_per_second:.1f} tok/s")
                
                return ChatResponse(
                    provider=LLMProvider.OLLAMA,
                    model=model_tag,
                    response=response_text,
                    error=None,
                    metadata={
                        "tokens_per_second": tokens_per_second,
                        "eval_count": eval_count,
                        "eval_duration_ms": eval_duration / 1e6,
                        "model_variant": model_variant,
                        "supports_tools": True
                    }
                )
            else:
                error_msg = f"Jan-nano API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return ChatResponse(
                    provider=LLMProvider.OLLAMA,
                    model=model_tag,
                    response=None,
                    error=error_msg
                )
                
        except Exception as e:
            error_msg = f"Jan-nano client error: {str(e)}"
            logger.error(error_msg)
            return ChatResponse(
                provider=LLMProvider.OLLAMA,
                model=f"jan-nano:{model_variant}",
                response=None,
                error=error_msg
            )
    
    async def streaming_chat_completion(self, prompt: str, model_variant: str = "q4_k_m", **kwargs) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion using Jan-nano
        """
        try:
            if not await self._check_model_availability():
                yield "Error: Jan-nano model not available"
                return
            
            model_tag = f"jan-nano:{model_variant}"
            config = {**self.default_config, **kwargs}
            
            # Format prompt for Jan-nano
            formatted_prompt = f"<start_of_turn>user\n{prompt}<end_of_turn>\n<start_of_turn>model\n"
            
            payload = {
                "model": model_tag,
                "prompt": formatted_prompt,
                "stream": True,
                "options": {
                    "temperature": config["temperature"],
                    "top_p": config["top_p"],
                    "top_k": config["top_k"],
                    "num_predict": config["num_predict"],
                    "stop": config["stop"]
                }
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/generate",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status_code == 200:
                    async for line in response.aiter_lines():
                        if line:
                            try:
                                data = json.loads(line)
                                if "response" in data:
                                    yield data["response"]
                                    
                                if data.get("done", False):
                                    break
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"Error: HTTP {response.status_code}"
                    
        except Exception as e:
            yield f"Error: {str(e)}"
    
    async def function_calling(self, prompt: str, functions: list, model_variant: str = "q4_k_m") -> ChatResponse:
        """
        Enhanced function calling using Jan-nano's tool use capabilities
        """
        # Format prompt with function definitions for tool use
        function_prompt = f"""You have access to the following functions. Use them when appropriate:

{json.dumps(functions, indent=2)}

User request: {prompt}

Please analyze the request and call the appropriate function(s) if needed. Format your function calls as JSON."""
        
        return await self.chat_completion(function_prompt, model_variant)
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get detailed information about the Jan-nano model"""
        info = {
            "name": "Jan-nano",
            "provider": "Menlo Research",
            "architecture": "Qwen3-4B",
            "parameters": "4.02B",
            "context_length": 32768,
            "supports_tools": True,
            "supports_multimodal": False,  # Text-only model
            "recommended_variants": {
                "speed": "q3_k_s",      # Fastest
                "balanced": "q4_k_m",   # Recommended
                "quality": "q6_k"       # Best quality
            },
            "use_cases": [
                "Function calling",
                "Tool use",
                "Research assistance", 
                "Code generation",
                "Local deployment"
            ],
            "memory_requirements": {
                "q3_k_s": "1.89 GB",
                "q4_k_m": "2.5 GB", 
                "q5_k_m": "2.89 GB",
                "q6_k": "3.31 GB",
                "q8_0": "4.28 GB"
            }
        }
        
        if await self._check_model_availability():
            info["status"] = "available"
        else:
            info["status"] = "not_installed"
            
        return info
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Usage examples and testing
if __name__ == "__main__":
    async def test_jan_nano():
        client = JanNanoClient()
        
        # Check availability
        print("🔍 Checking Jan-nano availability...")
        available = await client._check_model_availability()
        print(f"Available: {available}")
        
        if not available:
            print("💾 Model not found. You can install it using:")
            print("1. ollama pull jan-nano:q4_k_m")
            print("2. Or use client.install_from_gguf(path_to_gguf)")
            return
        
        # Get model info
        info = await client.get_model_info()
        print(f"📊 Model info: {json.dumps(info, indent=2)}")
        
        # Test basic chat
        print("\n🧠 Testing basic chat...")
        response = await client.chat_completion("Explain quantum computing in simple terms")
        print(f"Response: {response.response[:100]}...")
        
        # Test function calling
        print("\n🔧 Testing function calling...")
        functions = [
            {
                "name": "get_weather",
                "description": "Get weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string", "description": "City name"}
                    }
                }
            }
        ]
        
        func_response = await client.function_calling(
            "What's the weather like in Hanoi?", 
            functions
        )
        print(f"Function response: {func_response.response[:100]}...")
        
        await client.close()
    
    asyncio.run(test_jan_nano()) 