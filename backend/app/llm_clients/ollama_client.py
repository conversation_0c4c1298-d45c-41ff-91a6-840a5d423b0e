import httpx
import asyncio
import logging
import json
from typing import Optional, AsyncGenerator
from models.llm_models import ChatResponse, LLMProvider

logger = logging.getLogger(__name__)

class OllamaClient:
    """
    Ollama client for local models like Llama 3.1 8B
    """
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=120.0)
        self._available_models = []
        
        # Initialize available models flag (will be loaded on first use)
        self._models_loaded = False
    
    async def _load_available_models(self):
        """Load list of available Ollama models"""
        if self._models_loaded:
            return
            
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                self._available_models = [model["name"] for model in data.get("models", [])]
                logger.info(f"✅ Ollama models available: {self._available_models}")
                self._models_loaded = True
            else:
                logger.warning(f"❌ Failed to load Ollama models: {response.status_code}")
        except Exception as e:
            logger.warning(f"❌ Ollama not available: {e}")
    
    async def is_model_available(self, model_name: str) -> bool:
        """Check if a specific model is available"""
        if not self._available_models:
            await self._load_available_models()
        
        # Support different model name formats
        return any(
            model_name in model or model in model_name 
            for model in self._available_models
        )
    
    async def chat_completion(self, model: str, prompt: str) -> ChatResponse:
        """
        Generate chat completion using Ollama
        """
        try:
            if not await self.is_model_available(model):
                return ChatResponse(
                    provider=LLMProvider.OLLAMA,
                    model=model,
                    response=None,
                    error=f"Model {model} not available in Ollama. Available: {self._available_models}"
                )
            
            # Prepare request
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            logger.info(f"Sending request to Ollama model: {model}")
            
            # Make request to Ollama
            response = await self.client.post(
                f"{self.base_url}/api/generate",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")
                
                logger.info(f"✅ Ollama response received: {len(response_text)} characters")
                
                return ChatResponse(
                    provider=LLMProvider.OLLAMA,
                    model=model,
                    response=response_text,
                    error=None
                )
            else:
                error_msg = f"Ollama API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return ChatResponse(
                    provider=LLMProvider.OLLAMA,
                    model=model,
                    response=None,
                    error=error_msg
                )
                
        except Exception as e:
            error_msg = f"Ollama client error: {str(e)}"
            logger.error(error_msg)
            return ChatResponse(
                provider=LLMProvider.OLLAMA,
                model=model,
                response=None,
                error=error_msg
            )
    
    async def streaming_chat_completion(self, model: str, prompt: str) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion using Ollama
        """
        try:
            if not await self.is_model_available(model):
                yield f"Error: Model {model} not available"
                return
            
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": True,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/generate",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status_code == 200:
                    async for line in response.aiter_lines():
                        if line:
                            try:
                                data = json.loads(line)
                                if "response" in data:
                                    yield data["response"]
                                    
                                if data.get("done", False):
                                    break
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"Error: HTTP {response.status_code}"
                    
        except Exception as e:
            yield f"Error: {str(e)}"
    
    async def get_available_models(self):
        """Get list of available models"""
        if not self._available_models:
            await self._load_available_models()
        return self._available_models
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose() 