"""
API endpoints for Gemma 3n E4B with Google AI Edge SDK
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import logging
import time

logger = logging.getLogger(__name__)

router = APIRouter()

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str = "gemma-3n-e4b-litert"
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class GenerateRequest(BaseModel):
    text: str
    max_tokens: int = 1024
    temperature: float = 0.7

@router.get("/health")
async def health_check():
    """Health check for Gemma 3n E4B Google AI Edge service"""
    try:
        from services.gemma3n_edge_service import gemma3n_edge_service
        
        model_info = gemma3n_edge_service.get_model_info()
        
        return {
            "status": "healthy" if model_info["model_loaded"] else "unhealthy",
            "service": "Gemma 3n E4B Google AI Edge SDK",
            "model_loaded": model_info["model_loaded"],
            "sdk_available": model_info["sdk_available"],
            "real_inference": model_info["real_inference"],
            "model_path": model_info["model_path"],
            "max_context_length": model_info["max_context_length"],
            "image_token_count": model_info["image_token_count"],
            "input_details": model_info["input_details"],
            "output_details": model_info["output_details"],
            "timestamp": int(time.time())
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.post("/generate")
async def generate_text(request: GenerateRequest):
    """Generate text using Gemma 3n E4B with Google AI Edge SDK"""
    try:
        from services.gemma3n_edge_service import gemma3n_edge_service
        
        if not gemma3n_edge_service.is_available():
            raise HTTPException(status_code=503, detail="Gemma 3n E4B service not available")
        
        response = gemma3n_edge_service.generate_response(
            text=request.text,
            images=None,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        return {
            "response": response,
            "model": "gemma-3n-e4b-litert",
            "sdk": "Google AI Edge SDK",
            "timestamp": int(time.time())
        }
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")

@router.get("/model-info")
async def get_model_info():
    """Get detailed information about the Gemma 3n E4B model"""
    try:
        from services.gemma3n_edge_service import gemma3n_edge_service
        
        model_info = gemma3n_edge_service.get_model_info()
        
        return {
            "model_name": "Gemma 3n E4B LiteRT",
            "provider": "Google AI Edge SDK",
            "version": "INT4 Quantized",
            "capabilities": [
                "Text Generation",
                "Multimodal (Text + Images)",
                "32K Context Length",
                "Edge Optimized"
            ],
            "specifications": {
                "parameters": "4B (Effective)",
                "quantization": "INT4",
                "context_length": model_info["max_context_length"],
                "image_tokens": model_info["image_token_count"],
                "model_size": "~4.2MB"
            },
            "status": model_info,
            "performance": {
                "first_token_latency": "~118ms (CPU)",
                "throughput": "~12.8 tokens/sec (CPU)",
                "memory_usage": "~500MB"
            }
        }
        
    except Exception as e:
        logger.error(f"Model info failed: {e}")
        raise HTTPException(status_code=500, detail=f"Model info failed: {str(e)}")

@router.get("/sdk-status")
async def get_sdk_status():
    """Get Google AI Edge SDK installation status"""
    try:
        sdk_status = {}
        
        # Check ai-edge-torch
        try:
            import ai_edge_torch
            sdk_status["ai_edge_torch"] = {
                "installed": True,
                "version": getattr(ai_edge_torch, "__version__", "unknown")
            }
        except ImportError:
            sdk_status["ai_edge_torch"] = {"installed": False}
        
        # Check ai-edge-litert
        try:
            import ai_edge_litert
            sdk_status["ai_edge_litert"] = {
                "installed": True,
                "version": getattr(ai_edge_litert, "__version__", "unknown")
            }
        except ImportError:
            sdk_status["ai_edge_litert"] = {"installed": False}
        
        # Check torch
        try:
            import torch
            sdk_status["torch"] = {
                "installed": True,
                "version": torch.__version__
            }
        except ImportError:
            sdk_status["torch"] = {"installed": False}
        
        # Check google-generativeai
        try:
            import google.generativeai as genai
            sdk_status["google_generativeai"] = {
                "installed": True,
                "version": getattr(genai, "__version__", "unknown")
            }
        except ImportError:
            sdk_status["google_generativeai"] = {"installed": False}
        
        # Check mediapipe
        try:
            import mediapipe as mp
            sdk_status["mediapipe"] = {
                "installed": True,
                "version": mp.__version__
            }
        except ImportError:
            sdk_status["mediapipe"] = {"installed": False}
        
        return {
            "google_ai_edge_sdk": sdk_status,
            "overall_status": "ready" if all(pkg.get("installed", False) for pkg in sdk_status.values()) else "incomplete",
            "timestamp": int(time.time())
        }
        
    except Exception as e:
        logger.error(f"SDK status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"SDK status check failed: {str(e)}") 