from fastapi import APIRouter
from app.api.v1.endpoints import chat, health, rag, universal_rag, openwebui_rag, mem0_settings, image_handler, pandas_mcp
from app.api.memory_router import router as memory_router

api_router = APIRouter()

# Add OpenAI-compatible endpoints directly to api_router (without /chat prefix)
# This allows Open WebUI to access /api/v1/models and /api/v1/chat/completions
api_router.add_api_route("/models", chat.list_models, methods=["GET"], tags=["OpenAI-Compatible"])
api_router.add_api_route("/chat/completions", chat.create_chat_completion, methods=["POST"], tags=["OpenAI-Compatible"])

# Include RAG endpoints for knowledge base functionality
api_router.include_router(rag.router, prefix="/rag", tags=["RAG"])

# Include Universal RAG endpoints for all models
api_router.include_router(universal_rag.router, prefix="/universal-rag", tags=["Universal RAG"])

# Include Open WebUI RAG endpoints for advanced features
api_router.include_router(openwebui_rag.router, prefix="/openwebui-rag", tags=["Open WebUI RAG"])

# Include pandas MCP endpoints for data analysis
api_router.include_router(pandas_mcp.router, prefix="/pandas-mcp", tags=["Pandas MCP"])

api_router.include_router(health.router, prefix="/health", tags=["Health"])
api_router.include_router(chat.router, prefix="/chat", tags=["Chat"])

# Include memory management endpoints
api_router.include_router(memory_router, prefix="/memory", tags=["Memory"])
api_router.include_router(mem0_settings.router, prefix="/mem0", tags=["Memory Settings"])

# Include image processing endpoints
api_router.include_router(image_handler.router, tags=["Image Processing"])