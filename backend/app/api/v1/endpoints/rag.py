import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Query, Body
from typing import List, Dict, Any

from services.rag_service import rag_service # Singleton instance
from models.rag_models import (
    RAGQueryRequest, DocumentUploadResponse, DocumentChunk, RetrievedChunksResponse
)
from models.llm_models import ChatResponse # For RAG query response
from core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/upload_document", response_model=DocumentUploadResponse, summary="Upload a document for RAG processing")
async def upload_document_endpoint(file: UploadFile = File(..., description="Document file to be processed for RAG.")):
    """
    Accepts a document file (PDF, DOCX, TXT, XLSX, XLS), processes it, 
    generates embeddings for its chunks, and adds them to the vector store.
    """
    if not rag_service.is_ready():
        raise HTTPException(status_code=503, detail="RAGService is not properly initialized or available.")

    # Validate file type based on settings
    file_extension = file.filename.split(".")[-1].lower()
    if file_extension not in settings.RAG_SUPPORTED_FILE_TYPES:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type: '{file_extension}'. Supported types: {settings.RAG_SUPPORTED_FILE_TYPES}"
        )

    logger.info(f"Received document upload: {file.filename}")
    try:
        response = await rag_service.process_document_upload(file)
        if "Error" in response.message: # Check for errors indicated by the service
            if "RAGService is not properly initialized" in response.message:
                raise HTTPException(status_code=503, detail=response.message)
            raise HTTPException(status_code=500, detail=response.message)
        return response
    except HTTPException as http_exc: # Re-raise known HTTPExceptions
        raise http_exc
    except Exception as e:
        logger.exception(f"Unexpected error during document upload for {file.filename}: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected server error occurred: {str(e)}")

@router.post("/query", response_model=ChatResponse, summary="Query with RAG augmentation")
async def rag_query_endpoint(request: RAGQueryRequest = Body(...)):
    """
    Receives a user query, retrieves relevant context from stored documents,
    and generates an answer using an LLM (typically local Gemma) augmented with this context.
    """
    if not rag_service.is_ready():
        raise HTTPException(status_code=503, detail="RAGService is not properly initialized or available.")
    if not request.prompt:
        raise HTTPException(status_code=400, detail="Prompt cannot be empty.")

    logger.info(f"Received RAG query: {request.prompt[:100]}...")
    try:
        response = await rag_service.answer_query_with_rag(request)
        if response.error:
            # Check for specific errors that might indicate service unavailability
            if ("RAGService is not properly initialized" in response.error or 
               "Local Gemma model is not available" in response.error or 
               "Failed to generate query embedding" in response.error):
                raise HTTPException(status_code=503, detail=response.error)    
            raise HTTPException(status_code=500, detail=response.error)    
        return response
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"Unexpected server error during RAG query: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected server error occurred: {str(e)}")

@router.get("/retrieve_chunks", response_model=RetrievedChunksResponse, summary="Retrieve relevant chunks for a query (for debugging)")
async def retrieve_chunks_endpoint(
    query: str = Query(..., description="The query string to find relevant chunks for."),
    top_k: int | None = Query(None, description="Number of chunks to retrieve. Defaults to RAG_TOP_K_RESULTS.")
):
    """
    For debugging: retrieves the most relevant document chunks for a given query string 
    without sending them to an LLM for synthesis.
    """
    if not rag_service.is_ready():
        raise HTTPException(status_code=503, detail="RAGService is not properly initialized or available.")
    if not query:
        raise HTTPException(status_code=400, detail="Query cannot be empty.")

    logger.info(f"Received request to retrieve chunks for query: {query[:100]}...")
    try:
        chunks = await rag_service.get_retrieved_chunks_for_query(query, top_k=top_k)
        return RetrievedChunksResponse(query=query, retrieved_chunks=chunks, message=f"Retrieved {len(chunks)} chunks.")
    except Exception as e:
        logger.exception(f"Unexpected server error during chunk retrieval: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected server error occurred: {str(e)}")

@router.get("/store_status", summary="Get the status of the RAG vector store")
async def rag_store_status_endpoint() -> Dict[str, Any]:
    """Returns the current status and statistics of the RAG vector store (FAISS index)."""
    if not rag_service.vector_store: # Check if rag_service itself and its vector_store are initialized
        raise HTTPException(status_code=503, detail="RAGService or its vector store is not initialized.")
    try:
        return rag_service.get_vector_store_status()
    except Exception as e:
        logger.exception(f"Error getting RAG store status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting RAG store status: {str(e)}")

@router.post("/clear_store", summary="Clear all data from the RAG vector store")
async def clear_rag_store_endpoint() -> Dict[str, str]:
    """
    Clears all indexed documents (vectors and metadata) from the RAG store. 
    Uploaded documents in RAG_DOCUMENTS_DIR are not deleted by this operation by default.
    USE WITH CAUTION!
    """
    if not rag_service.vector_store:
        raise HTTPException(status_code=503, detail="RAGService or its vector store is not initialized.")
    logger.warning("Received request to clear RAG store. This action is irreversible for the index.")
    try:
        response = rag_service.clear_rag_store()
        if response["status"] == "error":
            raise HTTPException(status_code=500, detail=response["message"])
        return response
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"Unexpected error clearing RAG store: {e}")
        raise HTTPException(status_code=500, detail=f"Unexpected error clearing RAG store: {str(e)}")
 