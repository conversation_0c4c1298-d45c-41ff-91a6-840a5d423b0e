"""
Open WebUI RAG Endpoints
Advanced RAG endpoints compatible with Open WebUI features
"""

import logging
import asyncio
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Body, Query
from typing import List, Dict, Any, Optional
import tempfile
import os
from pathlib import Path

from services.openwebui_rag_service import openwebui_rag_service
from services.universal_rag_service import universal_rag_service
from models.rag_models import RAGQueryRequest
from models.llm_models import ChatResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# =================== KNOWLEDGE COLLECTIONS ===================

@router.post("/collections/create", summary="Create a new knowledge collection")
async def create_knowledge_collection(
    name: str = Body(..., description="Collection name"),
    description: str = Body("", description="Collection description"),
    visibility: str = Body("private", description="Collection visibility")
):
    """Create a new knowledge collection"""
    try:
        collection_id = await openwebui_rag_service.create_collection(name, description, visibility)
        return {
            "status": "success",
            "collection_id": collection_id,
            "message": f"Collection '{name}' created successfully"
        }
    except Exception as e:
        logger.error(f"Error creating collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/collections", summary="List all knowledge collections")
async def list_collections():
    """Get all knowledge collections"""
    try:
        collections = openwebui_rag_service.get_collections()
        return {
            "status": "success",
            "collections": collections
        }
    except Exception as e:
        logger.error(f"Error listing collections: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/collections/{collection_id}", summary="Get specific collection")
async def get_collection(collection_id: str):
    """Get specific collection details"""
    try:
        collection = openwebui_rag_service.get_collection(collection_id)
        if not collection:
            raise HTTPException(status_code=404, detail="Collection not found")
        
        return {
            "status": "success",
            "collection": collection
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/collections/{collection_id}", summary="Delete a collection")
async def delete_collection(collection_id: str):
    """Delete a knowledge collection"""
    try:
        success = await openwebui_rag_service.delete_collection(collection_id)
        if not success:
            raise HTTPException(status_code=404, detail="Collection not found")
        
        return {
            "status": "success",
            "message": "Collection deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =================== DOCUMENT MANAGEMENT ===================

@router.post("/collections/{collection_id}/documents/upload", summary="Upload document to collection")
async def upload_document_to_collection(
    collection_id: str,
    file: UploadFile = File(..., description="Document file to upload")
):
    """Upload and process a document into a collection"""
    try:
        if not openwebui_rag_service.is_ready():
            raise HTTPException(status_code=503, detail="Open WebUI RAG service not ready")
        
        # Validate file type
        file_extension = Path(file.filename).suffix.lower()
        supported_types = ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.md', '.html']
        
        if file_extension not in supported_types:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file_extension}. Supported: {supported_types}"
            )
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Process document
            result = await openwebui_rag_service.process_document(
                collection_id, temp_file_path, file.filename
            )
            
            if result["status"] == "error":
                raise HTTPException(status_code=400, detail=result["message"])
            
            return result
        
        finally:
            # Clean up temp file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/collections/{collection_id}/documents/upload_multiple", summary="Upload multiple documents")
async def upload_multiple_documents(
    collection_id: str,
    files: List[UploadFile] = File(..., description="Multiple document files")
):
    """Upload multiple documents to a collection"""
    try:
        results = []
        
        for file in files:
            try:
                # Validate file type
                file_extension = Path(file.filename).suffix.lower()
                supported_types = ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.md', '.html']
                
                if file_extension not in supported_types:
                    results.append({
                        "filename": file.filename,
                        "status": "error",
                        "message": f"Unsupported file type: {file_extension}"
                    })
                    continue
                
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                    content = await file.read()
                    temp_file.write(content)
                    temp_file_path = temp_file.name
                
                try:
                    # Process document
                    result = await openwebui_rag_service.process_document(
                        collection_id, temp_file_path, file.filename
                    )
                    
                    results.append({
                        "filename": file.filename,
                        **result
                    })
                
                finally:
                    # Clean up temp file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
            
            except Exception as e:
                logger.error(f"Error processing file {file.filename}: {e}")
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "message": str(e)
                })
        
        # Count success/failures
        success_count = sum(1 for r in results if r["status"] == "success")
        total_count = len(results)
        
        return {
            "status": "completed",
            "message": f"Processed {success_count}/{total_count} files successfully",
            "results": results
        }
    
    except Exception as e:
        logger.error(f"Error uploading multiple documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =================== HYBRID SEARCH ===================

@router.post("/collections/{collection_id}/search", summary="Hybrid search in collection")
async def hybrid_search_collection(
    collection_id: str,
    query: str = Body(..., description="Search query"),
    top_k: int = Body(5, description="Number of results to return")
):
    """Perform hybrid search (BM25 + Vector) in a collection"""
    try:
        if not openwebui_rag_service.is_ready():
            raise HTTPException(status_code=503, detail="Open WebUI RAG service not ready")
        
        chunks = await openwebui_rag_service.hybrid_search(query, collection_id, top_k)
        
        # Format results
        results = []
        for chunk in chunks:
            results.append({
                "id": chunk.id,
                "content": chunk.content,
                "metadata": chunk.metadata,
                "scores": {
                    "similarity": chunk.similarity_score,
                    "bm25": chunk.bm25_score,
                    "combined": getattr(chunk, 'combined_score', 0),
                    "rerank": chunk.rerank_score
                }
            })
        
        return {
            "status": "success",
            "query": query,
            "results": results,
            "collection_id": collection_id
        }
    
    except Exception as e:
        logger.error(f"Error in hybrid search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search_multiple_collections", summary="Search across multiple collections")
async def search_multiple_collections(
    query: str = Body(..., description="Search query"),
    collection_ids: List[str] = Body(..., description="List of collection IDs"),
    top_k: int = Body(5, description="Number of results per collection")
):
    """Search across multiple collections"""
    try:
        if not openwebui_rag_service.is_ready():
            raise HTTPException(status_code=503, detail="Open WebUI RAG service not ready")
        
        context = await openwebui_rag_service.retrieve_context(query, collection_ids, top_k)
        
        return {
            "status": "success",
            "query": query,
            "context": context,
            "collections_searched": collection_ids
        }
    
    except Exception as e:
        logger.error(f"Error searching multiple collections: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =================== UNIVERSAL RAG WITH COLLECTIONS ===================

@router.post("/query_with_collections", response_model=ChatResponse, summary="Query any model with knowledge collections")
async def query_with_knowledge_collections(
    prompt: str = Body(..., description="User query"),
    model: str = Body(..., description="Model to use for response"),
    collection_ids: List[str] = Body(..., description="Knowledge collections to search"),
    top_k: int = Body(5, description="Number of context chunks"),
    full_context: bool = Body(False, description="Use full context mode")
):
    """Query any model using knowledge collections as context"""
    try:
        if not openwebui_rag_service.is_ready():
            raise HTTPException(status_code=503, detail="Open WebUI RAG service not ready")
        
        # Retrieve context from collections
        context = await openwebui_rag_service.retrieve_context(prompt, collection_ids, top_k)
        
        if not context.strip():
            # No relevant context found, proceed without RAG
            request = RAGQueryRequest(prompt=prompt, model=model, top_k=0)
            response = await universal_rag_service.query_with_rag(request)
        else:
            # Create RAG-augmented prompt
            if full_context:
                # Full context mode - include all context
                augmented_prompt = f"""Context from knowledge base:
{context}

Based on the above context, please answer the following question:
{prompt}"""
            else:
                # Standard RAG mode - selective context
                augmented_prompt = f"""Use the following context to help answer the question:

Context:
{context}

Question: {prompt}

Please provide a comprehensive answer based on the context above."""
            
            # Query with augmented prompt
            request = RAGQueryRequest(prompt=augmented_prompt, model=model, top_k=0)
            response = await universal_rag_service.query_with_rag(request)
        
        # Add collection metadata to response
        if hasattr(response, 'metadata'):
            response.metadata["collections_used"] = collection_ids
            response.metadata["context_length"] = len(context)
        
        return response
    
    except Exception as e:
        logger.error(f"Error in collections query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =================== WEB CONTENT RAG ===================

@router.post("/web_content/extract_and_query", summary="Extract web content and query with model")
async def extract_web_content_and_query(
    url: str = Body(..., description="URL to extract content from"),
    query: str = Body(..., description="Question about the web content"),
    model: str = Body("gemma-3n-e4b-mediapipe", description="Model to use"),
    full_context: bool = Body(False, description="Use full context mode")
):
    """Extract content from a web URL and query it with any model"""
    try:
        import requests
        from bs4 import BeautifulSoup
        import html2text
        
        # Fetch web content
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        # Extract text from HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text content
        h = html2text.HTML2Text()
        h.ignore_links = True
        h.ignore_images = True
        text_content = h.handle(str(soup))
        
        if not text_content.strip():
            raise HTTPException(status_code=400, detail="No text content extracted from URL")
        
        # Create prompt with web content
        if full_context:
            # Include full web content
            augmented_prompt = f"""Web content from {url}:
{text_content}

Based on the above content, please answer:
{query}"""
        else:
            # Truncate if too long (basic chunking)
            max_context = 4000  # Approximate token limit
            if len(text_content) > max_context:
                text_content = text_content[:max_context] + "... [Content truncated]"
            
            augmented_prompt = f"""Web content from {url}:
{text_content}

Question: {query}

Please answer based on the web content above."""
        
        # Query model
        request = RAGQueryRequest(prompt=augmented_prompt, model=model, top_k=0)
        response = await universal_rag_service.query_with_rag(request)
        
        # Add web content metadata
        if hasattr(response, 'metadata'):
            response.metadata["source_url"] = url
            response.metadata["content_length"] = len(text_content)
        
        return response
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching web content: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to fetch content from URL: {str(e)}")
    except Exception as e:
        logger.error(f"Error in web content query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =================== SERVICE STATUS ===================

@router.get("/status", summary="Get Open WebUI RAG service status")
async def get_openwebui_rag_status():
    """Get comprehensive status of Open WebUI RAG service"""
    try:
        status = openwebui_rag_service.get_status()
        
        # Add additional information
        status.update({
            "universal_rag_available": universal_rag_service is not None,
            "supported_file_types": ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.md', '.html'],
            "features": {
                "knowledge_collections": True,
                "hybrid_search": True,
                "cross_encoder_reranking": True,
                "full_context_mode": True,
                "web_content_extraction": True,
                "multi_model_support": True
            }
        })
        
        return status
    
    except Exception as e:
        logger.error(f"Error getting service status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/config", summary="Get RAG configuration")
async def get_rag_config():
    """Get current RAG configuration"""
    try:
        config = openwebui_rag_service.config.dict()
        return {
            "status": "success",
            "config": config
        }
    except Exception as e:
        logger.error(f"Error getting RAG config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config/update", summary="Update RAG configuration")
async def update_rag_config(
    chunk_size: Optional[int] = Body(None, description="Chunk size for document splitting"),
    chunk_overlap: Optional[int] = Body(None, description="Overlap between chunks"),
    top_k: Optional[int] = Body(None, description="Default number of chunks to retrieve"),
    similarity_threshold: Optional[float] = Body(None, description="Similarity threshold for retrieval"),
    use_hybrid_search: Optional[bool] = Body(None, description="Enable hybrid search"),
    use_reranking: Optional[bool] = Body(None, description="Enable CrossEncoder reranking"),
    full_context_mode: Optional[bool] = Body(None, description="Enable full context mode")
):
    """Update RAG configuration parameters"""
    try:
        config = openwebui_rag_service.config
        
        # Update provided parameters
        if chunk_size is not None:
            config.chunk_size = chunk_size
        if chunk_overlap is not None:
            config.chunk_overlap = chunk_overlap
        if top_k is not None:
            config.top_k = top_k
        if similarity_threshold is not None:
            config.similarity_threshold = similarity_threshold
        if use_hybrid_search is not None:
            config.use_hybrid_search = use_hybrid_search
        if use_reranking is not None:
            config.use_reranking = use_reranking
        if full_context_mode is not None:
            config.full_context_mode = full_context_mode
        
        return {
            "status": "success",
            "message": "Configuration updated successfully",
            "config": config.dict()
        }
    
    except Exception as e:
        logger.error(f"Error updating RAG config: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 