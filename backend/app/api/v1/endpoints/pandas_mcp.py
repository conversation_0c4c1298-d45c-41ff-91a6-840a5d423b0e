import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Body
from typing import List, Dict, Any, Optional
import json
import pandas as pd
import tempfile
import os
from pathlib import Path

from models.llm_models import ChatResponse
from core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

# MCP client for pandas operations
class PandasMCPClient:
    """Client for interacting with the pandas MCP server through MCPO"""
    
    def __init__(self):
        # MCPO endpoint for pandas MCP server
        self.mcpo_base_url = "http://localhost:3001"  # Default MCPO port
        self.pandas_endpoint = f"{self.mcpo_base_url}/pandas"
        
    async def read_metadata(self, file_path: str) -> Dict[str, Any]:
        """Read metadata from Excel or CSV files"""
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.pandas_endpoint}/read_metadata",
                    json={"file_path": file_path},
                    timeout=30.0
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error reading metadata: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to read metadata: {str(e)}")
    
    async def run_pandas_code(self, code: str) -> Dict[str, Any]:
        """Execute pandas code with security checks"""
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.pandas_endpoint}/run_pandas_code",
                    json={"code": code},
                    timeout=60.0  # Longer timeout for code execution
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error executing pandas code: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to execute pandas code: {str(e)}")
    
    async def generate_chart(self, data: Dict, chart_types: List[str], title: str = "Data Visualization") -> Dict[str, Any]:
        """Generate interactive charts from data"""
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.pandas_endpoint}/generate_chartjs",
                    json={
                        "data": data,
                        "chart_types": chart_types,
                        "title": title
                    },
                    timeout=30.0
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error generating chart: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to generate chart: {str(e)}")

# Initialize client
pandas_mcp_client = PandasMCPClient()

@router.post("/analyze_file", summary="Analyze file metadata with pandas MCP")
async def analyze_file_metadata(file: UploadFile = File(...)):
    """
    Upload a file (Excel or CSV) and analyze its metadata using pandas MCP.
    Returns comprehensive information about the file structure, columns, and data statistics.
    """
    try:
        # Validate file type
        file_extension = Path(file.filename).suffix.lower()
        supported_types = ['.csv', '.xlsx', '.xls']
        
        if file_extension not in supported_types:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file_extension}. Supported: {supported_types}"
            )
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Analyze file metadata using pandas MCP
            metadata = await pandas_mcp_client.read_metadata(temp_file_path)
            
            return {
                "status": "success",
                "filename": file.filename,
                "metadata": metadata
            }
        
        finally:
            # Clean up temp file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing file: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/execute_code", summary="Execute pandas code with pandas MCP")
async def execute_pandas_code(
    code: str = Body(..., description="Python code string containing pandas operations"),
    file: Optional[UploadFile] = File(None, description="Optional data file for processing")
):
    """
    Execute pandas code with security checks using pandas MCP.
    If a file is provided, it will be available at '/tmp/data_file' in the code environment.
    """
    try:
        # If file is provided, save it temporarily
        temp_file_path = None
        if file:
            file_extension = Path(file.filename).suffix.lower()
            supported_types = ['.csv', '.xlsx', '.xls', '.json', '.txt']
            
            if file_extension not in supported_types:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Unsupported file type: {file_extension}. Supported: {supported_types}"
                )
            
            # Save uploaded file
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                content = await file.read()
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # Modify code to include file path
            if "file_path" not in code:
                code = f"file_path = '{temp_file_path}'\n{code}"
        
        try:
            # Execute pandas code using pandas MCP
            result = await pandas_mcp_client.run_pandas_code(code)
            
            return {
                "status": "success",
                "code": code,
                "result": result
            }
        
        finally:
            # Clean up temp file if it was created
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing pandas code: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate_chart", summary="Generate interactive charts with pandas MCP")
async def generate_interactive_chart(
    data: Dict = Body(..., description="Structured data with columns containing name, type, and examples"),
    chart_types: List[str] = Body(["bar"], description="List of chart types to generate (bar, line, pie)"),
    title: str = Body("Data Visualization", description="Chart title")
):
    """
    Generate interactive Chart.js visualizations from structured data using pandas MCP.
    """
    try:
        # Validate data structure
        if "columns" not in data:
            raise HTTPException(status_code=400, detail="Data must contain 'columns' field")
        
        # Generate chart using pandas MCP
        chart_result = await pandas_mcp_client.generate_chart(data, chart_types, title)
        
        return {
            "status": "success",
            "chart": chart_result
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating chart: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tools", summary="List available pandas MCP tools")
async def list_pandas_tools():
    """
    List all available tools provided by the pandas MCP server.
    """
    return {
        "status": "success",
        "tools": [
            {
                "name": "read_metadata",
                "description": "Read metadata from Excel or CSV files including file structure, column information, and data statistics",
                "parameters": {
                    "file_path": "Absolute path to the Excel or CSV file"
                }
            },
            {
                "name": "run_pandas_code",
                "description": "Execute pandas code for data manipulation, analysis, and processing with security checks",
                "parameters": {
                    "code": "Python code string containing pandas operations"
                }
            },
            {
                "name": "generate_chartjs",
                "description": "Generate interactive Chart.js visualizations from structured data",
                "parameters": {
                    "data": "Structured data with columns containing name, type, and examples",
                    "chart_types": "List of chart types to generate (bar, line, pie)",
                    "title": "Chart title"
                }
            }
        ]
    }

@router.get("/status", summary="Get pandas MCP service status")
async def get_pandas_mcp_status():
    """
    Get status of pandas MCP service.
    """
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{pandas_mcp_client.mcpo_base_url}/openapi.json", timeout=5.0)
            response.raise_for_status()
            return {
                "status": "available",
                "message": "Pandas MCP service is running and available",
                "mcpo_version": response.json().get("info", {}).get("version", "unknown")
            }
    except Exception as e:
        logger.error(f"Error checking pandas MCP status: {e}")
        return {
            "status": "unavailable",
            "message": f"Pandas MCP service is not available: {str(e)}"
        }