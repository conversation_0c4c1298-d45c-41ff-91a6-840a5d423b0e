"""
Default Image Processing Handler
Simple, reliable image upload and processing endpoint
"""

from fastapi import APIRouter, HTTPException, File, UploadFile, Form
from fastapi.responses import J<PERSON>NResponse
from typing import Optional, Dict, Any, List
import logging
import base64
import time
from io import BytesIO
from PIL import Image
import hashlib
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/images", tags=["Image Processing"])

# Simple in-memory stats tracking
processing_stats = {
    "total_uploads": 0,
    "successful_uploads": 0,
    "failed_uploads": 0,
    "last_upload": None
}

def validate_image(file: UploadFile) -> Dict[str, Any]:
    """Validate uploaded image file"""
    try:
        # Check content type
        if not file.content_type or not file.content_type.startswith('image/'):
            return {
                "valid": False,
                "error": f"Invalid content type: {file.content_type}. Must be an image."
            }
        
        # Check supported formats
        supported_formats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
        if file.content_type not in supported_formats:
            return {
                "valid": False,
                "error": f"Unsupported format: {file.content_type}. Supported: {', '.join(supported_formats)}"
            }
        
        return {"valid": True, "error": None}
        
    except Exception as e:
        return {"valid": False, "error": f"Validation error: {str(e)}"}

@router.post("/upload")
async def upload_image(file: UploadFile = File(...)):
    """
    Upload and process an image file
    Returns basic image information and base64 encoded data
    """
    processing_stats["total_uploads"] += 1
    start_time = time.time()
    
    try:
        # Validate image
        validation = validate_image(file)
        if not validation["valid"]:
            processing_stats["failed_uploads"] += 1
            raise HTTPException(status_code=400, detail=validation["error"])
        
        # Read image data
        image_data = await file.read()
        
        if len(image_data) == 0:
            processing_stats["failed_uploads"] += 1
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # Process with PIL to ensure it's a valid image
        try:
            image = Image.open(BytesIO(image_data))
            image.verify()  # Verify it's a valid image
            
            # Reopen for processing (verify() closes the image)
            image = Image.open(BytesIO(image_data))
            
            # Convert to RGB if necessary (for consistency)
            if image.mode not in ('RGB', 'RGBA'):
                image = image.convert('RGB')
            
        except Exception as e:
            processing_stats["failed_uploads"] += 1
            raise HTTPException(status_code=400, detail=f"Invalid image file: {str(e)}")
        
        # Generate image hash for identification
        image_hash = hashlib.md5(image_data).hexdigest()
        
        # Convert to base64 for response
        buffered = BytesIO()
        image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
        
        # Update stats
        processing_stats["successful_uploads"] += 1
        processing_stats["last_upload"] = datetime.now(timezone.utc).isoformat()
        
        processing_time = time.time() - start_time
        
        logger.info(f"Image processed successfully: {file.filename} ({len(image_data)} bytes) in {processing_time:.2f}s")
        
        return {
            "status": "success",
            "message": "Image uploaded and processed successfully",
            "data": {
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(image_data),
                "dimensions": {
                    "width": image.width,
                    "height": image.height
                },
                "format": image.format,
                "mode": image.mode,
                "hash": image_hash,
                "base64": f"data:image/png;base64,{img_base64}",
                "processing_time": round(processing_time, 3)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        processing_stats["failed_uploads"] += 1
        logger.error(f"Image processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Image processing failed: {str(e)}")

@router.post("/analyze")
async def analyze_image(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None)
):
    """
    Analyze an uploaded image and return basic information
    """
    processing_stats["total_uploads"] += 1
    start_time = time.time()
    
    try:
        # Validate image
        validation = validate_image(file)
        if not validation["valid"]:
            processing_stats["failed_uploads"] += 1
            raise HTTPException(status_code=400, detail=validation["error"])
        
        # Read and process image
        image_data = await file.read()
        image = Image.open(BytesIO(image_data))
        
        # Basic image analysis
        analysis = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size_bytes": len(image_data),
            "dimensions": {
                "width": image.width,
                "height": image.height
            },
            "format": image.format,
            "mode": image.mode,
            "has_transparency": image.mode in ('RGBA', 'LA') or 'transparency' in image.info,
            "aspect_ratio": round(image.width / image.height, 2),
            "megapixels": round((image.width * image.height) / 1000000, 2)
        }
        
        # Add description if provided
        if description:
            analysis["user_description"] = description
        
        # Simple categorization based on dimensions
        if image.width > image.height:
            analysis["orientation"] = "landscape"
        elif image.height > image.width:
            analysis["orientation"] = "portrait"
        else:
            analysis["orientation"] = "square"
        
        # Size category
        total_pixels = image.width * image.height
        if total_pixels < 100000:  # < 0.1MP
            analysis["size_category"] = "small"
        elif total_pixels < 2000000:  # < 2MP
            analysis["size_category"] = "medium"
        else:
            analysis["size_category"] = "large"
        
        processing_stats["successful_uploads"] += 1
        processing_time = time.time() - start_time
        
        logger.info(f"Image analyzed: {file.filename} in {processing_time:.2f}s")
        
        return {
            "status": "success",
            "message": "Image analyzed successfully",
            "analysis": analysis,
            "processing_time": round(processing_time, 3)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        processing_stats["failed_uploads"] += 1
        logger.error(f"Image analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")

@router.get("/stats")
async def get_processing_stats():
    """Get image processing statistics"""
    return {
        "status": "success",
        "stats": processing_stats,
        "supported_formats": [
            "image/jpeg",
            "image/jpg", 
            "image/png",
            "image/webp",
            "image/gif"
        ]
    }

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Image Processing Handler",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }