from fastapi import APIRouter, HTTPException, Depends
from app.core.mem0_config import Mem0Config, EmbeddingProviderConfig
from app.services.memory_service import memory_service
import json
import os
from typing import Optional
from pathlib import Path

router = APIRouter()

CONFIG_FILE = "data/mem0_config.json"
NEW_CONFIG_FILE = "backend/app/config/mem0_config.json"

def load_config() -> Mem0Config:
    """Load configuration from file"""
    try:
        # Try new config location first
        new_config_path = Path(NEW_CONFIG_FILE)
        if new_config_path.exists():
            with open(new_config_path, 'r') as f:
                config_data = json.load(f)
                # Convert new format to Mem0Config
                return convert_new_config_to_mem0_config(config_data)
        
        # Fallback to old config location
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r') as f:
                config_data = json.load(f)
                return Mem0Config(**config_data)
    except Exception as e:
        print(f"Error loading config: {e}")
    return Mem0Config()

def convert_new_config_to_mem0_config(config_data: dict) -> Mem0Config:
    """Convert new config format to Mem0Config"""
    if "provider" in config_data and "config" in config_data:
        # New format
        provider_config = EmbeddingProviderConfig(
            provider=config_data["provider"],
            model=config_data["config"].get("model"),
            api_key=config_data["config"].get("api_key"),
            dimensions=config_data["config"].get("dimensions"),
            task=config_data["config"].get("task")
        )
        return Mem0Config(embedding_provider=provider_config)
    else:
        # Old format or direct Mem0Config
        return Mem0Config(**config_data)

def save_config(config: Mem0Config):
    """Save configuration to file"""
    os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
    with open(CONFIG_FILE, 'w') as f:
        json.dump(config.dict(), f, indent=2)

@router.get("/settings", response_model=Mem0Config, tags=["Memory Settings"])
async def get_settings():
    """Get current Mem0 settings"""
    return load_config()

@router.post("/settings", response_model=Mem0Config, tags=["Memory Settings"])
async def update_settings(config: Mem0Config):
    """Update Mem0 settings"""
    try:
        # Save configuration
        save_config(config)
        
        # Convert to new format and save to memory service location
        await save_to_memory_service_config(config)
        
        # Update environment variable if API key is provided (legacy support)
        if config.openai_api_key:
            os.environ["OPENAI_API_KEY"] = config.openai_api_key
        
        # Update environment variables for new providers
        if config.embedding_provider:
            provider = config.embedding_provider.provider
            api_key = config.embedding_provider.api_key
            
            if provider == "jina" and api_key:
                os.environ["JINA_API_KEY"] = api_key
            elif provider == "google_ai" and api_key:
                os.environ["GOOGLE_AI_API_KEY"] = api_key
            elif provider == "openai" and api_key:
                os.environ["OPENAI_API_KEY"] = api_key
        
        # Reinitialize memory service with new config
        if memory_service:
            memory_service._initialize_mem0()
        
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def save_to_memory_service_config(config: Mem0Config):
    """Save config in format expected by memory service"""
    try:
        config_dir = Path("backend/app/config")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        if config.embedding_provider:
            # Save in new format
            new_config = {
                "provider": config.embedding_provider.provider,
                "config": {
                    "model": config.embedding_provider.model,
                    "api_key": config.embedding_provider.api_key,
                    "dimensions": config.embedding_provider.dimensions
                }
            }
            
            if config.embedding_provider.task:
                new_config["config"]["task"] = config.embedding_provider.task
            
            with open(config_dir / "mem0_config.json", 'w') as f:
                json.dump(new_config, f, indent=2)
        
        elif config.openai_api_key:
            # Legacy OpenAI format
            legacy_config = {
                "provider": "openai",
                "config": {
                    "model": "text-embedding-3-small",
                    "api_key": config.openai_api_key,
                    "dimensions": 1536
                }
            }
            
            with open(config_dir / "mem0_config.json", 'w') as f:
                json.dump(legacy_config, f, indent=2)
    
    except Exception as e:
        print(f"Error saving to memory service config: {e}")

@router.post("/settings/test", tags=["Memory Settings"])
async def test_settings():
    """Test current Mem0 settings"""
    try:
        config = load_config()
        
        # Test new embedding provider if configured
        if config.embedding_provider and config.embedding_provider.api_key:
            return await test_embedding_provider(config.embedding_provider)
        
        # Test legacy OpenAI API key if provided
        elif config.openai_api_key:
            return await test_openai_legacy(config.openai_api_key)
        
        return {"status": "warning", "message": "No embedding provider or API key configured"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def test_embedding_provider(provider_config: EmbeddingProviderConfig):
    """Test embedding provider configuration"""
    try:
        from mem0 import Memory
        
        # Create Mem0 config
        mem0_config = {
            "embedder": {
                "provider": provider_config.provider,
                "config": {
                    "model": provider_config.model,
                    "api_key": provider_config.api_key,
                    "dimensions": provider_config.dimensions
                }
            }
        }
        
        if provider_config.task:
            mem0_config["embedder"]["config"]["task"] = provider_config.task
        
        # Test initialization and basic operation
        m = Memory.from_config(mem0_config)
        
        # Test add and search
        test_text = f"Test message for {provider_config.provider} provider"
        result = m.add(test_text, user_id="test_settings_user")
        
        if result:
            search_result = m.search("test message", user_id="test_settings_user", limit=1)
            if search_result and search_result.get("results"):
                return {
                    "status": "success", 
                    "message": f"{provider_config.provider.title()} embedding provider tested successfully",
                    "provider": provider_config.provider,
                    "model": provider_config.model
                }
        
        return {
            "status": "error", 
            "message": f"Failed to test {provider_config.provider} provider"
        }
        
    except Exception as e:
        return {
            "status": "error", 
            "message": f"Error testing {provider_config.provider} provider: {str(e)}"
        }

async def test_openai_legacy(api_key: str):
    """Test legacy OpenAI configuration"""
    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key)
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=5
        )
        if response:
            return {
                "status": "success", 
                "message": "Legacy OpenAI settings tested successfully",
                "provider": "openai"
            }
    except Exception as e:
        return {
            "status": "error", 
            "message": f"Legacy OpenAI test failed: {str(e)}"
        }

@router.get("/providers", tags=["Memory Settings"])
async def get_available_providers():
    """Get list of available embedding providers"""
    providers = {
        "openai": {
            "name": "OpenAI",
            "models": [
                {"name": "text-embedding-3-small", "dimensions": 1536},
                {"name": "text-embedding-3-large", "dimensions": 3072},
                {"name": "text-embedding-ada-002", "dimensions": 1536}
            ],
            "requires_api_key": True,
            "pricing": "paid"
        },
        "jina": {
            "name": "Jina.ai",
            "models": [
                {
                    "name": "jina-embeddings-v3", 
                    "dimensions": 1024,
                    "tasks": ["text-matching", "retrieval", "classification", "clustering"]
                },
                {"name": "jina-embeddings-v2-base-en", "dimensions": 768},
                {"name": "jina-embeddings-v2-small-en", "dimensions": 512},
                {"name": "jina-clip-v2", "dimensions": 768, "multimodal": True}
            ],
            "requires_api_key": True,
            "pricing": "freemium"
        },
        "google_ai": {
            "name": "Google AI Studio",
            "models": [
                {"name": "text-embedding-004", "dimensions": 768},
                {"name": "text-embedding-003", "dimensions": 768},
                {"name": "embedding-001", "dimensions": 768}
            ],
            "requires_api_key": True,
            "pricing": "free"
        }
    }
    
    return {
        "providers": providers,
        "current_config": load_config()
    }

@router.post("/sync", tags=["Memory Settings"])
async def sync_memory_service():
    """Sync memory service with current settings"""
    try:
        if memory_service:
            # Reinitialize memory service
            memory_service._initialize_mem0()
            
            # Get current memory service settings
            settings = memory_service.get_settings()
            
            return {
                "status": "success",
                "message": "Memory service synced successfully",
                "memory_service_settings": settings
            }
        else:
            raise HTTPException(status_code=503, detail="Memory service not available")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error syncing memory service: {str(e)}")

@router.get("/status", tags=["Memory Settings"])
async def get_memory_service_status():
    """Get memory service status and configuration"""
    try:
        if memory_service:
            settings = memory_service.get_settings()
            
            # Check if Mem0 is initialized
            mem0_status = "initialized" if memory_service._mem0 else "not_initialized"
            
            # Try to load current embedding config
            embedding_config = memory_service._load_embedding_config()
            
            return {
                "service_available": True,
                "mem0_status": mem0_status,
                "settings": settings,
                "embedding_config": embedding_config,
                "config_locations_checked": [
                    "backend/app/config/mem0_config.json",
                    "app/config/mem0_config.json", 
                    "data/mem0_embedding_config.json"
                ]
            }
        else:
            return {
                "service_available": False,
                "message": "Memory service not available"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting memory service status: {str(e)}") 