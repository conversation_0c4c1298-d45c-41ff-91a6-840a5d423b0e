import logging
from fastapi import APIRouter, HTTPException, Body, Request
from services.llm_service import llm_service
from services.universal_rag_service import universal_rag_service
from models.llm_models import ChatRequest, ChatResponse
from models.rag_models import RAGQueryRequest
from app.services.memory_service import memory_service
from pydantic import BaseModel
from typing import List, Optional

logger = logging.getLogger(__name__)
router = APIRouter()

def extract_user_id(request: Request) -> str:
    """Extract user ID from request headers or use default"""
    # Try to get user ID from various sources
    user_id = request.headers.get("X-User-ID")
    if not user_id:
        user_id = request.headers.get("Authorization", "").replace("Bearer ", "")
    if not user_id:
        # Check for query parameters
        user_id = request.query_params.get("user_id")
    if not user_id:
        user_id = "default_user"
    
    return user_id

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = "default"
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    message: ChatMessage
    model: str

@router.post("/", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat endpoint that processes messages and returns responses.
    """
    try:
        # For now, just echo back the last message
        last_message = request.messages[-1]
        return {
            "message": ChatMessage(
                role="assistant",
                content=f"Echo: {last_message.content}"
            ),
            "model": request.model
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing chat request: {str(e)}"
        )

@router.post("/chat/completions", response_model=ChatResponse, summary="OpenAI-compatible chat completions endpoint")
async def create_chat_completion(chat_request: ChatRequest, request: Request):
    """
    OpenAI-compatible chat completions endpoint with memory enhancement
    """
    try:
        # Extract user ID
        user_id = extract_user_id(request)
        logger.debug(f"Processing chat for user: {user_id}")
        
        # Convert ChatRequest messages to dict format for memory service
        messages = [{"role": msg.role, "content": msg.content} for msg in chat_request.messages]
        
        # Enhance messages with memory context
        enhanced_messages = await memory_service.enhance_prompt_with_memories(
            messages, user_id, limit=3
        )
        
        # Convert back to ChatMessage format
        enhanced_chat_messages = [ChatMessage(role=msg["role"], content=msg["content"]) for msg in enhanced_messages]
        enhanced_request = ChatRequest(
            messages=enhanced_chat_messages,
            model=chat_request.model,
            temperature=chat_request.temperature,
            max_tokens=chat_request.max_tokens
        )
        
        # Process chat request
        response = await llm_service.process_chat(enhanced_request)
        
        if response.error:
            raise HTTPException(status_code=500, detail=response.error)
        
        # Save conversation to memory
        conversation_messages = messages + [{"role": "assistant", "content": response.message.content}]
        await memory_service.add_memory(conversation_messages, user_id)
        
        return response
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"Error in chat completions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models", summary="List available models (OpenAI-compatible endpoint)")
async def list_models():
    """
    OpenAI-compatible models endpoint for Open WebUI integration.
    Now includes all models (local and external APIs) with and without RAG support.
    """
    logger.info("Models endpoint was called.")
    
    try:
        # Get original models (non-RAG)
        original_models = [
            {
                "id": "gemma-3n-e4b-mediapipe",
                "object": "model",
                "created": 1704067200,
                "owned_by": "google-mediapipe"
            }
        ]
        
        # Get Universal RAG models
        rag_models_response = universal_rag_service.get_available_models()
        rag_models = rag_models_response.get("data", [])
        
        # Add external API models (non-RAG versions)
        external_models = []
        
        # Add OpenAI models if API key available
        if hasattr(universal_rag_service.clients, 'openai') and 'openai' in universal_rag_service.clients:
            external_models.extend([
                {"id": "gpt-3.5-turbo", "object": "model", "created": 1704067200, "owned_by": "openai"},
                {"id": "gpt-4", "object": "model", "created": 1704067200, "owned_by": "openai"},
                {"id": "gpt-4o", "object": "model", "created": 1704067200, "owned_by": "openai"},
                {"id": "gpt-4-turbo", "object": "model", "created": 1704067200, "owned_by": "openai"}
            ])
        
        # Add Gemini models if API key available
        if 'gemini' in universal_rag_service.clients:
            external_models.extend([
                {"id": "gemini-pro", "object": "model", "created": 1704067200, "owned_by": "google"},
                {"id": "gemini-1.5-flash", "object": "model", "created": 1704067200, "owned_by": "google"},
                {"id": "gemini-1.5-pro", "object": "model", "created": 1704067200, "owned_by": "google"}
            ])
        
        # Add DeepSeek models if API key available
        if 'deepseek' in universal_rag_service.clients:
            external_models.extend([
                {"id": "deepseek-chat", "object": "model", "created": 1704067200, "owned_by": "deepseek"},
                {"id": "deepseek-coder", "object": "model", "created": 1704067200, "owned_by": "deepseek"}
            ])
        
        # Combine all models
        all_models = original_models + external_models + rag_models
        
        logger.info(f"Listed {len(all_models)} total models ({len(original_models)} original, {len(external_models)} external, {len(rag_models)} RAG)")
        
        return {
            "object": "list",
            "data": all_models
        }
        
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        # Fallback to original models if there's an error
        return {
            "object": "list",
            "data": [
                {
                    "id": "gemma-3n-e4b-mediapipe",
                    "object": "model",
                    "created": 1704067200,
                    "owned_by": "google-mediapipe"
                }
            ]
        }
