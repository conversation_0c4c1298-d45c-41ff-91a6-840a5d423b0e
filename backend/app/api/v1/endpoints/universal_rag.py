import logging
from fastapi import APIRouter, HTTPException, Body, Query
from typing import List, Dict, Any, Optional

from services.universal_rag_service import universal_rag_service
from models.rag_models import RAGQueryRequest
from models.llm_models import ChatResponse

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/models", summary="List all models with RAG support")
async def list_rag_models():
    """
    List all available models (local and external APIs) with RAG support enabled.
    Each model will have '-rag' suffix to indicate RAG capability.
    """
    try:
        models = universal_rag_service.get_available_models()
        logger.info(f"Listed {len(models['data'])} RAG-enabled models")
        return models
    except Exception as e:
        logger.error(f"Error listing RAG models: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing models: {str(e)}")

@router.post("/query", response_model=ChatResponse, summary="Universal RAG query for any model")
async def universal_rag_query(request: RAGQueryRequest = Body(...)):
    """
    Universal RAG query endpoint that works with any model (local or external API).
    
    Supported models:
    - Local: gemma-3n-e4b-mediapipe, gemma-2b-it, gemma-3-4b
    - OpenAI: gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4-turbo
    - Google: gemini-pro, gemini-1.5-flash, gemini-1.5-pro
    - DeepSeek: deepseek-chat, deepseek-coder
    
    The system will:
    1. Retrieve relevant context from uploaded documents
    2. Augment the prompt with context
    3. Route to the specified model
    4. Return response with RAG enhancement
    """
    if not request.prompt:
        raise HTTPException(status_code=400, detail="Prompt cannot be empty")
    
    logger.info(f"Universal RAG query: model={request.model}, prompt={request.prompt[:100]}...")
    
    try:
        response = await universal_rag_service.query_with_rag(request)
        
        if response.error:
            # Check for specific errors that might indicate service issues
            if "not ready" in response.error.lower() or "not available" in response.error.lower():
                raise HTTPException(status_code=503, detail=response.error)
            raise HTTPException(status_code=500, detail=response.error)
        
        return response
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.exception(f"Unexpected error in universal RAG query: {e}")
        raise HTTPException(status_code=500, detail=f"Unexpected server error: {str(e)}")

@router.get("/status", summary="Get Universal RAG service status")
async def get_universal_rag_status() -> Dict[str, Any]:
    """
    Get status of Universal RAG service including available clients and models.
    """
    try:
        status = {
            "service": "Universal RAG Service",
            "available_clients": list(universal_rag_service.clients.keys()),
            "total_models": len(universal_rag_service.model_mapping),
            "available_models": len(universal_rag_service.clients),
            "model_mapping": universal_rag_service.model_mapping,
            "rag_ready": True  # Since we're using the existing rag_service
        }
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting universal RAG status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting status: {str(e)}")

@router.post("/query_specific", response_model=ChatResponse, summary="Query specific model with RAG")
async def query_specific_model(
    prompt: str = Body(..., description="The query prompt"),
    model: str = Body(..., description="Specific model to use"),
    top_k: Optional[int] = Body(None, description="Number of context chunks to retrieve")
):
    """
    Query a specific model with RAG support.
    Simplified endpoint for direct model selection.
    """
    request = RAGQueryRequest(
        prompt=prompt,
        model=model,
        top_k=top_k
    )
    
    return await universal_rag_query(request)

@router.get("/supported_models", summary="Get list of supported model types")
async def get_supported_models() -> Dict[str, List[str]]:
    """
    Get categorized list of all supported models.
    """
    try:
        categorized = {
            "local_models": [],
            "openai_models": [],
            "google_models": [],
            "deepseek_models": [],
            "available_now": [],
            "requires_api_key": []
        }
        
        for model_id, client_type in universal_rag_service.model_mapping.items():
            # Categorize by type
            if client_type.startswith("gemma"):
                categorized["local_models"].append(model_id)
            elif client_type == "openai":
                categorized["openai_models"].append(model_id)
            elif client_type == "gemini":
                categorized["google_models"].append(model_id)
            elif client_type == "deepseek":
                categorized["deepseek_models"].append(model_id)
            
            # Check availability
            if client_type in universal_rag_service.clients:
                categorized["available_now"].append(model_id)
            else:
                categorized["requires_api_key"].append(model_id)
        
        return categorized
        
    except Exception as e:
        logger.error(f"Error getting supported models: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting models: {str(e)}")

@router.post("/test_model", summary="Test if a specific model works with RAG")
async def test_model_rag(
    model: str = Body(..., description="Model to test"),
    test_prompt: str = Body("Hello, this is a test.", description="Test prompt")
) -> Dict[str, Any]:
    """
    Test if a specific model works with RAG.
    Useful for debugging and verification.
    """
    try:
        request = RAGQueryRequest(
            prompt=test_prompt,
            model=model,
            top_k=1
        )
        
        response = await universal_rag_service.query_with_rag(request)
        
        return {
            "model": model,
            "test_prompt": test_prompt,
            "success": response.error is None,
            "response_preview": response.response[:200] if response.response else None,
            "error": response.error,
            "provider": response.provider.value if response.provider else None
        }
        
    except Exception as e:
        logger.error(f"Error testing model {model}: {e}")
        return {
            "model": model,
            "test_prompt": test_prompt,
            "success": False,
            "response_preview": None,
            "error": str(e),
            "provider": None
        } 