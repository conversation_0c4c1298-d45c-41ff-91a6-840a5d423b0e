from fastapi import APIRouter, HTTPException, Depends, Request, Query
from typing import List, Dict, Optional
from pydantic import BaseModel
from ....services.memory_service import memory_service
from ....config.memory_config import Mem0Config
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

class UserFeedbackRequest(BaseModel):
    memory_id: str
    feedback: str  # "positive", "negative", "neutral"

class MemorySearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 3
    filters: Optional[Dict] = None

class EnhancedMemoryRequest(BaseModel):
    messages: List[Dict]
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None
    metadata: Optional[Dict] = None

def extract_user_id(request: Request) -> str:
    """Extract user ID from request headers or use default"""
    user_id = request.headers.get("X-User-ID")
    if not user_id:
        user_id = request.headers.get("Authorization", "").replace("Bearer ", "")
    if not user_id:
        user_id = request.query_params.get("user_id")
    if not user_id:
        user_id = "default_user"
    return user_id

@router.get("/settings", response_model=Mem0Config, tags=["Memory"])
async def get_settings():
    """Get current memory settings"""
    try:
        return memory_service.get_settings()
    except Exception as e:
        logger.error(f"Error getting settings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/settings", response_model=Mem0Config, tags=["Memory"])
async def update_settings(config: Mem0Config):
    """Update memory settings"""
    try:
        return memory_service.update_settings(config.dict())
    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/add", tags=["Memory"])
async def add_memory(request_data: EnhancedMemoryRequest, request: Request):
    """Add a new memory from conversation messages with enhanced metadata"""
    try:
        user_id = extract_user_id(request)
        await memory_service.add_memory(
            messages=request_data.messages,
            user_id=user_id,
            session_id=request_data.session_id,
            conversation_id=request_data.conversation_id,
            metadata=request_data.metadata
        )
        return {"status": "success", "message": "Enhanced memory added successfully"}
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/add/simple", tags=["Memory"])
async def add_simple_memory(messages: List[Dict], request: Request):
    """Add a simple memory (backward compatibility)"""
    try:
        user_id = extract_user_id(request)
        await memory_service.add_memory(messages, user_id)
        return {"status": "success", "message": "Memory added successfully"}
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/memories/search", tags=["Memory"])
async def search_memories(
    query: str,
    request: Request,
    limit: Optional[int] = 3,
    session_id: Optional[str] = Query(None),
    source: Optional[str] = Query(None),
    tags: Optional[str] = Query(None)
):
    """Search for relevant memories with metadata filtering"""
    try:
        user_id = extract_user_id(request)
        
        # Build filters from query parameters
        filters = {}
        if session_id:
            filters['session_id'] = session_id
        if source:
            filters['source'] = source
        if tags:
            filters['tags'] = tags.split(',') if ',' in tags else [tags]
        
        memories = await memory_service.get_relevant_memories(
            query, user_id, limit, filters if filters else None
        )
        return memories
    except Exception as e:
        logger.error(f"Error searching memories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/search/advanced", tags=["Memory"])
async def advanced_search_memories(search_request: MemorySearchRequest, request: Request):
    """Advanced search for memories with complex filtering"""
    try:
        user_id = extract_user_id(request)
        memories = await memory_service.get_relevant_memories(
            search_request.query,
            user_id,
            search_request.limit,
            search_request.filters
        )
        return memories
    except Exception as e:
        logger.error(f"Error in advanced search: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/enhance", tags=["Memory"])
async def enhance_prompt(
    messages: List[Dict],
    request: Request,
    limit: Optional[int] = 3,
    session_id: Optional[str] = None
):
    """Enhance conversation with relevant memories using metadata"""
    try:
        user_id = extract_user_id(request)
        enhanced_messages = await memory_service.enhance_prompt_with_memories(
            messages, user_id, limit, session_id
        )
        return {"enhanced_messages": enhanced_messages}
    except Exception as e:
        logger.error(f"Error enhancing prompt: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/memories", tags=["Memory"])
async def get_all_memories(request: Request):
    """Get all memories for the current user"""
    try:
        user_id = extract_user_id(request)
        memories = await memory_service.get_all_memories(user_id)
        return {"memories": memories}
    except Exception as e:
        logger.error(f"Error getting all memories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/feedback", tags=["Memory"])
async def add_user_feedback(feedback_request: UserFeedbackRequest, request: Request):
    """Add user feedback to a memory for learning purposes"""
    try:
        user_id = extract_user_id(request)
        await memory_service.add_user_feedback(
            feedback_request.memory_id,
            feedback_request.feedback,
            user_id
        )
        return {"status": "success", "message": "Feedback added successfully"}
    except Exception as e:
        logger.error(f"Error adding feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/memories/analytics", tags=["Memory"])
async def get_memory_analytics(request: Request):
    """Get analytics about user's memory usage"""
    try:
        user_id = extract_user_id(request)
        analytics = await memory_service.get_memory_analytics(user_id)
        return analytics
    except Exception as e:
        logger.error(f"Error getting analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/memories/{memory_id}", tags=["Memory"])
async def delete_memory(memory_id: str, request: Request):
    """Delete a specific memory"""
    try:
        user_id = extract_user_id(request)
        await memory_service.delete_memory(memory_id, user_id)
        return {"status": "success", "message": f"Memory {memory_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting memory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))