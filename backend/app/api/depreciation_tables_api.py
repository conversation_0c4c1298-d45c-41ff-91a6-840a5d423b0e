"""
Depreciation Tables API for MobiFone RAG System
Provides access to extracted depreciation tables from VB 94
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
import sqlite3
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/depreciation", tags=["depreciation"])

DB_PATH = Path(__file__).parent.parent / "rag" / "enhanced_tables.db"

@router.get("/tables")
async def get_depreciation_tables(
    limit: int = Query(10, ge=1, le=100),
    min_confidence: float = Query(0.6, ge=0.0, le=1.0)
) -> Dict[str, Any]:
    """
    Get depreciation tables from VB 94 with filtering options
    """
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # Get depreciation tables
        cursor.execute('''
            SELECT source_file, table_type, confidence_score, content, 
                   headers, metadata, created_at
            FROM enhanced_table_chunks 
            WHERE (content LIKE '%khấu hao%' OR content LIKE '%depreciat%')
              AND confidence_score >= ?
            ORDER BY confidence_score DESC
            LIMIT ?
        ''', (min_confidence, limit))
        
        results = cursor.fetchall()
        
        tables = []
        for row in results:
            source_file, table_type, confidence, content, headers, metadata, created_at = row
            
            # Parse JSON fields safely
            try:
                headers_list = json.loads(headers) if headers else []
                metadata_dict = json.loads(metadata) if metadata else {}
            except json.JSONDecodeError:
                headers_list = []
                metadata_dict = {}
            
            tables.append({
                "source_file": source_file,
                "table_type": table_type,
                "confidence_score": confidence,
                "content": content,
                "headers": headers_list,
                "metadata": metadata_dict,
                "created_at": created_at
            })
        
        conn.close()
        
        return {
            "total_tables": len(tables),
            "tables": tables,
            "summary": {
                "avg_confidence": sum(t["confidence_score"] for t in tables) / len(tables) if tables else 0,
                "table_types": list(set(t["table_type"] for t in tables)),
                "source_documents": list(set(t["source_file"] for t in tables))
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching depreciation tables: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/summary")
async def get_depreciation_summary() -> Dict[str, Any]:
    """
    Get summary of depreciation information from MobiFone VB 94
    """
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # Get statistics
        cursor.execute('''
            SELECT 
                COUNT(*) as total_count,
                AVG(confidence_score) as avg_confidence,
                MAX(confidence_score) as max_confidence,
                MIN(confidence_score) as min_confidence
            FROM enhanced_table_chunks 
            WHERE content LIKE '%khấu hao%' OR content LIKE '%depreciat%'
        ''')
        
        stats = cursor.fetchone()
        
        # Get table types breakdown
        cursor.execute('''
            SELECT table_type, COUNT(*) as count
            FROM enhanced_table_chunks 
            WHERE content LIKE '%khấu hao%' OR content LIKE '%depreciat%'
            GROUP BY table_type
            ORDER BY count DESC
        ''')
        
        type_breakdown = dict(cursor.fetchall())
        
        # Get key regulations mentioned
        cursor.execute('''
            SELECT content
            FROM enhanced_table_chunks 
            WHERE (content LIKE '%khấu hao%' OR content LIKE '%depreciat%')
              AND content LIKE '%TT-BTC%'
            ORDER BY confidence_score DESC
            LIMIT 3
        ''')
        
        regulations = []
        for (content,) in cursor.fetchall():
            if "TT-BTC" in content:
                lines = content.split('\n')
                for line in lines:
                    if "TT-BTC" in line and line.strip():
                        regulations.append(line.strip())
                        break
        
        conn.close()
        
        return {
            "statistics": {
                "total_depreciation_tables": stats[0],
                "average_confidence": round(stats[1], 2) if stats[1] else 0,
                "highest_confidence": stats[2],
                "lowest_confidence": stats[3]
            },
            "table_types": type_breakdown,
            "key_regulations": list(set(regulations))[:5],
            "summary_text": f"""
            📊 TỔNG QUAN KHẤU HAO MOBIFONE (VB 94):
            - Tổng số bảng khấu hao: {stats[0]}
            - Độ tin cậy trung bình: {round(stats[1], 2) if stats[1] else 0}
            - Căn cứ pháp lý: Thông tư 200/2014/TT-BTC, Thông tư 45/2013/TT-BTC
            - Phạm vi: Quy trình quản lý tài sản cố định và công cụ dụng cụ
            """
        }
        
    except Exception as e:
        logger.error(f"Error generating depreciation summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search")
async def search_depreciation_content(
    query: str = Query(..., min_length=1),
    limit: int = Query(5, ge=1, le=20)
) -> Dict[str, Any]:
    """
    Search depreciation table content
    """
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # Search in content
        search_pattern = f"%{query}%"
        cursor.execute('''
            SELECT source_file, table_type, confidence_score, content, headers
            FROM enhanced_table_chunks 
            WHERE (content LIKE '%khấu hao%' OR content LIKE '%depreciat%')
              AND content LIKE ?
            ORDER BY confidence_score DESC
            LIMIT ?
        ''', (search_pattern, limit))
        
        results = cursor.fetchall()
        
        matches = []
        for row in results:
            source_file, table_type, confidence, content, headers = row
            
            # Extract relevant snippet
            lines = content.split('\n')
            relevant_lines = []
            for line in lines:
                if query.lower() in line.lower():
                    relevant_lines.append(line.strip())
            
            matches.append({
                "source_file": source_file,
                "table_type": table_type,
                "confidence_score": confidence,
                "relevant_content": relevant_lines[:3],  # Top 3 matching lines
                "full_content_preview": content[:200] + "..." if len(content) > 200 else content
            })
        
        conn.close()
        
        return {
            "query": query,
            "total_matches": len(matches),
            "matches": matches
        }
        
    except Exception as e:
        logger.error(f"Error searching depreciation content: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 