from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Optional
from app.services.memory_service import memory_service
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/memories/add", tags=["Memory"])
async def add_memory(messages: List[Dict], user_id: Optional[str] = "default_user"):
    """
    Add a new memory from conversation messages
    """
    try:
        await memory_service.add_memory(messages, user_id)
        return {"status": "success", "message": "Memory added successfully"}
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/memories/search", tags=["Memory"])
async def search_memories(query: str, user_id: Optional[str] = "default_user", limit: Optional[int] = 3):
    """
    Search for relevant memories
    """
    try:
        memories = await memory_service.get_relevant_memories(query, user_id, limit)
        return memories
    except Exception as e:
        logger.error(f"Error searching memories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/enhance", tags=["Memory"])
async def enhance_prompt(messages: List[Dict], user_id: Optional[str] = "default_user", limit: Optional[int] = 3):
    """
    Enhance conversation with relevant memories
    """
    try:
        enhanced_messages = await memory_service.enhance_prompt_with_memories(messages, user_id, limit)
        return {"enhanced_messages": enhanced_messages}
    except Exception as e:
        logger.error(f"Error enhancing prompt: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/memories/clear/{user_id}", tags=["Memory"])
async def clear_memories(user_id: str = "default_user"):
    """
    Clear all memories for a specific user
    """
    try:
        await memory_service.clear_memories(user_id)
        return {"status": "success", "message": f"Memories cleared for user {user_id}"}
    except Exception as e:
        logger.error(f"Error clearing memories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 