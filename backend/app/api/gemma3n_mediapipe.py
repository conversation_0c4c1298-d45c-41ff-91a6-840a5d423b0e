"""
Gemma 3n E4B MediaPipe API
Sử dụng MediaPipe LLM Inference API - đ<PERSON><PERSON> cách như Google AI Edge Gallery
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import logging

logger = logging.getLogger(__name__)

try:
    from services.gemma3n_mediapipe_service import gemma3n_mediapipe_service
    MEDIAPIPE_SERVICE_AVAILABLE = True
    logger.info("✅ MediaPipe service imported successfully")
except ImportError as e:
    MEDIAPIPE_SERVICE_AVAILABLE = False
    logger.error(f"❌ Could not import MediaPipe service: {e}")

router = APIRouter()

class MediaPipeRequest(BaseModel):
    text: str
    max_tokens: Optional[int] = 100
    temperature: Optional[float] = 0.7

class MediaPipeResponse(BaseModel):
    response: str
    model_info: dict

@router.post("/test-mediapipe-gemma3n", response_model=MediaPipeResponse)
async def test_mediapipe_gemma3n(request: MediaPipeRequest):
    """Test MediaPipe Gemma 3n E4B inference"""
    try:
        if not MEDIAPIPE_SERVICE_AVAILABLE:
            raise HTTPException(status_code=503, detail="MediaPipe service not available")
        
        # Generate response using MediaPipe LLM Inference API
        response = await gemma3n_mediapipe_service.generate_response(
            text=request.text,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # Get model info
        model_info = await gemma3n_mediapipe_service.get_model_info()
        
        return MediaPipeResponse(
            response=response,
            model_info=model_info
        )
        
    except Exception as e:
        logger.error(f"MediaPipe API error: {e}")
        raise HTTPException(status_code=500, detail=f"MediaPipe inference failed: {str(e)}")

@router.get("/mediapipe-status")
async def get_mediapipe_status():
    """Get MediaPipe service status"""
    try:
        if not MEDIAPIPE_SERVICE_AVAILABLE:
            return {
                "available": False,
                "error": "MediaPipe service not imported"
            }
        
        model_info = await gemma3n_mediapipe_service.get_model_info()
        
        return {
            "available": gemma3n_mediapipe_service.is_available(),
            "model_info": model_info
        }
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        return {
            "available": False,
            "error": str(e)
        } 