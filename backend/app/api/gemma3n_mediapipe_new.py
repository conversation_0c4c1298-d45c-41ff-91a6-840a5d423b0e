"""
API Router for Gemma 3N E4B MediaPipe Service
Following Google AI Edge MediaPipe LLM Inference documentation
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

from app.services.gemma3n_mediapipe_service import Gemma3nMediaPipeService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize service
try:
    gemma3n_mediapipe_service = Gemma3nMediaPipeService()
    logger.info("✅ Gemma 3N E4B MediaPipe service initialized")
except Exception as e:
    logger.error(f"❌ Failed to initialize Gemma 3N E4B MediaPipe service: {e}")
    gemma3n_mediapipe_service = None

class ChatRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 512
    temperature: Optional[float] = 0.8
    top_k: Optional[int] = 40

class ChatResponse(BaseModel):
    response: str
    model: str
    status: str
    metadata: Optional[Dict[str, Any]] = None

@router.post("/chat", response_model=ChatResponse)
async def chat_with_gemma3n_mediapipe(request: ChatRequest):
    """
    Chat with Gemma 3N E4B using MediaPipe LLM Inference
    Following Google AI Edge official documentation
    """
    try:
        if not gemma3n_mediapipe_service or not gemma3n_mediapipe_service.is_available():
            raise HTTPException(
                status_code=503, 
                detail="Gemma 3N E4B MediaPipe service is not available"
            )
        
        logger.info(f"Processing MediaPipe chat request: '{request.message[:50]}...'")
        
        # Generate response using MediaPipe
        response = gemma3n_mediapipe_service.generate_response(
            text=request.message,
            max_tokens=request.max_tokens
        )
        
        # Get model info for metadata
        model_info = gemma3n_mediapipe_service.get_model_info()
        
        return ChatResponse(
            response=response,
            model="gemma-3n-e4b-mediapipe",
            status="success",
            metadata={
                "framework": model_info.get("framework"),
                "approach": model_info.get("approach"),
                "version": model_info.get("version"),
                "loaded": model_info.get("loaded"),
                "config": model_info.get("config"),
                "documentation": model_info.get("documentation")
            }
        )
        
    except Exception as e:
        logger.error(f"MediaPipe chat request failed: {e}")
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")

@router.get("/status")
async def get_gemma3n_mediapipe_status():
    """Get Gemma 3N E4B MediaPipe service status"""
    try:
        if not gemma3n_mediapipe_service:
            return {
                "status": "unavailable",
                "message": "Service not initialized",
                "model": "gemma-3n-e4b-mediapipe",
                "approach": "MediaPipe LLM Inference"
            }
        
        model_info = gemma3n_mediapipe_service.get_model_info()
        
        return {
            "status": "available" if gemma3n_mediapipe_service.is_available() else "unavailable",
            "model": "gemma-3n-e4b-mediapipe",
            "approach": "Google AI Edge recommended",
            "info": model_info
        }
        
    except Exception as e:
        logger.error(f"MediaPipe status check failed: {e}")
        return {
            "status": "error",
            "message": str(e),
            "model": "gemma-3n-e4b-mediapipe"
        }

@router.get("/info")
async def get_gemma3n_mediapipe_info():
    """Get detailed Gemma 3N E4B MediaPipe model information"""
    try:
        if not gemma3n_mediapipe_service:
            raise HTTPException(status_code=503, detail="Service not available")
        
        return gemma3n_mediapipe_service.get_model_info()
        
    except Exception as e:
        logger.error(f"MediaPipe info request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test")
async def test_gemma3n_mediapipe():
    """Test Gemma 3N E4B MediaPipe with predefined questions"""
    try:
        if not gemma3n_mediapipe_service or not gemma3n_mediapipe_service.is_available():
            raise HTTPException(status_code=503, detail="Service not available")
        
        test_questions = [
            "Xin chào!",
            "Bạn có phải là Gemini không?",
            "MediaPipe là gì?",
            "Hello, how are you?",
            "Bạn khỏe không?"
        ]
        
        results = []
        for question in test_questions:
            try:
                response = gemma3n_mediapipe_service.generate_response(question)
                results.append({
                    "question": question,
                    "response": response,
                    "status": "success"
                })
            except Exception as e:
                results.append({
                    "question": question,
                    "response": f"Error: {str(e)}",
                    "status": "error"
                })
        
        return {
            "model": "gemma-3n-e4b-mediapipe",
            "approach": "MediaPipe LLM Inference",
            "test_results": results,
            "total_tests": len(test_questions),
            "successful_tests": len([r for r in results if r["status"] == "success"]),
            "documentation": "https://ai.google.dev/edge/mediapipe/solutions/genai/llm_inference"
        }
        
    except Exception as e:
        logger.error(f"MediaPipe test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 