"""
API Router for Gemma 3N E4B Improved Service
Enhanced text generation with better quality responses
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

from app.services.gemma3n_improved_service import Gemma3nImprovedService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize service
try:
    gemma3n_improved_service = Gemma3nImprovedService()
    logger.info("✅ Gemma 3N E4B Improved service initialized")
except Exception as e:
    logger.error(f"❌ Failed to initialize Gemma 3N E4B Improved service: {e}")
    gemma3n_improved_service = None

class ChatRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 30
    temperature: Optional[float] = 0.7
    top_k: Optional[int] = 40
    top_p: Optional[float] = 0.9

class ChatResponse(BaseModel):
    response: str
    model: str
    status: str
    metadata: Optional[Dict[str, Any]] = None

@router.post("/chat", response_model=ChatResponse)
async def chat_with_gemma3n_improved(request: ChatRequest):
    """
    Chat with Gemma 3N E4B Improved model
    Enhanced generation with better Vietnamese support
    """
    try:
        if not gemma3n_improved_service or not gemma3n_improved_service.is_available():
            raise HTTPException(
                status_code=503, 
                detail="Gemma 3N E4B Improved service is not available"
            )
        
        logger.info(f"Processing chat request: '{request.message[:50]}...'")
        
        # Generate response
        response = gemma3n_improved_service.generate_response(
            text=request.message,
            max_tokens=request.max_tokens
        )
        
        # Get model info for metadata
        model_info = gemma3n_improved_service.get_model_info()
        
        return ChatResponse(
            response=response,
            model="gemma-3n-e4b-improved",
            status="success",
            metadata={
                "framework": model_info.get("framework"),
                "version": model_info.get("version"),
                "inputs": model_info.get("inputs"),
                "outputs": model_info.get("outputs"),
                "config": model_info.get("config")
            }
        )
        
    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")

@router.get("/status")
async def get_gemma3n_improved_status():
    """Get Gemma 3N E4B Improved service status"""
    try:
        if not gemma3n_improved_service:
            return {
                "status": "unavailable",
                "message": "Service not initialized",
                "model": "gemma-3n-e4b-improved"
            }
        
        model_info = gemma3n_improved_service.get_model_info()
        
        return {
            "status": "available" if gemma3n_improved_service.is_available() else "unavailable",
            "model": "gemma-3n-e4b-improved",
            "info": model_info
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return {
            "status": "error",
            "message": str(e),
            "model": "gemma-3n-e4b-improved"
        }

@router.get("/info")
async def get_gemma3n_improved_info():
    """Get detailed Gemma 3N E4B Improved model information"""
    try:
        if not gemma3n_improved_service:
            raise HTTPException(status_code=503, detail="Service not available")
        
        return gemma3n_improved_service.get_model_info()
        
    except Exception as e:
        logger.error(f"Info request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test")
async def test_gemma3n_improved():
    """Test Gemma 3N E4B Improved with predefined questions"""
    try:
        if not gemma3n_improved_service or not gemma3n_improved_service.is_available():
            raise HTTPException(status_code=503, detail="Service not available")
        
        test_questions = [
            "Xin chào!",
            "Bạn có phải là Gemini không?",
            "Bạn khỏe không?",
            "Hello, how are you?"
        ]
        
        results = []
        for question in test_questions:
            try:
                response = gemma3n_improved_service.generate_response(question)
                results.append({
                    "question": question,
                    "response": response,
                    "status": "success"
                })
            except Exception as e:
                results.append({
                    "question": question,
                    "response": f"Error: {str(e)}",
                    "status": "error"
                })
        
        return {
            "model": "gemma-3n-e4b-improved",
            "test_results": results,
            "total_tests": len(test_questions),
            "successful_tests": len([r for r in results if r["status"] == "success"])
        }
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 