"""
Gemma 3n E4B Edge-Aligned API Router
Fixed implementation following AI Edge Gallery patterns
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import logging

logger = logging.getLogger(__name__)

try:
    from services.gemma3n_edge_aligned_service import Gemma3nEdgeAlignedService
    edge_aligned_service = Gemma3nEdgeAlignedService()
    EDGE_ALIGNED_SERVICE_AVAILABLE = True
    logger.info("✅ Edge-Aligned Gemma service initialized successfully")
except ImportError as e:
    EDGE_ALIGNED_SERVICE_AVAILABLE = False
    logger.error(f"❌ Could not import Edge-Aligned service: {e}")

router = APIRouter()

class EdgeAlignedRequest(BaseModel):
    text: str
    max_tokens: Optional[int] = 4096  # AI Edge Gallery default
    temperature: Optional[float] = 1.0  # AI Edge Gallery default
    top_k: Optional[int] = 64  # AI Edge Gallery default
    top_p: Optional[float] = 0.95  # AI Edge Gallery default

class EdgeAlignedResponse(BaseModel):
    response: str
    model_info: dict
    parameters_used: dict

@router.post("/test-edge-aligned", response_model=EdgeAlignedResponse)
async def test_edge_aligned_gemma(request: EdgeAlignedRequest):
    """Test Edge-Aligned Gemma 3n E4B with AI Edge Gallery parameters"""
    try:
        if not EDGE_ALIGNED_SERVICE_AVAILABLE:
            raise HTTPException(status_code=503, detail="Edge-Aligned service not available")
        
        logger.info(f"Testing with question: {request.text}")
        
        # Generate response using Edge-Aligned service
        response = edge_aligned_service.generate_response(
            text=request.text,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_k=request.top_k,
            top_p=request.top_p
        )
        
        # Get model info
        model_info = edge_aligned_service.get_model_info()
        
        # Parameters used
        parameters_used = {
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "top_k": request.top_k,
            "top_p": request.top_p,
            "source": "AI Edge Gallery Aligned"
        }
        
        return EdgeAlignedResponse(
            response=response,
            model_info=model_info,
            parameters_used=parameters_used
        )
        
    except Exception as e:
        logger.error(f"Edge-Aligned API error: {e}")
        raise HTTPException(status_code=500, detail=f"Edge-Aligned inference failed: {str(e)}")

@router.get("/edge-aligned-status")
async def get_edge_aligned_status():
    """Get Edge-Aligned service status"""
    try:
        if not EDGE_ALIGNED_SERVICE_AVAILABLE:
            return {
                "available": False,
                "error": "Edge-Aligned service not imported"
            }
        
        model_info = edge_aligned_service.get_model_info()
        
        return {
            "available": edge_aligned_service.is_available(),
            "model_info": model_info,
            "ai_edge_gallery_aligned": True
        }
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        return {
            "available": False,
            "error": str(e)
        }

@router.post("/quick-test")
async def quick_test_vietnamese():
    """Quick test with Vietnamese question about Gemini vs Gemma"""
    try:
        if not EDGE_ALIGNED_SERVICE_AVAILABLE:
            return {"error": "Service not available"}
        
        test_question = "bạn có phải là gemini không?"
        
        logger.info(f"Quick test with: {test_question}")
        
        response = edge_aligned_service.generate_response(
            text=test_question,
            max_tokens=4096,  # AI Edge Gallery default
            temperature=1.0,   # AI Edge Gallery default
            top_k=64,          # AI Edge Gallery default
            top_p=0.95         # AI Edge Gallery default
        )
        
        return {
            "question": test_question,
            "response": response,
            "ai_edge_gallery_parameters": {
                "topK": 64,
                "topP": 0.95,
                "temperature": 1.0,
                "maxTokens": 4096
            },
            "expected_fix": "Should respond about being Gemma, not Gemini, with proper Vietnamese"
        }
        
    except Exception as e:
        logger.error(f"Quick test error: {e}")
        return {"error": str(e)} 