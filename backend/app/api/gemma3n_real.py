"""
Gemma 3n E4B Real Inference API
Sử dụng model thật sự với Google AI Edge LiteRT
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import logging

logger = logging.getLogger(__name__)

try:
    from services.gemma3n_real_inference import gemma3n_service
    REAL_INFERENCE_AVAILABLE = True
    logger.info("✅ Real Gemma 3n E4B inference service imported successfully")
except ImportError as e:
    REAL_INFERENCE_AVAILABLE = False
    logger.error(f"❌ Could not import real inference service: {e}")

router = APIRouter()

class Gemma3nRealRequest(BaseModel):
    text: str
    max_tokens: Optional[int] = 100
    temperature: Optional[float] = 0.7

class Gemma3nRealResponse(BaseModel):
    response: str
    model_info: dict

@router.post("/test-real-gemma3n", response_model=Gemma3nRealResponse)
async def test_real_gemma3n(request: Gemma3nRealRequest):
    """Test real Gemma 3n E4B inference với Google AI Edge LiteRT"""
    
    if not REAL_INFERENCE_AVAILABLE:
        raise HTTPException(status_code=503, detail="Real inference service not available")
    
    try:
        logger.info(f"🧪 Testing real Gemma 3n E4B inference: '{request.text[:50]}...'")
        
        # Generate response using real model
        response = await gemma3n_service.generate_response(
            text=request.text,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # Get model info
        model_info = await gemma3n_service.get_model_info()
        
        return Gemma3nRealResponse(
            response=response,
            model_info=model_info
        )
        
    except Exception as e:
        logger.error(f"❌ Real inference error: {e}")
        raise HTTPException(status_code=500, detail=f"Real inference failed: {str(e)}")

@router.get("/model-info")
async def get_model_info():
    """Get real Gemma 3n E4B model information"""
    
    if not REAL_INFERENCE_AVAILABLE:
        raise HTTPException(status_code=503, detail="Real inference service not available")
    
    try:
        model_info = await gemma3n_service.get_model_info()
        is_available = model_info.get("model_loaded", False)
        
        return {
            "available": is_available,
            "model_info": model_info,
            "service_status": "Real Gemma 3n E4B inference service (Google AI Edge Gallery style)"
        }
        
    except Exception as e:
        logger.error(f"❌ Model info error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get model info: {str(e)}") 