"""
Gemma 3n E4B AI Edge LiteRT API
Sử dụng AI Edge LiteRT đúng cách - theo Google recommendation
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import logging

logger = logging.getLogger(__name__)

try:
    from services.gemma3n_litert_service import gemma3n_litert_service
    LITERT_SERVICE_AVAILABLE = True
    logger.info("✅ AI Edge LiteRT service imported successfully")
except ImportError as e:
    LITERT_SERVICE_AVAILABLE = False
    logger.error(f"❌ Could not import AI Edge LiteRT service: {e}")

router = APIRouter()

class LiteRTRequest(BaseModel):
    text: str
    max_tokens: Optional[int] = 100
    temperature: Optional[float] = 0.7

class LiteRTResponse(BaseModel):
    response: str
    model_info: dict

@router.post("/test-litert-gemma3n", response_model=LiteRTResponse)
async def test_litert_gemma3n(request: LiteRTRequest):
    """Test AI Edge LiteRT Gemma 3n E4B inference"""
    try:
        if not LITERT_SERVICE_AVAILABLE:
            raise HTTPException(status_code=503, detail="AI Edge LiteRT service not available")
        
        # Generate response using AI Edge LiteRT
        response = await gemma3n_litert_service.generate_response(
            text=request.text,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # Get model info
        model_info = await gemma3n_litert_service.get_model_info()
        
        return LiteRTResponse(
            response=response,
            model_info=model_info
        )
        
    except Exception as e:
        logger.error(f"AI Edge LiteRT API error: {e}")
        raise HTTPException(status_code=500, detail=f"AI Edge LiteRT inference failed: {str(e)}")

@router.get("/litert-status")
async def get_litert_status():
    """Get AI Edge LiteRT service status"""
    try:
        if not LITERT_SERVICE_AVAILABLE:
            return {
                "available": False,
                "error": "AI Edge LiteRT service not imported"
            }
        
        model_info = await gemma3n_litert_service.get_model_info()
        
        return {
            "available": gemma3n_litert_service.is_available(),
            "model_info": model_info
        }
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        return {
            "available": False,
            "error": str(e)
        }

@router.get("/debug-model-structure")
async def debug_model_structure():
    """Debug model input/output structure"""
    try:
        if not LITERT_SERVICE_AVAILABLE:
            return {"error": "Service not available"}
        
        # Force initialization if needed
        if not gemma3n_litert_service.model_loaded:
            await gemma3n_litert_service._initialize()
        
        if not gemma3n_litert_service.model_loaded:
            return {"error": "Model not loaded"}
        
        # Get model details
        interpreter = gemma3n_litert_service.interpreter
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        return {
            "model_loaded": True,
            "num_inputs": len(input_details),
            "num_outputs": len(output_details),
            "input_details": [
                {
                    "index": detail["index"],
                    "name": detail["name"],
                    "shape": detail["shape"].tolist(),
                    "dtype": str(detail["dtype"])
                }
                for detail in input_details[:10]  # First 10 inputs
            ],
            "output_details": [
                {
                    "index": detail["index"], 
                    "name": detail["name"],
                    "shape": detail["shape"].tolist(),
                    "dtype": str(detail["dtype"])
                }
                for detail in output_details[:10]  # First 10 outputs
            ]
        }
    except Exception as e:
        logger.error(f"Error debugging model structure: {e}")
        return {"error": str(e)} 