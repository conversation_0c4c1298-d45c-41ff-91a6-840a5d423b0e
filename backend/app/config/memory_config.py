from typing import Dict, Optional, List
from pydantic import BaseModel
from datetime import datetime

class EmbeddingProviderConfig(BaseModel):
    provider: str
    api_key: Optional[str] = None
    model: Optional[str] = None

class MemoryMetadata(BaseModel):
    """Enhanced metadata for memory entries"""
    timestamp: datetime
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None
    source: str = "user"  # "user", "assistant", "system", "summary"
    tags: List[str] = []
    keywords: List[str] = []
    user_feedback: Optional[str] = None  # "positive", "negative", "neutral"
    confidence_score: Optional[float] = None
    context_type: Optional[str] = None  # "question", "answer", "instruction", "casual"
    language: Optional[str] = "vi"  # "vi", "en", "mixed"
    topic: Optional[str] = None
    importance: Optional[int] = 1  # 1-5 scale

class Mem0Config(BaseModel):
    enabled: bool = True
    max_memories: int = 1000
    memories_per_query: int = 3
    memory_ttl: int = 86400  # 24 hours
    min_relevance_score: float = 0.7
    embedding_provider: Optional[EmbeddingProviderConfig] = None
    
    # Enhanced metadata settings
    enable_metadata: bool = True
    auto_tag_extraction: bool = True
    auto_keyword_extraction: bool = True
    store_conversation_context: bool = True
    track_user_feedback: bool = True

# Default configuration
DEFAULT_CONFIG = {
    "enabled": True,
    "max_memories": 1000,
    "memories_per_query": 3,
    "memory_ttl": 86400,
    "min_relevance_score": 0.7,
    "embedding_provider": {
        "provider": "openai",
        "model": "text-embedding-3-small"
    },
    "enable_metadata": True,
    "auto_tag_extraction": True,
    "auto_keyword_extraction": True,
    "store_conversation_context": True,
    "track_user_feedback": True
}