"""
Enhanced Table Processor for Vietnamese RAG System
Handles complex table structures, preserves relationships, and improves RAG accuracy
"""

import re
import json
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging

# Document processing libraries
try:
    import fitz  # PyMuPDF
    import pdfplumber
    import camelot
    import tabula
    ADVANCED_PDF_AVAILABLE = True
except ImportError:
    ADVANCED_PDF_AVAILABLE = False

try:
    import docx
    from docx.table import Table as DocxTable
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TableChunk:
    content: str
    table_structure: Dict[str, Any]
    metadata: Dict[str, Any]
    chunk_id: str
    confidence_score: float
    source_location: str
    table_type: str  # 'data_table', 'form_table', 'layout_table'

@dataclass
class ProcessedTable:
    raw_content: str
    structured_content: str  # Formatted for RAG
    summary: str  # Natural language summary
    headers: List[str]
    rows: List[List[str]]
    table_type: str
    confidence: float
    metadata: Dict[str, Any]

class EnhancedTableProcessor:
    def __init__(self):
        self.min_confidence = 0.6
        self.max_cells_per_chunk = 50
        self.vietnamese_patterns = {
            'currency': r'(?:\d{1,3}(?:\.\d{3})*(?:,\d{2})?)\s*(?:VNĐ|VND|đồng)',
            'percentage': r'\d+(?:,\d+)?%',
            'date': r'\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}',
            'phone': r'(?:\+84|84|0)(?:3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}',
            'code': r'[A-Z]{2,5}[\-\_]?\d{2,6}',
        }
        
        self.table_indicators = [
            'STT', 'Số TT', 'TT', '#',  # Vietnamese ordinal indicators
            'Tên', 'Họ tên', 'Name',
            'Số lượng', 'SL', 'Quantity',
            'Đơn giá', 'Giá', 'Price', 'Unit Price',
            'Thành tiền', 'Total', 'Amount',
            'Ngày', 'Date', 'Thời gian',
            'Mã', 'Code', 'ID',
            'Ghi chú', 'Note', 'Remarks'
        ]

    def process_document_tables(self, file_path: str) -> List[TableChunk]:
        """Main entry point for processing tables in documents"""
        file_ext = Path(file_path).suffix.lower()
        
        try:
            if file_ext == '.pdf':
                return self._process_pdf_tables(file_path)
            elif file_ext in ['.docx', '.doc']:
                return self._process_docx_tables(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                return self._process_excel_tables(file_path)
            else:
                logger.warning(f"Unsupported file format for table extraction: {file_ext}")
                return []
        
        except Exception as e:
            logger.error(f"Error processing tables in {file_path}: {e}")
            return []

    def _process_pdf_tables(self, file_path: str) -> List[TableChunk]:
        """Process tables from PDF using multiple methods"""
        table_chunks = []
        
        # Method 1: pdfplumber (best for structured PDFs)
        if ADVANCED_PDF_AVAILABLE:
            try:
                tables_pdfplumber = self._extract_tables_pdfplumber(file_path)
                table_chunks.extend(tables_pdfplumber)
            except Exception as e:
                logger.warning(f"pdfplumber failed: {e}")
        
        # Method 2: camelot (good for bordered tables)
        if ADVANCED_PDF_AVAILABLE:
            try:
                tables_camelot = self._extract_tables_camelot(file_path)
                table_chunks.extend(tables_camelot)
            except Exception as e:
                logger.warning(f"camelot failed: {e}")
        
        # Method 3: PyMuPDF fallback
        try:
            tables_pymupdf = self._extract_tables_pymupdf(file_path)
            table_chunks.extend(tables_pymupdf)
        except Exception as e:
            logger.warning(f"PyMuPDF table extraction failed: {e}")
        
        # Deduplicate and rank tables
        return self._deduplicate_tables(table_chunks)

    def _extract_tables_pdfplumber(self, file_path: str) -> List[TableChunk]:
        """Extract tables using pdfplumber"""
        import pdfplumber
        
        table_chunks = []
        
        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                tables = page.extract_tables()
                
                for table_idx, table in enumerate(tables):
                    if table and len(table) > 1:  # At least header + 1 row
                        processed_table = self._process_raw_table(
                            table, f"page_{page_num + 1}_table_{table_idx + 1}"
                        )
                        
                        if processed_table.confidence >= self.min_confidence:
                            chunk = self._create_table_chunk(
                                processed_table, file_path, page_num + 1
                            )
                            table_chunks.append(chunk)
        
        return table_chunks

    def _extract_tables_camelot(self, file_path: str) -> List[TableChunk]:
        """Extract tables using camelot"""
        import camelot
        
        table_chunks = []
        
        try:
            # Extract tables with lattice method (for bordered tables)
            tables = camelot.read_pdf(file_path, flavor='lattice', pages='all')
            
            for table_idx, table in enumerate(tables):
                if table.accuracy > 0.6:  # Camelot confidence score
                    # Convert to list format
                    raw_table = table.df.values.tolist()
                    headers = table.df.columns.tolist()
                    raw_table.insert(0, headers)
                    
                    processed_table = self._process_raw_table(
                        raw_table, f"camelot_table_{table_idx + 1}"
                    )
                    
                    if processed_table.confidence >= self.min_confidence:
                        chunk = self._create_table_chunk(
                            processed_table, file_path, table.page
                        )
                        table_chunks.append(chunk)
        
        except Exception as e:
            logger.debug(f"Camelot lattice method failed, trying stream: {e}")
            
            # Fallback to stream method (for tables without borders)
            try:
                tables = camelot.read_pdf(file_path, flavor='stream', pages='all')
                
                for table_idx, table in enumerate(tables):
                    if table.accuracy > 0.5:
                        raw_table = table.df.values.tolist()
                        headers = table.df.columns.tolist()
                        raw_table.insert(0, headers)
                        
                        processed_table = self._process_raw_table(
                            raw_table, f"camelot_stream_table_{table_idx + 1}"
                        )
                        
                        if processed_table.confidence >= self.min_confidence:
                            chunk = self._create_table_chunk(
                                processed_table, file_path, table.page
                            )
                            table_chunks.append(chunk)
            
            except Exception as e2:
                logger.warning(f"Camelot stream method also failed: {e2}")
        
        return table_chunks

    def _extract_tables_pymupdf(self, file_path: str) -> List[TableChunk]:
        """Fallback table extraction using PyMuPDF"""
        import fitz
        
        table_chunks = []
        
        doc = fitz.open(file_path)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find tables using text analysis
            tables = self._detect_table_structures(page)
            
            for table_idx, table_data in enumerate(tables):
                processed_table = self._process_raw_table(
                    table_data, f"pymupdf_page_{page_num + 1}_table_{table_idx + 1}"
                )
                
                if processed_table.confidence >= self.min_confidence:
                    chunk = self._create_table_chunk(
                        processed_table, file_path, page_num + 1
                    )
                    table_chunks.append(chunk)
        
        doc.close()
        return table_chunks

    def _detect_table_structures(self, page) -> List[List[List[str]]]:
        """Detect table-like structures in PDF page"""
        # Get text with position information
        text_dict = page.get_text("dict")
        
        # Group text by lines and detect table patterns
        lines = []
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span.get("text", "") + " "
                    
                    if line_text.strip():
                        lines.append(line_text.strip())
        
        # Analyze lines for table patterns
        tables = []
        current_table = []
        
        for line in lines:
            if self._is_table_row(line):
                cells = self._split_table_row(line)
                current_table.append(cells)
            else:
                if len(current_table) >= 2:  # At least header + 1 row
                    tables.append(current_table)
                current_table = []
        
        # Don't forget the last table
        if len(current_table) >= 2:
            tables.append(current_table)
        
        return tables

    def _is_table_row(self, line: str) -> bool:
        """Determine if a line represents a table row"""
        # Check for common table indicators
        has_separators = any(sep in line for sep in ['\t', '|', '  ', '...'])
        has_table_indicators = any(indicator in line for indicator in self.table_indicators)
        has_structured_data = bool(re.search(r'\d+\s+[^\d\s]+\s+\d+', line))  # number + text + number pattern
        
        return has_separators and (has_table_indicators or has_structured_data)

    def _split_table_row(self, line: str) -> List[str]:
        """Split a table row into cells"""
        # Try different splitting methods
        if '\t' in line:
            cells = line.split('\t')
        elif '|' in line:
            cells = line.split('|')
        else:
            # Split on multiple spaces
            cells = re.split(r'\s{2,}', line)
        
        # Clean up cells
        return [cell.strip() for cell in cells if cell.strip()]

    def _process_docx_tables(self, file_path: str) -> List[TableChunk]:
        """Process tables from DOCX files"""
        if not DOCX_AVAILABLE:
            logger.warning("python-docx not available")
            return []
        
        table_chunks = []
        
        try:
            doc = docx.Document(file_path)
            
            for table_idx, table in enumerate(doc.tables):
                raw_table = []
                
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        row_data.append(cell_text)
                    raw_table.append(row_data)
                
                if len(raw_table) >= 2:  # At least header + 1 row
                    processed_table = self._process_raw_table(
                        raw_table, f"docx_table_{table_idx + 1}"
                    )
                    
                    if processed_table.confidence >= self.min_confidence:
                        chunk = self._create_table_chunk(
                            processed_table, file_path, None
                        )
                        table_chunks.append(chunk)
        
        except Exception as e:
            logger.error(f"Error processing DOCX tables: {e}")
        
        return table_chunks

    def _process_excel_tables(self, file_path: str) -> List[TableChunk]:
        """Process tables from Excel files"""
        if not EXCEL_AVAILABLE:
            logger.warning("openpyxl not available")
            return []
        
        table_chunks = []
        
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                
                # Extract all data from sheet
                raw_table = []
                for row in sheet.iter_rows(values_only=True):
                    if any(cell for cell in row):  # Skip empty rows
                        row_data = [str(cell) if cell is not None else "" for cell in row]
                        raw_table.append(row_data)
                
                if len(raw_table) >= 2:
                    processed_table = self._process_raw_table(
                        raw_table, f"excel_sheet_{sheet_name}"
                    )
                    
                    if processed_table.confidence >= self.min_confidence:
                        chunk = self._create_table_chunk(
                            processed_table, file_path, None, sheet_name
                        )
                        table_chunks.append(chunk)
        
        except Exception as e:
            logger.error(f"Error processing Excel tables: {e}")
        
        return table_chunks

    def _process_raw_table(self, raw_table: List[List[str]], table_id: str) -> ProcessedTable:
        """Process raw table data into structured format"""
        if not raw_table or len(raw_table) < 2:
            return ProcessedTable("", "", "", [], [], "unknown", 0.0, {})
        
        # Detect headers
        headers = raw_table[0]
        rows = raw_table[1:]
        
        # Clean headers and rows
        headers = [self._clean_cell_text(header) for header in headers]
        cleaned_rows = []
        
        for row in rows:
            cleaned_row = [self._clean_cell_text(cell) for cell in row]
            if any(cell for cell in cleaned_row):  # Skip empty rows
                cleaned_rows.append(cleaned_row)
        
        # Determine table type
        table_type = self._classify_table_type(headers, cleaned_rows)
        
        # Calculate confidence
        confidence = self._calculate_table_confidence(headers, cleaned_rows)
        
        # Generate content formats
        raw_content = self._generate_raw_table_text(headers, cleaned_rows)
        structured_content = self._generate_structured_table_text(headers, cleaned_rows, table_type)
        summary = self._generate_table_summary(headers, cleaned_rows, table_type)
        
        # Metadata
        metadata = {
            "table_id": table_id,
            "row_count": len(cleaned_rows),
            "column_count": len(headers),
            "has_vietnamese": self._has_vietnamese_content(raw_content),
            "data_types": self._detect_column_types(cleaned_rows),
            "confidence_factors": self._get_confidence_factors(headers, cleaned_rows)
        }
        
        return ProcessedTable(
            raw_content=raw_content,
            structured_content=structured_content,
            summary=summary,
            headers=headers,
            rows=cleaned_rows,
            table_type=table_type,
            confidence=confidence,
            metadata=metadata
        )

    def _clean_cell_text(self, text: str) -> str:
        """Clean and normalize cell text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', str(text)).strip()
        
        # Fix common OCR issues with Vietnamese
        vietnamese_fixes = {
            'ạ': 'a', 'ả': 'a', 'ã': 'a', 'á': 'a', 'à': 'a',
            'ậ': 'ậ', 'ẩ': 'ẩ', 'ẫ': 'ẫ', 'ấ': 'ấ', 'ầ': 'ầ',
            # Add more as needed
        }
        
        return text

    def _classify_table_type(self, headers: List[str], rows: List[List[str]]) -> str:
        """Classify the type of table"""
        header_text = " ".join(headers).lower()
        
        # Data tables (structured information)
        if any(indicator in header_text for indicator in ['stt', 'số tt', 'tên', 'số lượng', 'đơn giá']):
            return 'data_table'
        
        # Form tables (key-value pairs)
        if len(headers) == 2 and any(indicator in header_text for indicator in ['nội dung', 'thông tin', 'chi tiết']):
            return 'form_table'
        
        # Financial tables
        if any(indicator in header_text for indicator in ['thành tiền', 'tổng cộng', 'thuế', 'vat']):
            return 'financial_table'
        
        # Process tables (workflow/procedure)
        if any(indicator in header_text for indicator in ['bước', 'thủ tục', 'quy trình', 'trách nhiệm']):
            return 'process_table'
        
        return 'general_table'

    def _calculate_table_confidence(self, headers: List[str], rows: List[List[str]]) -> float:
        """Calculate confidence score for table extraction"""
        confidence_factors = []
        
        # Header quality
        header_score = 0.0
        if headers:
            non_empty_headers = sum(1 for h in headers if h.strip())
            header_score = non_empty_headers / len(headers)
            
            # Bonus for Vietnamese table indicators
            vietnamese_indicators = sum(1 for h in headers if any(
                indicator in h.lower() for indicator in self.table_indicators
            ))
            if vietnamese_indicators > 0:
                header_score += 0.2
        
        confidence_factors.append(header_score)
        
        # Data consistency
        if rows:
            col_count = len(headers) if headers else 0
            consistent_rows = sum(1 for row in rows if len(row) == col_count)
            consistency_score = consistent_rows / len(rows) if rows else 0
            confidence_factors.append(consistency_score)
        
        # Data quality (non-empty cells)
        total_cells = len(headers) * len(rows) if headers and rows else 0
        if total_cells > 0:
            non_empty_cells = sum(1 for row in rows for cell in row if cell.strip())
            data_quality_score = non_empty_cells / total_cells
            confidence_factors.append(data_quality_score)
        
        # Structured data patterns
        pattern_score = 0.0
        all_text = " ".join([" ".join(headers)] + [" ".join(row) for row in rows])
        
        pattern_matches = 0
        for pattern in self.vietnamese_patterns.values():
            if re.search(pattern, all_text):
                pattern_matches += 1
        
        if pattern_matches > 0:
            pattern_score = min(pattern_matches / 3, 1.0)  # Cap at 1.0
        
        confidence_factors.append(pattern_score)
        
        # Calculate weighted average
        if confidence_factors:
            return sum(confidence_factors) / len(confidence_factors)
        
        return 0.0

    def _generate_raw_table_text(self, headers: List[str], rows: List[List[str]]) -> str:
        """Generate raw table text for basic processing"""
        lines = []
        
        if headers:
            lines.append(" | ".join(headers))
            lines.append("-" * 50)  # Separator
        
        for row in rows:
            lines.append(" | ".join(row))
        
        return "\n".join(lines)

    def _generate_structured_table_text(self, headers: List[str], rows: List[List[str]], table_type: str) -> str:
        """Generate structured table text optimized for RAG"""
        if table_type == 'form_table' and len(headers) == 2:
            # Key-value format for forms
            lines = [f"BẢNG THÔNG TIN ({table_type.upper()}):\n"]
            for row in rows:
                if len(row) >= 2 and row[0] and row[1]:
                    lines.append(f"• {row[0]}: {row[1]}")
            return "\n".join(lines)
        
        elif table_type == 'data_table':
            # Structured data format
            lines = [f"BẢNG DỮ LIỆU ({len(rows)} dòng, {len(headers)} cột):\n"]
            
            # Add headers
            if headers:
                lines.append("CÁC CỘT: " + ", ".join(headers))
                lines.append("")
            
            # Add data rows with context
            for i, row in enumerate(rows, 1):
                if any(cell for cell in row):
                    row_data = []
                    for j, cell in enumerate(row):
                        if cell and j < len(headers):
                            row_data.append(f"{headers[j]}: {cell}")
                        elif cell:
                            row_data.append(cell)
                    
                    if row_data:
                        lines.append(f"Dòng {i}: {' | '.join(row_data)}")
            
            return "\n".join(lines)
        
        else:
            # General format
            lines = [f"BẢNG ({table_type.upper()}):\n"]
            
            # Markdown-style table
            if headers:
                lines.append("| " + " | ".join(headers) + " |")
                lines.append("|" + "|".join([" --- " for _ in headers]) + "|")
            
            for row in rows:
                if any(cell for cell in row):
                    # Pad row to match header length
                    padded_row = row + [""] * (len(headers) - len(row))
                    lines.append("| " + " | ".join(padded_row[:len(headers)]) + " |")
            
            return "\n".join(lines)

    def _generate_table_summary(self, headers: List[str], rows: List[List[str]], table_type: str) -> str:
        """Generate natural language summary of table"""
        summary_parts = []
        
        # Basic info
        summary_parts.append(f"Bảng dữ liệu loại {table_type} có {len(rows)} dòng và {len(headers)} cột.")
        
        # Headers summary
        if headers:
            summary_parts.append(f"Các cột bao gồm: {', '.join(headers)}.")
        
        # Content insights
        if table_type == 'financial_table':
            # Look for totals, amounts
            for row in rows:
                for cell in row:
                    if any(keyword in cell.lower() for keyword in ['tổng', 'total', 'cộng']):
                        summary_parts.append(f"Bảng chứa thông tin tài chính với {cell}.")
                        break
        
        elif table_type == 'process_table':
            summary_parts.append(f"Bảng mô tả quy trình gồm {len(rows)} bước.")
        
        # Data characteristics
        non_empty_rows = sum(1 for row in rows if any(cell.strip() for cell in row))
        if non_empty_rows != len(rows):
            summary_parts.append(f"Có {non_empty_rows} dòng chứa dữ liệu hữu ích.")
        
        return " ".join(summary_parts)

    def _has_vietnamese_content(self, text: str) -> bool:
        """Check if text contains Vietnamese characters"""
        vietnamese_chars = re.search(r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', text.lower())
        return vietnamese_chars is not None

    def _detect_column_types(self, rows: List[List[str]]) -> List[str]:
        """Detect data types for each column"""
        if not rows:
            return []
        
        max_cols = max(len(row) for row in rows) if rows else 0
        column_types = []
        
        for col_idx in range(max_cols):
            col_values = [row[col_idx] if col_idx < len(row) else "" for row in rows]
            col_type = self._analyze_column_type(col_values)
            column_types.append(col_type)
        
        return column_types

    def _analyze_column_type(self, values: List[str]) -> str:
        """Analyze column type based on values"""
        non_empty_values = [v for v in values if v.strip()]
        
        if not non_empty_values:
            return "empty"
        
        # Check patterns
        numeric_count = sum(1 for v in non_empty_values if re.match(r'^\d+(?:[,\.]\d+)*$', v.replace(' ', '')))
        date_count = sum(1 for v in non_empty_values if re.search(self.vietnamese_patterns['date'], v))
        currency_count = sum(1 for v in non_empty_values if re.search(self.vietnamese_patterns['currency'], v))
        
        total = len(non_empty_values)
        
        if currency_count / total > 0.5:
            return "currency"
        elif date_count / total > 0.5:
            return "date"
        elif numeric_count / total > 0.7:
            return "numeric"
        else:
            return "text"

    def _get_confidence_factors(self, headers: List[str], rows: List[List[str]]) -> Dict[str, float]:
        """Get detailed confidence factors"""
        return {
            "header_quality": len([h for h in headers if h.strip()]) / len(headers) if headers else 0,
            "data_consistency": len([r for r in rows if len(r) == len(headers)]) / len(rows) if rows else 0,
            "vietnamese_indicators": sum(1 for h in headers if any(ind in h.lower() for ind in self.table_indicators)),
            "structured_patterns": len([v for row in rows for v in row if re.search(r'\d+', v)]) / (len(rows) * len(headers)) if rows and headers else 0
        }

    def _create_table_chunk(self, processed_table: ProcessedTable, file_path: str, 
                           page_number: Optional[int] = None, sheet_name: Optional[str] = None) -> TableChunk:
        """Create a TableChunk from processed table"""
        file_name = Path(file_path).name
        
        # Create comprehensive content for RAG
        chunk_content_parts = [
            f"=== BẢNG DỮ LIỆU TỪ {file_name} ===",
            ""
        ]
        
        if page_number:
            chunk_content_parts.append(f"Vị trí: Trang {page_number}")
        
        if sheet_name:
            chunk_content_parts.append(f"Sheet: {sheet_name}")
        
        chunk_content_parts.extend([
            "",
            "TÓm tắt:",
            processed_table.summary,
            "",
            "DỮ LIỆU CHI TIẾT:",
            processed_table.structured_content,
            "",
            "DỮ LIỆU THÔ:",
            processed_table.raw_content
        ])
        
        chunk_content = "\n".join(chunk_content_parts)
        
        # Metadata
        metadata = {
            "source_file": file_name,
            "content_type": "table",
            "table_type": processed_table.table_type,
            "page_number": page_number,
            "sheet_name": sheet_name,
            "row_count": len(processed_table.rows),
            "column_count": len(processed_table.headers),
            "confidence_score": processed_table.confidence,
            "headers": processed_table.headers,
            "has_vietnamese": processed_table.metadata.get("has_vietnamese", False),
            "data_types": processed_table.metadata.get("data_types", []),
            "extraction_method": "enhanced_table_processor"
        }
        
        # Table structure for advanced queries
        table_structure = {
            "headers": processed_table.headers,
            "row_count": len(processed_table.rows),
            "column_count": len(processed_table.headers),
            "sample_rows": processed_table.rows[:3],  # First 3 rows as sample
            "column_types": processed_table.metadata.get("data_types", [])
        }
        
        chunk_id = f"{file_name}_table_{processed_table.metadata['table_id']}"
        
        return TableChunk(
            content=chunk_content,
            table_structure=table_structure,
            metadata=metadata,
            chunk_id=chunk_id,
            confidence_score=processed_table.confidence,
            source_location=f"{file_name}:{page_number or sheet_name or 'unknown'}",
            table_type=processed_table.table_type
        )

    def _deduplicate_tables(self, table_chunks: List[TableChunk]) -> List[TableChunk]:
        """Remove duplicate tables and keep the best version"""
        if not table_chunks:
            return []
        
        # Group similar tables
        groups = []
        
        for chunk in table_chunks:
            added_to_group = False
            
            for group in groups:
                # Compare with first item in group
                if self._are_similar_tables(chunk, group[0]):
                    group.append(chunk)
                    added_to_group = True
                    break
            
            if not added_to_group:
                groups.append([chunk])
        
        # Select best from each group
        best_chunks = []
        for group in groups:
            # Sort by confidence score and select best
            group.sort(key=lambda x: x.confidence_score, reverse=True)
            best_chunks.append(group[0])
        
        return best_chunks

    def _are_similar_tables(self, chunk1: TableChunk, chunk2: TableChunk) -> bool:
        """Check if two table chunks are similar (likely duplicates)"""
        # Compare headers
        headers1 = chunk1.table_structure.get("headers", [])
        headers2 = chunk2.table_structure.get("headers", [])
        
        if len(headers1) != len(headers2):
            return False
        
        # Check header similarity
        header_matches = sum(1 for h1, h2 in zip(headers1, headers2) if h1.lower() == h2.lower())
        header_similarity = header_matches / len(headers1) if headers1 else 0
        
        # Compare dimensions
        rows1 = chunk1.table_structure.get("row_count", 0)
        rows2 = chunk2.table_structure.get("row_count", 0)
        
        size_similarity = 1 - abs(rows1 - rows2) / max(rows1, rows2, 1)
        
        # Tables are similar if headers match well and sizes are close
        return header_similarity > 0.8 and size_similarity > 0.8

# Usage example
def process_document_with_enhanced_tables(file_path: str) -> List[TableChunk]:
    """Process a document and extract enhanced table chunks"""
    processor = EnhancedTableProcessor()
    return processor.process_document_tables(file_path)

if __name__ == "__main__":
    # Test with a sample file
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        chunks = process_document_with_enhanced_tables(file_path)
        
        print(f"Extracted {len(chunks)} table chunks:")
        for chunk in chunks:
            print(f"\n--- {chunk.chunk_id} ---")
            print(f"Type: {chunk.table_type}")
            print(f"Confidence: {chunk.confidence_score:.2f}")
            print(f"Content preview: {chunk.content[:200]}...")
    else:
        print("Usage: python enhanced_table_processor.py <file_path>")