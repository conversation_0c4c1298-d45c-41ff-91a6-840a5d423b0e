FROM python:3.11-slim

WORKDIR /app

# Install system dependencies for tesseract and compilation
RUN apt-get update && apt-get install -y \
    build-essential \
    g++ \
    gcc \
    pkg-config \
    tesseract-ocr \
    tesseract-ocr-vie \
    tesseract-ocr-eng \
    libleptonica-dev \
    libtesseract-dev \
    libpng-dev \
    libjpeg-dev \
    libtiff-dev \
    zlib1g-dev \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables for tesseract
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata/
ENV LC_ALL=C.UTF-8
ENV LANG=C.UTF-8

# Copy requirements and install Python dependencies
COPY requirements.txt .

# Install Python packages with verbose output
RUN pip install --no-cache-dir --verbose -r requirements.txt

# Verify tesserocr installation
RUN python -c "import tesserocr; print('tesserocr installed successfully')" || echo "tesserocr failed"

# Copy the server script
COPY docling_server.py .

# Expose port
EXPOSE 5001

# Run the server
CMD ["python", "docling_server.py"]