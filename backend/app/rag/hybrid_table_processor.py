"""
Hybrid Table Processor for Vietnamese RAG System
Combines LLMSherpa, <PERSON>ling, and Enhanced for maximum table extraction accuracy
"""

import os
import json
import logging
import time
import requests
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# Import existing processors
from enhanced_table_processor import TableChunk, ProcessedTable, EnhancedTableProcessor

try:
    from text_table_processor import TextTableProcessor, TextTableChunk
    TEXT_AVAILABLE = True
except ImportError:
    TEXT_AVAILABLE = False
    print("⚠️  Text Table Processor not available")

try:
    from llmsherpa_table_processor import LLMSherpaTableProcessor
    LLMSHERPA_AVAILABLE = True
except ImportError:
    LLMSHERPA_AVAILABLE = False
    print("⚠️  LLMSherpa not available")

try:
    from docling_table_processor import DoclingTableProcessor
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False
    print("⚠️  Docling not available")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class HybridExtractionResult:
    """Result from hybrid table extraction"""
    file_path: str
    llmsherpa_chunks: List[TableChunk]
    docling_chunks: List[TableChunk]
    enhanced_chunks: List[TableChunk]
    text_chunks: List[TableChunk]
    final_chunks: List[TableChunk]
    processing_stats: Dict[str, Any]
    recommended_method: str

class HybridTableProcessor:
    """Hybrid table processor combining multiple extraction methods"""
    
    def __init__(self, 
                 llmsherpa_url: str = "http://localhost:5010/api/parseDocument?renderFormat=all",
                 docling_url: str = "http://localhost:5001"):
        
        self.processors = {}
        self.available_methods = []
        self.server_status = {}
        
        # Initialize Enhanced processor (always available)
        self.processors['enhanced'] = EnhancedTableProcessor()
        self.available_methods.append('enhanced')
        self.server_status['enhanced'] = True
        
        # Initialize Text processor (always available for .txt files)
        if TEXT_AVAILABLE:
            self.processors['text'] = TextTableProcessor()
            self.available_methods.append('text')
            self.server_status['text'] = True
            logger.info("✅ Text processor initialized")
        else:
            self.server_status['text'] = False
        
        # Initialize LLMSherpa if available
        if LLMSHERPA_AVAILABLE:
            try:
                self.processors['llmsherpa'] = LLMSherpaTableProcessor(llmsherpa_url)
                self.available_methods.append('llmsherpa')
                self.server_status['llmsherpa'] = self._check_server_availability(llmsherpa_url)
                logger.info(f"✅ LLMSherpa processor initialized (Server: {'✅' if self.server_status['llmsherpa'] else '❌'})")
            except Exception as e:
                logger.warning(f"⚠️  Failed to initialize LLMSherpa: {e}")
                self.server_status['llmsherpa'] = False
        else:
            self.server_status['llmsherpa'] = False
        
        # Initialize Docling if available
        if DOCLING_AVAILABLE:
            try:
                self.processors['docling'] = DoclingTableProcessor(docling_url)
                self.available_methods.append('docling')
                self.server_status['docling'] = self._check_docling_server(docling_url)
                logger.info(f"✅ Docling processor initialized (Server: {'✅' if self.server_status['docling'] else '❌'})")
            except Exception as e:
                logger.warning(f"⚠️  Failed to initialize Docling: {e}")
                self.server_status['docling'] = False
        else:
            self.server_status['docling'] = False
        
        logger.info(f"🔧 Hybrid processor initialized with methods: {self.available_methods}")
        logger.info(f"📡 Server status: {self.server_status}")
    
    def _check_server_availability(self, url: str) -> bool:
        """Check if LLMSherpa server is available"""
        try:
            # Extract base URL for health check
            if '/api/' in url:
                base_url = url.split('/api/')[0]
            else:
                base_url = url
            response = requests.get(f"{base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            try:
                # Try direct URL check
                response = requests.get(url, timeout=5)
                return response.status_code in [200, 400, 422]  # 400/422 might be normal for API endpoints
            except:
                return False
    
    def _check_docling_server(self, url: str) -> bool:
        """Check if Docling server is available"""
        try:
            response = requests.get(f"{url}/health", timeout=5)
            return response.status_code == 200
        except:
            try:
                response = requests.get(f"{url}/", timeout=5)
                return response.status_code in [200, 404]
            except:
                return False
    
    def process_document_hybrid(self, file_path: str, 
                              force_method: Optional[str] = None) -> HybridExtractionResult:
        """Process document using hybrid approach with all available methods"""
        
        logger.info(f"🔍 Processing document: {Path(file_path).name}")
        
        # Initialize result containers
        all_chunks = {
            'llmsherpa': [],
            'docling': [],
            'enhanced': [],
            'text': []
        }
        
        processing_stats = {
            'file_path': file_path,
            'methods_attempted': [],
            'methods_successful': [],
            'processing_times': {},
            'chunk_counts': {},
            'confidence_scores': {},
            'server_status': self.server_status.copy()
        }
        
        # Determine which methods to use
        methods_to_use = []
        if force_method:
            if force_method in self.available_methods:
                methods_to_use = [force_method]
            else:
                logger.warning(f"⚠️  Forced method '{force_method}' not available, using all available methods")
                methods_to_use = self.available_methods
        else:
            methods_to_use = self.available_methods
        
        # Process with each available method
        for method in methods_to_use:
            processing_stats['methods_attempted'].append(method)
            
            try:
                start_time = time.time()
                chunks = self._process_with_method(method, file_path)
                processing_time = time.time() - start_time
                
                processing_stats['processing_times'][method] = processing_time
                processing_stats['chunk_counts'][method] = len(chunks)
                
                if chunks:
                    all_chunks[method] = chunks
                    processing_stats['methods_successful'].append(method)
                    
                    # Calculate average confidence
                    confidences = [chunk.confidence_score for chunk in chunks if hasattr(chunk, 'confidence_score')]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
                    processing_stats['confidence_scores'][method] = avg_confidence
                    
                    logger.info(f"✅ {method.upper()} extracted {len(chunks)} chunks in {processing_time:.2f}s (avg confidence: {avg_confidence:.2f})")
                else:
                    processing_stats['confidence_scores'][method] = 0.0
                    logger.info(f"📄 {method.upper()} found no tables in {processing_time:.2f}s")
                    
            except Exception as e:
                logger.warning(f"⚠️  {method.upper()} processing failed: {e}")
                processing_stats['processing_times'][method] = 0
                processing_stats['chunk_counts'][method] = 0
                processing_stats['confidence_scores'][method] = 0.0
        
        # Combine and deduplicate chunks
        final_chunks = self._combine_and_deduplicate_chunks(all_chunks)
        
        # Determine recommended method
        recommended_method = self._determine_best_method(processing_stats)
        
        # Create result
        result = HybridExtractionResult(
            file_path=file_path,
            llmsherpa_chunks=all_chunks['llmsherpa'],
            docling_chunks=all_chunks['docling'],
            enhanced_chunks=all_chunks['enhanced'],
            text_chunks=all_chunks['text'],
            final_chunks=final_chunks,
            processing_stats=processing_stats,
            recommended_method=recommended_method
        )
        
        logger.info(f"🎯 Hybrid processing complete: {len(final_chunks)} final chunks")
        logger.info(f"💡 Recommended method for similar documents: {recommended_method}")
        
        return result
    
    def _process_with_method(self, method: str, file_path: str) -> List[TableChunk]:
        """Process document with specific method"""
        
        if method not in self.processors:
            logger.warning(f"⚠️  Processor for method '{method}' not available")
            return []
        
        processor = self.processors[method]
        
        try:
            if method == 'llmsherpa':
                if not self.server_status['llmsherpa']:
                    logger.warning("⚠️  LLMSherpa server not available, skipping")
                    return []
                return processor.process_pdf_with_llmsherpa(file_path)
            
            elif method == 'docling':
                if not self.server_status['docling']:
                    logger.warning("⚠️  Docling server not available, skipping")
                    return []
                return processor.process_pdf_with_docling(file_path)
            
            elif method == 'enhanced':
                if file_path.lower().endswith('.pdf'):
                    return processor._process_pdf_tables(file_path)
                else:
                    return processor.process_document_tables(file_path)
            
            elif method == 'text':
                # Convert TextTableChunk to TableChunk
                text_chunks = processor.process_text_file(file_path)
                converted_chunks = []
                for text_chunk in text_chunks:
                    table_chunk = TableChunk(
                        content=text_chunk.content,
                        table_structure=text_chunk.table_structure,
                        metadata=text_chunk.metadata,
                        chunk_id=text_chunk.chunk_id,
                        confidence_score=text_chunk.confidence_score,
                        source_location=text_chunk.source_location,
                        table_type=text_chunk.table_type
                    )
                    converted_chunks.append(table_chunk)
                return converted_chunks
            
            else:
                logger.warning(f"⚠️  Unknown method: {method}")
                return []
                
        except Exception as e:
            logger.error(f"❌ {method.upper()} processing failed: {e}")
            return []
    
    def _combine_and_deduplicate_chunks(self, all_chunks: Dict[str, List[TableChunk]]) -> List[TableChunk]:
        """Combine chunks from all methods and remove duplicates"""
        
        # Flatten all chunks
        combined_chunks = []
        for method, chunks in all_chunks.items():
            for chunk in chunks:
                # Add method information to metadata if not present
                if hasattr(chunk, 'metadata') and chunk.metadata:
                    chunk.metadata['hybrid_source_method'] = method
                elif hasattr(chunk, 'extraction_method'):
                    chunk.extraction_method = method
                combined_chunks.append(chunk)
        
        if not combined_chunks:
            return []
        
        # Remove duplicates using content similarity
        unique_chunks = []
        
        for chunk in combined_chunks:
            is_duplicate = False
            
            for existing_chunk in unique_chunks:
                similarity = self._calculate_content_similarity(chunk, existing_chunk)
                if similarity > 0.8:  # 80% similarity threshold
                    # Keep the chunk with higher confidence
                    if hasattr(chunk, 'confidence_score') and hasattr(existing_chunk, 'confidence_score'):
                        if chunk.confidence_score > existing_chunk.confidence_score:
                            # Replace existing with current
                            unique_chunks.remove(existing_chunk)
                            unique_chunks.append(chunk)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_chunks.append(chunk)
        
        # Sort by confidence score (highest first)
        unique_chunks.sort(
            key=lambda x: getattr(x, 'confidence_score', 0.0), 
            reverse=True
        )
        
        logger.info(f"🔄 Deduplication: {len(combined_chunks)} → {len(unique_chunks)} chunks")
        
        return unique_chunks
    
    def _calculate_content_similarity(self, chunk1: TableChunk, chunk2: TableChunk) -> float:
        """Calculate similarity between two table chunks"""
        try:
            # Compare content
            content1 = getattr(chunk1, 'content', '').lower()
            content2 = getattr(chunk2, 'content', '').lower()
            
            if not content1 or not content2:
                return 0.0
            
            # Simple word-based similarity
            words1 = set(content1.split())
            words2 = set(content2.split())
            
            if not words1 or not words2:
                return 0.0
            
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            
            return intersection / union if union > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"Failed to calculate similarity: {e}")
            return 0.0
    
    def _determine_best_method(self, processing_stats: Dict[str, Any]) -> str:
        """Determine the best method based on processing statistics"""
        
        successful_methods = processing_stats['methods_successful']
        
        if not successful_methods:
            return 'enhanced'  # Fallback
        
        # Scoring system
        method_scores = {}
        
        for method in successful_methods:
            score = 0.0
            
            # Factor 1: Number of chunks found (30% weight)
            chunk_count = processing_stats['chunk_counts'].get(method, 0)
            if chunk_count > 0:
                score += 0.3 * min(chunk_count / 5.0, 1.0)  # Normalize to max 5 chunks
            
            # Factor 2: Average confidence (40% weight)
            confidence = processing_stats['confidence_scores'].get(method, 0.0)
            score += 0.4 * confidence
            
            # Factor 3: Method reliability (30% weight)
            method_reliability = {
                'llmsherpa': 0.9,  # High reliability for PDFs
                'docling': 0.85,   # Good reliability
                'enhanced': 0.7    # Fallback reliability
            }
            score += 0.3 * method_reliability.get(method, 0.5)
            
            method_scores[method] = score
        
        # Return method with highest score
        best_method = max(method_scores.items(), key=lambda x: x[1])[0]
        
        logger.info(f"📊 Method scores: {method_scores}")
        
        return best_method
    
    def get_server_status(self) -> Dict[str, Any]:
        """Get current server status"""
        # Refresh server status
        if 'llmsherpa' in self.processors:
            llmsherpa_url = getattr(self.processors['llmsherpa'], 'nlm_ingestor_url', 'http://localhost:5010')
            self.server_status['llmsherpa'] = self._check_server_availability(llmsherpa_url)
        
        if 'docling' in self.processors:
            docling_url = getattr(self.processors['docling'], 'docling_server_url', 'http://localhost:5001')
            self.server_status['docling'] = self._check_docling_server(docling_url)
        
        return {
            'available_methods': self.available_methods,
            'server_status': self.server_status,
            'processors_initialized': list(self.processors.keys())
        }
    
    def analyze_document_comprehensive(self, file_path: str) -> Dict[str, Any]:
        """Comprehensive document analysis using all available methods"""
        
        analysis = {
            'file_path': file_path,
            'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            'methods': {},
            'summary': {},
            'recommendations': {}
        }
        
        # Analyze with each method
        for method in self.available_methods:
            if method == 'enhanced' or self.server_status.get(method, False):
                try:
                    if method == 'llmsherpa' and 'llmsherpa' in self.processors:
                        # LLMSherpa analysis would need to be implemented
                        analysis['methods'][method] = {'status': 'available', 'analysis': 'Not implemented'}
                    
                    elif method == 'docling' and 'docling' in self.processors:
                        docling_analysis = self.processors['docling'].analyze_document_with_docling(file_path)
                        analysis['methods'][method] = docling_analysis
                    
                    elif method == 'enhanced':
                        # Basic analysis for enhanced method
                        analysis['methods'][method] = {
                            'status': 'available',
                            'analysis': 'Enhanced processor available as fallback'
                        }
                        
                except Exception as e:
                    analysis['methods'][method] = {'status': 'error', 'error': str(e)}
            else:
                analysis['methods'][method] = {'status': 'server_unavailable'}
        
        # Generate summary
        available_methods = [m for m in self.available_methods if self.server_status.get(m, True)]
        analysis['summary'] = {
            'available_methods': available_methods,
            'recommended_method': available_methods[0] if available_methods else 'enhanced',
            'server_status': self.server_status
        }
        
        return analysis


def process_document_hybrid(file_path: str, 
                          llmsherpa_url: str = "http://localhost:5010/api/parseDocument?renderFormat=all",
                          docling_url: str = "http://localhost:5001",
                          force_method: Optional[str] = None) -> HybridExtractionResult:
    """Main function to process document with hybrid approach"""
    processor = HybridTableProcessor(llmsherpa_url, docling_url)
    return processor.process_document_hybrid(file_path, force_method)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        
        if not Path(file_path).exists():
            print(f"❌ File not found: {file_path}")
            sys.exit(1)
        
        print(f"🔍 Processing {file_path} with hybrid approach...")
        
        # Create processor
        processor = HybridTableProcessor()
        
        # Show server status
        status = processor.get_server_status()
        print(f"📡 Server Status: {status}")
        
        # Analyze document
        analysis = processor.analyze_document_comprehensive(file_path)
        print(f"📊 Document Analysis: {analysis['summary']}")
        
        # Process with hybrid approach
        result = processor.process_document_hybrid(file_path)
        
        print(f"\n🎯 Hybrid Processing Results:")
        print(f"   LLMSherpa chunks: {len(result.llmsherpa_chunks)}")
        print(f"   Docling chunks: {len(result.docling_chunks)}")
        print(f"   Enhanced chunks: {len(result.enhanced_chunks)}")
        print(f"   Text chunks: {len(result.text_chunks)}")
        print(f"   Final unique chunks: {len(result.final_chunks)}")
        print(f"   Recommended method: {result.recommended_method}")
        
        # Show processing stats
        print(f"\n📈 Processing Statistics:")
        for method, time_taken in result.processing_stats['processing_times'].items():
            chunk_count = result.processing_stats['chunk_counts'][method]
            confidence = result.processing_stats['confidence_scores'][method]
            print(f"   {method.upper()}: {chunk_count} chunks in {time_taken:.2f}s (confidence: {confidence:.2f})")
        
        # Show sample results
        for i, chunk in enumerate(result.final_chunks[:3]):
            print(f"\n📋 Table {i+1}:")
            print(f"   Type: {getattr(chunk, 'table_type', 'Unknown')}")
            print(f"   Confidence: {getattr(chunk, 'confidence_score', 0.0):.2f}")
            print(f"   Source: {getattr(chunk, 'extraction_method', 'Unknown')}")
            print(f"   Content preview: {getattr(chunk, 'content', '')[:200]}...")
    
    else:
        print("Usage: python hybrid_table_processor.py <file_path>")
        print("Example: python hybrid_table_processor.py knowledge_files/sample.pdf")