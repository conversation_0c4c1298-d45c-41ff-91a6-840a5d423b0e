import logging
import os
import json
from typing import List, Tuple, Dict, Any
from core.config import settings

# Optional imports with fallbacks
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("faiss not available. Vector search will be disabled.")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    logging.warning("numpy not available. Vector operations will be disabled.")
from models.rag_models import DocumentChunk, DocumentMetadata # Re-using
from rag.embedding_generator import EmbeddingGenerator # To get dimension
import asyncio

logger = logging.getLogger(__name__)

METADATA_FILE_SUFFIX = "_metadata.json"

class FaissVectorStore:
    def __init__(self, index_path: str = settings.RAG_FAISS_INDEX_PATH, 
                 embedding_dimension: int | None = None):
        if not FAISS_AVAILABLE or not NUMPY_AVAILABLE:
            logger.warning("FAISS or numpy not available. Vector store will be disabled.")
            self.index = None
            self.metadata_store = {}
            self.next_id = 0
            self.embedding_dimension = embedding_dimension
            return
            
        self.index_path = index_path
        self.metadata_path = self.index_path.replace(".faiss", METADATA_FILE_SUFFIX)
        self.index = None  # Will be faiss.Index if available
        self.metadata_store: Dict[int, Dict[str, Any]] = {} # Maps FAISS index ID to chunk_data (text, metadata, chunk_id)
        self.next_id = 0 # To keep track of FAISS index IDs if building from scratch
        self.embedding_dimension = embedding_dimension

        # Ensure directories exist
        os.makedirs(os.path.dirname(self.index_path), exist_ok=True)

        self._load_or_initialize_index()

    def _load_or_initialize_index(self):
        if os.path.exists(self.index_path) and os.path.exists(self.metadata_path):
            try:
                logger.info(f"Loading existing FAISS index from {self.index_path}")
                self.index = faiss.read_index(self.index_path)
                logger.info(f"FAISS index loaded. Index has {self.index.ntotal} vectors.")
                
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata_store = {int(k): v for k, v in json.load(f).items()} 
                self.next_id = self.index.ntotal # Start next_id from the current total number of vectors
                # Infer embedding dimension if not provided
                if self.index.ntotal > 0 and self.embedding_dimension is None:
                    # This is a bit of a hack if index is empty or not IDMap. D is dimension.
                    # For IndexIDMap, d is available. For flat, also d.
                    if hasattr(self.index, 'd'):
                        self.embedding_dimension = self.index.d
                        logger.info(f"Inferred embedding dimension from loaded index: {self.embedding_dimension}")
                    else: # Fallback, try to get from EmbeddingGenerator
                        emb_gen = EmbeddingGenerator()
                        if emb_gen.is_available:
                            self.embedding_dimension = emb_gen.get_embedding_dimension()
                            logger.info(f"Got embedding dimension from EmbeddingGenerator: {self.embedding_dimension}")
                logger.info(f"Loaded {len(self.metadata_store)} metadata entries.")
                return
            except Exception as e:
                logger.error(f"Error loading FAISS index or metadata: {e}. Re-initializing.", exc_info=True)
                # Reset to ensure clean initialization if loading fails
                self.index = None
                self.metadata_store = {}
                self.next_id = 0
        
        # Initialize if not loaded or if loading failed
        if self.embedding_dimension is None:
            emb_gen = EmbeddingGenerator()
            if emb_gen.is_available:
                self.embedding_dimension = emb_gen.get_embedding_dimension()
                if self.embedding_dimension is None:
                    logger.error("Could not determine embedding dimension for FAISS index initialization from EmbeddingGenerator.")
                    raise RuntimeError("FAISS index cannot be initialized without embedding dimension.")
            else:
                logger.error("EmbeddingGenerator not available to determine embedding dimension for FAISS.")
                raise RuntimeError("FAISS index cannot be initialized: EmbeddingGenerator unavailable.")

        logger.info(f"Initializing new FAISS index at {self.index_path} with dimension {self.embedding_dimension}")
        # Using IndexIDMap to map vectors to their original IDs (our self.next_id)
        # This allows us to easily retrieve the original data associated with a search result.
        # IndexFlatL2 is a common choice for exact search using L2 distance.
        self.index = faiss.IndexIDMap(faiss.IndexFlatL2(self.embedding_dimension))
        self.metadata_store = {}
        self.next_id = 0
        # Save empty index and metadata immediately to establish the files
        self.save_index()

    def add_chunks(self, chunks_with_embeddings: List[Tuple[Dict[str, Any], List[float]]]):
        """Adds chunks (text, metadata) and their embeddings to the FAISS index and metadata store."""
        if not FAISS_AVAILABLE or not NUMPY_AVAILABLE:
            logger.warning("FAISS or numpy not available. Cannot add chunks.")
            return 0
        if not self.index:
            logger.error("FAISS index is not initialized. Cannot add chunks.")
            return 0
        if not chunks_with_embeddings:
            return 0

        embeddings_to_add = []
        ids_to_add = []
        added_count = 0

        for chunk_data, embedding_vector in chunks_with_embeddings:
            if (not isinstance(embedding_vector, list) or 
                (self.embedding_dimension and len(embedding_vector) != self.embedding_dimension)):
                logger.warning(f"Skipping chunk due to mismatched embedding dimension. Expected {self.embedding_dimension}, got {len(embedding_vector)}. Chunk ID: {chunk_data.get('chunk_id')}")
                continue

            embeddings_to_add.append(embedding_vector)
            ids_to_add.append(self.next_id)
            self.metadata_store[self.next_id] = chunk_data # Store original chunk_data (text, metadata, chunk_id)
            self.next_id += 1
            added_count +=1
        
        if embeddings_to_add:
            embeddings_np = np.array(embeddings_to_add, dtype='float32')
            ids_np = np.array(ids_to_add, dtype='int64')
            self.index.add_with_ids(embeddings_np, ids_np)
            logger.info(f"Added {added_count} new chunks to FAISS index. Total vectors: {self.index.ntotal}")
            self.save_index() # Save after adding
        return added_count

    async def search_similar_chunks(self, query_embedding: List[float], top_k: int = settings.RAG_TOP_K_RESULTS) -> List[DocumentChunk]:
        if not FAISS_AVAILABLE or not NUMPY_AVAILABLE:
            logger.warning("FAISS or numpy not available. Cannot perform search.")
            return []
        if not self.index or self.index.ntotal == 0:
            logger.warning("FAISS index is not initialized or is empty. Cannot perform search.")
            return []
        if not query_embedding or (self.embedding_dimension and len(query_embedding) != self.embedding_dimension):
            logger.error(f"Invalid query embedding for search. Expected dim {self.embedding_dimension}, got {len(query_embedding) if query_embedding else 'None'}")
            return []

        query_embedding_np = np.array([query_embedding], dtype='float32') # FAISS expects a 2D array
        
        # Run FAISS search in a thread to be async-friendly
        def _search():
            distances, indices = self.index.search(query_embedding_np, k=min(top_k, self.index.ntotal))
            return distances[0], indices[0]

        distances, faiss_ids = await asyncio.to_thread(_search)
        
        retrieved_chunks = []
        for i, faiss_id in enumerate(faiss_ids):
            if faiss_id == -1: # FAISS returns -1 for indices if fewer than k results are found
                continue
            original_chunk_data = self.metadata_store.get(int(faiss_id))
            if original_chunk_data:
                # Reconstruct DocumentChunk from stored dict
                metadata_dict = original_chunk_data.get("metadata", {})
                doc_chunk = DocumentChunk(
                    chunk_id=original_chunk_data.get("chunk_id", "unknown_id"),
                    text=original_chunk_data.get("text", ""),
                    metadata=DocumentMetadata(**metadata_dict), # Create Pydantic model from dict
                    score=float(distances[i]) # L2 distance, lower is better
                )
                retrieved_chunks.append(doc_chunk)
            else:
                logger.warning(f"Metadata not found for FAISS ID: {faiss_id}")
        
        logger.info(f"Retrieved {len(retrieved_chunks)} chunks from FAISS for query.")
        return retrieved_chunks

    def save_index(self):
        if self.index:
            faiss.write_index(self.index, self.index_path)
            logger.info(f"FAISS index saved to {self.index_path}")
        with open(self.metadata_path, 'w', encoding='utf-8') as f:
            json.dump(self.metadata_store, f, indent=4)
        logger.info(f"Metadata store saved to {self.metadata_path}")

    def get_total_vectors(self) -> int:
        return self.index.ntotal if self.index else 0

    def clear_index(self):
        """Clears the FAISS index and associated metadata."""
        logger.info("Clearing FAISS index and metadata...")
        if self.embedding_dimension is None: # Should not happen if initialized properly
            emb_gen = EmbeddingGenerator()
            if emb_gen.is_available: 
                self.embedding_dimension = emb_gen.get_embedding_dimension() or 384 # fallback dim
            else: 
                self.embedding_dimension = 384 # Absolute fallback
        
        self.index = faiss.IndexIDMap(faiss.IndexFlatL2(self.embedding_dimension))
        self.metadata_store = {}
        self.next_id = 0
        self.save_index() # Save the now empty index and metadata
        logger.info("FAISS index and metadata cleared.")

# Singleton instance for the vector store
# faiss_vector_store = FaissVectorStore()

# Example usage (for testing this module directly)
if __name__ == '__main__':
    async def test_faiss_store():
        print("Testing FaissVectorStore...")
        # Ensure RAG_EMBEDDING_MODEL_NAME is set for EmbeddingGenerator to get dimension
        if not settings.RAG_EMBEDDING_MODEL_NAME:
            print("Please ensure RAG_EMBEDDING_MODEL_NAME is set for EmbeddingGenerator.")
            return

        # Clean up old index files for a fresh test if they exist
        if os.path.exists(settings.RAG_FAISS_INDEX_PATH):
            os.remove(settings.RAG_FAISS_INDEX_PATH)
        metadata_path_for_test = settings.RAG_FAISS_INDEX_PATH.replace(".faiss", METADATA_FILE_SUFFIX)
        if os.path.exists(metadata_path_for_test):
            os.remove(metadata_path_for_test)
        print(f"Cleaned up old index files for test if any.")

        emb_gen = EmbeddingGenerator()
        if not emb_gen.is_available:
            print("EmbeddingGenerator is not available. Cannot run FaissVectorStore test.")
            return
        
        store_dimension = emb_gen.get_embedding_dimension()
        if not store_dimension:
            print("Could not get embedding dimension. Cannot run FaissVectorStore test.")
            return
            
        print(f"Initializing FaissVectorStore with dimension: {store_dimension}")
        store = FaissVectorStore(embedding_dimension=store_dimension)
        print(f"Initial total vectors: {store.get_total_vectors()}")

        # Create some dummy chunks with embeddings
        test_chunks_data = [
            ({"chunk_id": "chunk1", "text": "FastAPI is great for building APIs.", "metadata": {"filename": "doc1.txt", "source": "doc1.txt"}}),
            ({"chunk_id": "chunk2", "text": "RAG combines retrieval with generation.", "metadata": {"filename": "doc2.txt", "source": "doc2.txt"}}),
            ({"chunk_id": "chunk3", "text": "Gemma is a new family of open models.", "metadata": {"filename": "doc3.txt", "source": "doc3.txt"}})
        ]
        texts_to_embed = [cd["text"] for cd in test_chunks_data]
        dummy_embeddings = await emb_gen.generate_embeddings(texts_to_embed)

        if not dummy_embeddings or len(dummy_embeddings) != len(test_chunks_data):
            print("Failed to generate dummy embeddings. Aborting test.")
            return

        chunks_with_embeddings_to_add = list(zip(test_chunks_data, dummy_embeddings))
        store.add_chunks(chunks_with_embeddings_to_add)
        print(f"Total vectors after adding: {store.get_total_vectors()}")

        # Test search
        query_text = "Tell me about FastAPI"
        query_embedding = await emb_gen.generate_embeddings([query_text])
        if query_embedding and query_embedding[0]:
            print(f"\nSearching for: '{query_text}'")
            results = await store.search_similar_chunks(query_embedding[0], top_k=2)
            print(f"Search results ({len(results)}):")
            for res_chunk in results:
                print(f"  ID: {res_chunk.chunk_id}, Score: {res_chunk.score:.4f}, Text: {res_chunk.text[:50]}..., Metadata: {res_chunk.metadata.source}")
        else:
            print("Failed to generate query embedding for search test.")
        
        # Test clearing the index
        # print("\nClearing the index...")
        # store.clear_index()
        # print(f"Total vectors after clearing: {store.get_total_vectors()}")

        # Note: This test creates files in ./data/faiss_index/. You might want to clean them up manually after testing.
        print(f"\nFAISS index and metadata are stored at: {store.index_path} and {store.metadata_path}")

    asyncio.run(test_faiss_store()) 