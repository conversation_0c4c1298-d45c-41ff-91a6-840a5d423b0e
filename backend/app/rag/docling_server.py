#!/usr/bin/env python3
"""
Fixed Docling Server - Xử lý ĐÚNG các file văn bản và bảng biểu
Hỗ trợ: PDF, DOCX, TXT, JSON, HTML, MD, CSV với docling và text processing
Tương thích với Open WebUI và yêu cầu của LLM
"""

import os
import sys
import json
import logging
import traceback
import tempfile
import mimetypes
import chardet
from pathlib import Path
from typing import Dict, Any, List, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS

# Document processing imports
try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions, TesseractOcrOptions
    from docling.document_converter import PdfFormatOption
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False

# Text processing imports
try:
    import markdown
    from bs4 import BeautifulSoup
    MARKUP_PROCESSING_AVAILABLE = True
except ImportError:
    MARKUP_PROCESSING_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from docx import Document as DocxDocument
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins=["*"])

class FixedDoclingServer:
    """Fixed Docling server xử lý đúng các file văn bản và bảng biểu"""
    
    def __init__(self):
        self.supported_formats = {
            # Docling formats (PDF, images)
            '.pdf': 'docling',
            '.png': 'docling', 
            '.jpg': 'docling',
            '.jpeg': 'docling',
            '.tiff': 'docling',
            '.tif': 'docling',
            '.bmp': 'docling',
            
            # Text formats (xử lý riêng)
            '.txt': 'text',
            '.md': 'markdown',
            '.html': 'html',
            '.htm': 'html',
            '.json': 'json',
            '.csv': 'csv',
            '.docx': 'docx'
        }
        
        logger.info("🚀 Fixed Docling Server initialized")
        logger.info(f"📋 Supported formats: {list(self.supported_formats.keys())}")
        self._log_available_libraries()
    
    def _log_available_libraries(self):
        """Log available processing libraries"""
        libraries = {
            'Docling': DOCLING_AVAILABLE,
            'python-docx': PYTHON_DOCX_AVAILABLE,
            'markup processing': MARKUP_PROCESSING_AVAILABLE,
            'pandas': PANDAS_AVAILABLE
        }
        
        for lib, available in libraries.items():
            status = "✅" if available else "❌"
            logger.info(f"  {status} {lib}")
    
    def process_file(self, file_path: str, ocr_engine: str = "tesseract", 
                    ocr_langs: List[str] = None) -> Dict[str, Any]:
        """Process file dựa trên định dạng"""
        try:
            file_ext = Path(file_path).suffix.lower()
            file_size = os.path.getsize(file_path)
            
            logger.info(f"📄 Processing: {Path(file_path).name} ({file_ext}, {file_size} bytes)")
            
            if file_ext not in self.supported_formats:
                return self._create_error_response(
                    f"Unsupported file format: {file_ext}. Supported: {list(self.supported_formats.keys())}",
                    file_path
                )
            
            processor_type = self.supported_formats[file_ext]
            
            # Route to appropriate processor
            if processor_type == 'docling':
                return self._process_with_docling(file_path, ocr_engine, ocr_langs)
            elif processor_type == 'text':
                return self._process_text_file(file_path)
            elif processor_type == 'markdown':
                return self._process_markdown_file(file_path)
            elif processor_type == 'html':
                return self._process_html_file(file_path)
            elif processor_type == 'json':
                return self._process_json_file(file_path)
            elif processor_type == 'csv':
                return self._process_csv_file(file_path)
            elif processor_type == 'docx':
                return self._process_docx_file(file_path)
            else:
                return self._create_error_response(
                    f"No processor available for {processor_type}",
                    file_path
                )
                
        except Exception as e:
            logger.error(f"❌ Error processing {file_path}: {e}")
            logger.error(traceback.format_exc())
            return self._create_error_response(str(e), file_path)
    
    def _process_with_docling(self, file_path: str, ocr_engine: str, ocr_langs: List[str]) -> Dict[str, Any]:
        """Process PDF and images with Docling"""
        if not DOCLING_AVAILABLE:
            return self._create_error_response("Docling not available", file_path)
        
        try:
            logger.info(f"🔄 Processing with Docling: {Path(file_path).name}")
            
            # Configure OCR
            actual_ocr_langs = ocr_langs if ocr_langs else ['vie', 'eng']
            ocr_options = TesseractOcrOptions(lang=actual_ocr_langs)
            
            disable_ocr = os.environ.get('DISABLE_OCR', '0').lower() in ['1', 'true', 'yes']
            do_ocr = not disable_ocr
            
            # Pipeline options
            pipeline_options = PdfPipelineOptions(
                do_ocr=do_ocr,
                do_table_structure=True,
                ocr_options=ocr_options if do_ocr else None
            )
            
            # Format options
            format_options = {
                InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options),
                InputFormat.IMAGE: PdfFormatOption(pipeline_options=pipeline_options)
            }
            
            # Convert document
            converter = DocumentConverter(format_options=format_options)
            result = converter.convert(file_path)
            
            # Extract content and tables
            text_content = ""
            tables = []
            
            if hasattr(result, 'document') and result.document:
                doc = result.document
                
                # Extract text content
                try:
                    text_content = doc.export_to_markdown()
                    logger.info(f"✅ Extracted text: {len(text_content)} characters")
                except Exception as e:
                    logger.warning(f"Failed to export to markdown: {e}")
                    # Fallback to other methods
                    if hasattr(doc, 'texts') and doc.texts:
                        text_parts = []
                        for text_block in doc.texts:
                            if isinstance(text_block, str):
                                text_parts.append(text_block)
                            elif hasattr(text_block, 'text'):
                                text_parts.append(str(text_block.text))
                            else:
                                text_parts.append(str(text_block))
                        text_content = "\n\n".join(text_parts)
                
                # Extract tables
                if hasattr(doc, 'tables') and doc.tables:
                    logger.info(f"Found {len(doc.tables)} tables")
                    for i, table in enumerate(doc.tables):
                        try:
                            table_md = self._table_to_markdown(table)
                            if table_md:
                                tables.append({
                                    'table_id': i,
                                    'content': str(table),
                                    'markdown': table_md,
                                    'confidence': getattr(table, 'confidence', 0.95)
                                })
                        except Exception as e:
                            logger.warning(f"Failed to process table {i}: {e}")
            
            return self._create_success_response(text_content, tables, file_path, "docling")
            
        except Exception as e:
            logger.error(f"❌ Docling processing failed: {e}")
            return self._create_error_response(f"Docling error: {str(e)}", file_path)
    
    def _process_text_file(self, file_path: str) -> Dict[str, Any]:
        """Process plain text files"""
        logger.info(f"📄 Processing text file: {Path(file_path).name}")
        
        try:
            # Detect encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'
            
            # Read file
            with open(file_path, 'r', encoding=encoding) as f:
                text_content = f.read()
            
            # Extract tables from text (markdown-style tables)
            tables = self._extract_tables_from_text(text_content)
            
            logger.info(f"✅ Text processed: {len(text_content)} characters, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, file_path, "text")
            
        except Exception as e:
            logger.error(f"❌ Text processing failed: {e}")
            return self._create_error_response(f"Text error: {e}", file_path)
    
    def _process_docx_file(self, file_path: str) -> Dict[str, Any]:
        """Process DOCX files"""
        logger.info(f"📝 Processing DOCX: {Path(file_path).name}")
        
        if not PYTHON_DOCX_AVAILABLE:
            return self._create_error_response("python-docx not available", file_path)
        
        try:
            doc = DocxDocument(file_path)
            
            # Extract text
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text.strip())
            text_content = '\n\n'.join(paragraphs)
            
            # Extract tables
            tables = []
            for i, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                
                if table_data:
                    markdown_table = self._table_data_to_markdown(table_data)
                    tables.append({
                        'table_id': i,
                        'content': table_data,
                        'markdown': markdown_table,
                        'confidence': 0.95
                    })
            
            logger.info(f"✅ DOCX processed: {len(text_content)} chars, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, file_path, "docx")
            
        except Exception as e:
            logger.error(f"❌ DOCX processing failed: {e}")
            return self._create_error_response(f"DOCX error: {e}", file_path)
    
    def _process_markdown_file(self, file_path: str) -> Dict[str, Any]:
        """Process Markdown files"""
        logger.info(f"📝 Processing Markdown: {Path(file_path).name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            text_content = md_content
            tables = self._extract_tables_from_text(md_content)
            
            logger.info(f"✅ Markdown processed: {len(text_content)} chars, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, file_path, "markdown")
            
        except Exception as e:
            logger.error(f"❌ Markdown processing failed: {e}")
            return self._create_error_response(f"Markdown error: {e}", file_path)
    
    def _process_html_file(self, file_path: str) -> Dict[str, Any]:
        """Process HTML files"""
        logger.info(f"🌐 Processing HTML: {Path(file_path).name}")
        
        if not MARKUP_PROCESSING_AVAILABLE:
            return self._create_error_response("HTML processing libraries not available", file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract text
            text_content = soup.get_text()
            
            # Extract tables
            tables = self._extract_tables_from_html(soup)
            
            logger.info(f"✅ HTML processed: {len(text_content)} chars, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, file_path, "html")
            
        except Exception as e:
            logger.error(f"❌ HTML processing failed: {e}")
            return self._create_error_response(f"HTML error: {e}", file_path)
    
    def _process_json_file(self, file_path: str) -> Dict[str, Any]:
        """Process JSON files"""
        logger.info(f"📋 Processing JSON: {Path(file_path).name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Convert to formatted text
            text_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            
            # Extract tables from JSON arrays
            tables = self._extract_tables_from_json(json_data)
            
            logger.info(f"✅ JSON processed: {len(text_content)} chars, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, file_path, "json")
            
        except Exception as e:
            logger.error(f"❌ JSON processing failed: {e}")
            return self._create_error_response(f"JSON error: {e}", file_path)
    
    def _process_csv_file(self, file_path: str) -> Dict[str, Any]:
        """Process CSV files"""
        logger.info(f"📊 Processing CSV: {Path(file_path).name}")
        
        try:
            if PANDAS_AVAILABLE:
                # Use pandas for better CSV handling
                df = pd.read_csv(file_path)
                
                # Convert to text
                text_content = df.to_string()
                
                # Convert to table
                table_data = [df.columns.tolist()] + df.values.tolist()
                markdown_table = self._table_data_to_markdown(table_data)
                tables = [{
                    'table_id': 0,
                    'content': table_data,
                    'markdown': markdown_table,
                    'confidence': 0.99
                }]
            else:
                # Fallback to basic CSV reading
                import csv
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                
                text_content = '\n'.join([' | '.join(row) for row in rows])
                
                tables = []
                if rows:
                    markdown_table = self._table_data_to_markdown(rows)
                    tables = [{
                        'table_id': 0,
                        'content': rows,
                        'markdown': markdown_table,
                        'confidence': 0.95
                    }]
            
            logger.info(f"✅ CSV processed: {len(text_content)} chars, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, file_path, "csv")
            
        except Exception as e:
            logger.error(f"❌ CSV processing failed: {e}")
            return self._create_error_response(f"CSV error: {e}", file_path)
    
    # Helper methods
    def _table_to_markdown(self, table_item) -> str:
        """Convert Docling table object to markdown"""
        try:
            if hasattr(table_item, 'to_markdown') and callable(table_item.to_markdown):
                md = table_item.to_markdown()
                if md is not None:
                    return str(md)
            
            if hasattr(table_item, 'data'):
                return self._table_data_to_markdown(table_item.data)
            
            return str(table_item)
            
        except Exception as e:
            logger.error(f"Error converting table to markdown: {e}")
            return str(table_item)
    
    def _table_data_to_markdown(self, table_data: List[List[str]]) -> str:
        """Convert table data to markdown format"""
        if not table_data or len(table_data) == 0:
            return ""
        
        try:
            markdown = ""
            
            # Header row
            if len(table_data) > 0:
                header = "| " + " | ".join(str(cell) for cell in table_data[0]) + " |"
                separator = "|" + "|".join([" --- " for _ in table_data[0]]) + "|"
                markdown = header + "\n" + separator + "\n"
                
                # Data rows
                for row in table_data[1:]:
                    row_md = "| " + " | ".join(str(cell) for cell in row) + " |"
                    markdown += row_md + "\n"
            
            return markdown
        except Exception as e:
            logger.error(f"Error converting table data to markdown: {e}")
            return str(table_data)
    
    def _extract_tables_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract markdown-style tables from text"""
        tables = []
        lines = text.split('\n')
        
        current_table = []
        table_id = 0
        
        for line in lines:
            line = line.strip()
            if line.startswith('|') and line.endswith('|'):
                current_table.append(line)
            else:
                if current_table and len(current_table) > 2:  # At least header + separator + 1 row
                    # Parse table
                    table_data = []
                    for table_line in current_table:
                        if '---' not in table_line:  # Skip separator line
                            cells = [cell.strip() for cell in table_line.split('|')[1:-1]]
                            table_data.append(cells)
                    
                    if table_data:
                        markdown_table = self._table_data_to_markdown(table_data)
                        tables.append({
                            'table_id': table_id,
                            'content': table_data,
                            'markdown': markdown_table,
                            'confidence': 0.85
                        })
                        table_id += 1
                
                current_table = []
        
        # Don't forget the last table
        if current_table and len(current_table) > 2:
            table_data = []
            for table_line in current_table:
                if '---' not in table_line:
                    cells = [cell.strip() for cell in table_line.split('|')[1:-1]]
                    table_data.append(cells)
            
            if table_data:
                markdown_table = self._table_data_to_markdown(table_data)
                tables.append({
                    'table_id': table_id,
                    'content': table_data,
                    'markdown': markdown_table,
                    'confidence': 0.85
                })
        
        return tables
    
    def _extract_tables_from_html(self, soup) -> List[Dict[str, Any]]:
        """Extract tables from HTML soup"""
        tables = []
        html_tables = soup.find_all('table')
        
        for i, table in enumerate(html_tables):
            table_data = []
            rows = table.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                row_data = [cell.get_text().strip() for cell in cells]
                if row_data:
                    table_data.append(row_data)
            
            if table_data:
                markdown_table = self._table_data_to_markdown(table_data)
                tables.append({
                    'table_id': i,
                    'content': table_data,
                    'markdown': markdown_table,
                    'confidence': 0.95
                })
        
        return tables
    
    def _extract_tables_from_json(self, json_data) -> List[Dict[str, Any]]:
        """Extract tables from JSON arrays"""
        tables = []
        table_id = 0
        
        def extract_from_value(value, path=""):
            nonlocal table_id
            
            if isinstance(value, list) and len(value) > 0:
                # Check if it's a list of objects (table-like)
                if isinstance(value[0], dict):
                    # Extract keys as headers
                    headers = list(value[0].keys())
                    table_data = [headers]
                    
                    # Extract values
                    for item in value:
                        if isinstance(item, dict):
                            row = [str(item.get(key, '')) for key in headers]
                            table_data.append(row)
                    
                    if len(table_data) > 1:
                        markdown_table = self._table_data_to_markdown(table_data)
                        tables.append({
                            'table_id': table_id,
                            'path': path,
                            'content': table_data,
                            'markdown': markdown_table,
                            'confidence': 0.90
                        })
                        table_id += 1
                
                # Recursively check nested structures
                for i, item in enumerate(value):
                    extract_from_value(item, f"{path}[{i}]")
            
            elif isinstance(value, dict):
                for key, val in value.items():
                    new_path = f"{path}.{key}" if path else key
                    extract_from_value(val, new_path)
        
        extract_from_value(json_data)
        return tables
    
    def _create_success_response(self, text_content: str, tables: List[Dict],
                               file_path: str, processor: str) -> Dict[str, Any]:
        """Create successful response với format chuẩn cho LLM"""
        # Tạo content list như test file mong đợi
        content_parts = []
        if text_content:
            content_parts.append(text_content)
        
        # Add table markdown to content
        for table in tables:
            if table.get('markdown'):
                content_parts.append(table['markdown'])
        
        return {
            'success': True,
            'text': text_content,  # Field mà test file mong đợi
            'content': content_parts,  # Field mà docling server hiện tại trả về
            'tables': tables,
            'total_tables': len(tables),
            'method': processor,
            'file_path': file_path,
            'metadata': {
                'file_name': Path(file_path).name,
                'processor': processor,
                'text_length': len(text_content),
                'table_count': len(tables)
            }
        }
    
    def _create_error_response(self, error_msg: str, file_path: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            'success': False,
            'error': error_msg,
            'text': '',
            'content': [],
            'tables': [],
            'total_tables': 0,
            'method': 'error',
            'file_path': file_path
        }

# Initialize processor
processor = FixedDoclingServer()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'fixed-docling-server',
        'version': '2.0.0',
        'docling_available': DOCLING_AVAILABLE,
        'supported_formats': list(processor.supported_formats.keys()),
        'endpoints': ['/extract_tables', '/extract_content', '/extract_text', '/v1/convert/file', '/v1alpha/convert/file', '/health']
    })

# Keep this /v1alpha endpoint for backward compatibility with Open WebUI
@app.route('/v1/convert/file', methods=['POST'])
@app.route('/v1/convert/file', '/v1alpha/convert/file', methods=['POST'])
@app.route('/extract_tables', methods=['POST'])
@app.route('/extract_content', methods=['POST'])
@app.route('/extract_text', methods=['POST'])
def process_document():
    """Process uploaded document - unified endpoint"""
    logger.info(f"Received request on {request.endpoint}")
    logger.info(f"Request files: {list(request.files.keys())}")
    
    try:
        # Check for file in different possible field names
        file_obj = None
        for field_name in ['files', 'file']:
            if field_name in request.files:
                file_obj = request.files[field_name]
                break
        
        if not file_obj:
            return jsonify({
                'success': False,
                'error': 'No file uploaded. Expected file in \'files\' or \'file\' field.',
                'text': '',
                'content': [],
                'tables': [],
                'total_tables': 0
            }), 400
        
        if file_obj.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected (filename is empty).',
                'text': '',
                'content': [],
                'tables': [],
                'total_tables': 0
            }), 400
        
        # Get OCR settings from request form
        ocr_langs = request.form.getlist('ocr_lang') or ['vie', 'eng']
        ocr_engine = request.form.get('ocr_engine', 'tesseract')
        
        logger.info(f"Processing file: {file_obj.filename}")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file_obj.filename).suffix) as tmp_file:
            file_obj.save(tmp_file.name)
            tmp_file_path = tmp_file.name
        
        try:
            # Process file
            result = processor.process_file(tmp_file_path, ocr_engine, ocr_langs)
            return jsonify(result)
        finally:
            # Clean up temporary file
            try:
                os.unlink(tmp_file_path)
            except:
                pass
    
    except Exception as e:
        logger.error(f"Error in process_document endpoint: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e),
            'text': '',
            'content': [],
            'tables': [],
            'total_tables': 0
        }), 500

@app.route('/extract_tables_from_path', methods=['POST'])
def extract_tables_from_path():
    """Extract tables from file path (for internal use)"""
    try:
        data = request.get_json()
        if not data or 'file_path' not in data:
            return jsonify({
                'success': False,
                'error': 'file_path required in JSON body',
                'text': '',
                'content': [],
                'tables': [],
                'total_tables': 0
            }), 400
        
        file_path = data['file_path']
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': f'File not found: {file_path}',
                'text': '',
                'content': [],
                'tables': [],
                'total_tables': 0
            }), 404
        
        # Process file
        result = processor.process_file(file_path, 'tesseract', ['vie', 'eng'])
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Error in extract_tables_from_path endpoint: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e),
            'text': '',
            'content': [],
            'tables': [],
            'total_tables': 0
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Fixed Docling Server...")
    print("📊 Hỗ trợ đầy đủ các file văn bản và bảng biểu")
    print("🔗 Server will run on http://localhost:5001")
    print("📋 Endpoints:")
    print("   - GET  /health - Health check")
    print("   - POST /extract_tables - Extract from uploaded file")
    print("   - POST /extract_content - Extract content from uploaded file")
    print("   - POST /extract_text - Extract text from uploaded file")
    print("   - POST /v1alpha/convert/file - Docling compatible endpoint")
    print("   - POST /extract_tables_from_path - Extract from file path")
    
    app.run(host='0.0.0.0', port=5001, debug=False)