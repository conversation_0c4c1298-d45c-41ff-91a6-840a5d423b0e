import logging
from typing import List, Tuple, Dict, Any

# Optional langchain import with fallback
try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("langchain_text_splitters not available. Using simple text splitting.")

# Optional imports with fallbacks
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logging.warning("PyMuPDF not available. PDF processing will be disabled.")

try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logging.warning("python-docx not available. DOCX processing will be disabled.")

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    logging.warning("openpyxl not available. Excel processing will be disabled.")
from core.config import settings
from models.rag_models import DocumentMetadata # Re-using from rag_models
import hashlib
import os

logger = logging.getLogger(__name__)

class SimpleTextSplitter:
    """Simple fallback text splitter when langchain is not available."""
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def split_text(self, text: str) -> List[str]:
        """Simple text splitting by sentences and paragraphs."""
        if len(text) <= self.chunk_size:
            return [text]
        
        chunks = []
        start = 0
        while start < len(text):
            end = start + self.chunk_size
            if end >= len(text):
                chunks.append(text[start:])
                break
            
            # Try to break at sentence or paragraph boundary
            chunk = text[start:end]
            last_period = chunk.rfind('.')
            last_newline = chunk.rfind('\n')
            
            if last_period > len(chunk) * 0.7:  # If period is in last 30%
                end = start + last_period + 1
            elif last_newline > len(chunk) * 0.7:  # If newline is in last 30%
                end = start + last_newline + 1
            
            chunks.append(text[start:end])
            start = end - self.chunk_overlap
            
        return chunks

class DocumentProcessor:
    def __init__(self):
        if LANGCHAIN_AVAILABLE:
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=settings.RAG_CHUNK_SIZE,
                chunk_overlap=settings.RAG_CHUNK_OVERLAP,
                length_function=len,
                is_separator_regex=False,
            )
        else:
            self.text_splitter = SimpleTextSplitter(
                chunk_size=settings.RAG_CHUNK_SIZE,
                chunk_overlap=settings.RAG_CHUNK_OVERLAP
            )
        # Ensure documents directory exists
        os.makedirs(settings.RAG_DOCUMENTS_DIR, exist_ok=True)

    def _extract_text_from_pdf(self, file_path: str) -> List[Tuple[str, DocumentMetadata]]:
        """Extracts text page by page from a PDF file."""
        if not PYMUPDF_AVAILABLE:
            logger.error("PyMuPDF not available. Cannot process PDF files.")
            return []
            
        doc_pages = []
        try:
            doc = fitz.open(file_path)
            for page_num, page in enumerate(doc):
                text = page.get_text("text")
                if text.strip(): # Only add if there is text on the page
                    metadata = DocumentMetadata(
                        filename=os.path.basename(file_path),
                        source=os.path.basename(file_path),
                        page_number=page_num + 1
                    )
                    doc_pages.append((text, metadata))
            doc.close()
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}", exc_info=True)
        return doc_pages

    def _extract_text_from_docx(self, file_path: str) -> List[Tuple[str, DocumentMetadata]]:
        """Extracts text from a DOCX file."""
        if not DOCX_AVAILABLE:
            logger.error("python-docx not available. Cannot process DOCX files.")
            return []
            
        full_text = []
        try:
            document = DocxDocument(file_path)
            for para in document.paragraphs:
                if para.text.strip():
                    full_text.append(para.text)
            if full_text:
                metadata = DocumentMetadata(
                    filename=os.path.basename(file_path),
                    source=os.path.basename(file_path)
                )
                return [("\n".join(full_text), metadata)]
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {e}", exc_info=True)
        return []

    def _extract_text_from_txt(self, file_path: str) -> List[Tuple[str, DocumentMetadata]]:
        """Extracts text from a TXT file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            if text.strip():
                metadata = DocumentMetadata(
                    filename=os.path.basename(file_path),
                    source=os.path.basename(file_path)
                )
                return [(text, metadata)]
        except Exception as e:
            logger.error(f"Error extracting text from TXT {file_path}: {e}", exc_info=True)
        return []

    def _extract_text_from_excel(self, file_path: str) -> List[Tuple[str, DocumentMetadata]]:
        """Extracts text from all cells in an XLSX/XLS file, sheet by sheet."""
        if not OPENPYXL_AVAILABLE:
            logger.error("openpyxl not available. Cannot process Excel files.")
            return []
            
        doc_sheets_content = []
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True) # data_only=True to get values, not formulas
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_text = []
                for row in sheet.iter_rows():
                    row_text = []
                    for cell in row:
                        if cell.value is not None:
                            row_text.append(str(cell.value).strip())
                    if row_text:
                        sheet_text.append(" | ".join(row_text)) # Join cell values in a row with a separator
                
                if sheet_text:
                    full_sheet_text = "\n".join(sheet_text)
                    metadata = DocumentMetadata(
                        filename=os.path.basename(file_path),
                        source=f"{os.path.basename(file_path)} - Sheet: {sheet_name}", # More specific source
                        page_number=None # Or use sheet index workbook.sheetnames.index(sheet_name) + 1
                    )
                    doc_sheets_content.append((full_sheet_text, metadata))
        except Exception as e:
            logger.error(f"Error extracting text from Excel {file_path}: {e}", exc_info=True)
        return doc_sheets_content

    def process_document(self, file_path: str) -> List[Dict[str, Any]]: # Returns list of chunk dicts
        """Processes a single document: extracts text, splits into chunks, and adds metadata."""
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        raw_pages_with_metadata: List[Tuple[str, DocumentMetadata]] = []

        if ext == ".pdf":
            raw_pages_with_metadata = self._extract_text_from_pdf(file_path)
        elif ext == ".docx":
            raw_pages_with_metadata = self._extract_text_from_docx(file_path)
        elif ext == ".txt":
            raw_pages_with_metadata = self._extract_text_from_txt(file_path)
        elif ext in [".xlsx", ".xls"]:
             raw_pages_with_metadata = self._extract_text_from_excel(file_path)
        else:
            logger.warning(f"Unsupported file type: {ext} for file {file_path}")
            return []

        all_chunks = []
        for page_text, metadata_obj in raw_pages_with_metadata:
            if not page_text.strip():
                continue
            
            split_texts = self.text_splitter.split_text(page_text)
            for i, text_chunk in enumerate(split_texts):
                chunk_id = hashlib.md5((metadata_obj.source + text_chunk + str(i)).encode()).hexdigest()
                chunk_data = {
                    "chunk_id": chunk_id,
                    "text": text_chunk,
                    "metadata": metadata_obj.model_dump() # Convert Pydantic model to dict
                }
                all_chunks.append(chunk_data)
        
        logger.info(f"Processed {file_path}: extracted {len(raw_pages_with_metadata)} raw sections, generated {len(all_chunks)} chunks.")
        return all_chunks

# Example usage (for testing this module directly)
if __name__ == '''__main__''':
    # Create dummy files for testing
    os.makedirs("./tmp_docs", exist_ok=True)
    with open("./tmp_docs/test.txt", "w") as f:
        f.write("This is a test text file.\nIt has multiple lines.")
    # You would need a dummy PDF, DOCX, XLSX to test other functions.
    # For example, create a dummy PDF using a library or a simple one manually.

    processor = DocumentProcessor()
    
    # Test TXT
    txt_chunks = processor.process_document("./tmp_docs/test.txt")
    print(f"TXT Chunks ({len(txt_chunks)}):")
    for chunk in txt_chunks:
        print(f"  ID: {chunk['chunk_id']}, Metadata: {chunk['metadata']['source']}, Text: {chunk['text'][:30]}...")

    # Clean up dummy files
    # os.remove("./tmp_docs/test.txt")
    # os.rmdir("./tmp_docs")
    print("\nPlease ensure you have PyMuPDF, python-docx, openpyxl, and langchain-text-splitters installed to run tests.")
    print(f"Supported file types from settings: {settings.RAG_SUPPORTED_FILE_TYPES}") 