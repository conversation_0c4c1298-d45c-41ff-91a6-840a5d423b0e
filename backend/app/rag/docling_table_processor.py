"""
Docling Table Processor for Vietnamese RAG System
Integrates Docling's superior table extraction with existing enhanced table processing
"""

import os
import json
import logging
import requests
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import tempfile
import re

# Import existing enhanced table processor
from enhanced_table_processor import TableChunk, ProcessedTable, EnhancedTableProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DoclingTable:
    """Enhanced table structure from Docling"""
    content: str
    markdown_content: str
    rows: List[List[str]]
    headers: List[str]
    page_number: int
    table_index: int
    confidence: float
    extraction_method: str = "docling"

class DoclingTableProcessor(EnhancedTableProcessor):
    """Docling table processor with Vietnamese content optimization"""
    
    def __init__(self, docling_server_url: str = "http://localhost:5001"):
        super().__init__()
        self.docling_server_url = docling_server_url
        self.docling_available = self._check_docling_server()
        
        # Vietnamese table patterns for enhanced recognition
        self.vietnamese_table_patterns = {
            'financial_headers': [
                'doanh thu', 'lợi nhuận', 'chi phí', 'tài sản', 'nợ phải trả',
                'vốn chủ sở hữu', 'tiền mặt', 'hàng tồn kho', 'khấu hao'
            ],
            'process_keywords': [
                'quy trình', 'bước', 'giai đoạn', 'hoạt động', 'công việc',
                'nhiệm vụ', 'trách nhiệm', 'thời gian', 'kết quả'
            ],
            'common_headers': [
                'stt', 'số thứ tự', 'tên', 'mô tả', 'số lượng', 'đơn vị',
                'giá trị', 'tỷ lệ', 'phần trăm', 'thời gian', 'ngày tháng'
            ]
        }
        
        if self.docling_available:
            logger.info(f"✅ Docling server available at {self.docling_server_url}")
        else:
            logger.warning(f"⚠️  Docling server not available at {self.docling_server_url}")
    
    def _check_docling_server(self) -> bool:
        """Check if Docling server is available"""
        try:
            response = requests.get(f"{self.docling_server_url}/health", timeout=5)
            return response.status_code == 200
        except:
            try:
                # Try alternative health check endpoint
                response = requests.get(f"{self.docling_server_url}/", timeout=5)
                return response.status_code in [200, 404]  # 404 might be normal for root
            except:
                return False
    
    def process_pdf_with_docling(self, file_path: str) -> List[TableChunk]:
        """Process PDF using Docling server"""
        if not self.docling_available:
            logger.info(f"🔄 Docling not available for {Path(file_path).name}, using enhanced processing")
            return self._process_pdf_tables(file_path)
        
        try:
            # Send document to Docling server
            logger.info(f"🔍 Processing {Path(file_path).name} with Docling...")
            
            with open(file_path, 'rb') as f:
                files = {'file': (Path(file_path).name, f, 'application/pdf')}
                response = requests.post(
                    f"{self.docling_server_url}/convert",
                    files=files,
                    timeout=120  # Generous timeout for large documents
                )
            
            if response.status_code != 200:
                logger.warning(f"⚠️  Docling server error {response.status_code}, falling back to enhanced processing")
                return self._process_pdf_tables(file_path)
            
            result = response.json()
            markdown_content = result.get('text', '')
            
            if not markdown_content:
                logger.warning("⚠️  Empty response from Docling, falling back to enhanced processing")
                return self._process_pdf_tables(file_path)
            
            # Extract tables from markdown
            table_chunks = self._extract_tables_from_docling_markdown(markdown_content, file_path)
            
            if table_chunks:
                logger.info(f"✅ Docling extracted {len(table_chunks)} table chunks from {Path(file_path).name}")
                return table_chunks
            else:
                logger.info(f"📄 No tables found by Docling in {Path(file_path).name}, trying enhanced processing")
                enhanced_tables = self._process_pdf_tables(file_path)
                if enhanced_tables:
                    logger.info(f"✅ Enhanced processing found {len(enhanced_tables)} table(s)")
                return enhanced_tables
            
        except Exception as e:
            logger.error(f"❌ Docling processing failed for {Path(file_path).name}: {e}")
            logger.info("🔄 Falling back to enhanced processing...")
            
            # Fallback to standard processing
            enhanced_tables = self._process_pdf_tables(file_path)
            if enhanced_tables:
                logger.info(f"✅ Enhanced processing fallback found {len(enhanced_tables)} table(s)")
            else:
                logger.warning("⚠️  Both Docling and enhanced processing found no tables")
            return enhanced_tables

    def _extract_tables_from_docling_markdown(self, markdown_content: str, file_path: str) -> List[TableChunk]:
        """Extract tables from Docling's markdown output"""
        table_chunks = []
        
        try:
            # Split content into sections
            sections = markdown_content.split('\n\n')
            table_index = 0
            
            for section_idx, section in enumerate(sections):
                if self._contains_markdown_table(section):
                    # Extract table from markdown
                    table_data = self._parse_markdown_table(section)
                    
                    if table_data and len(table_data) >= 2:  # At least header + 1 row
                        # Create DoclingTable
                        docling_table = DoclingTable(
                            content=section,
                            markdown_content=section,
                            rows=table_data[1:],  # Data rows
                            headers=table_data[0],  # Header row
                            page_number=1,  # Docling doesn't provide page info easily
                            table_index=table_index,
                            confidence=self._calculate_docling_table_confidence(table_data)
                        )
                        
                        if docling_table.confidence >= self.min_confidence:
                            # Convert to ProcessedTable format
                            processed_table = self._process_docling_table(docling_table)
                            
                            # Create table chunk
                            chunk = self._create_table_chunk_from_docling(
                                processed_table, docling_table, file_path
                            )
                            table_chunks.append(chunk)
                            table_index += 1
                            logger.info(f"✅ Processed Docling table {table_index} (confidence: {docling_table.confidence:.2f})")
            
            return table_chunks
            
        except Exception as e:
            logger.warning(f"Failed to extract tables from Docling markdown: {e}")
            return []

    def _contains_markdown_table(self, text: str) -> bool:
        """Check if text contains a markdown table"""
        lines = text.strip().split('\n')
        
        # Look for markdown table pattern
        for i, line in enumerate(lines):
            if '|' in line and i + 1 < len(lines):
                next_line = lines[i + 1]
                # Check for header separator (|---|---|)
                if '|' in next_line and ('-' in next_line or ':' in next_line):
                    return True
        
        return False

    def _parse_markdown_table(self, markdown_table: str) -> Optional[List[List[str]]]:
        """Parse markdown table into rows and columns"""
        try:
            lines = [line.strip() for line in markdown_table.strip().split('\n') if line.strip()]
            
            if len(lines) < 3:  # Need at least header, separator, and one data row
                return None
            
            table_data = []
            
            for i, line in enumerate(lines):
                if '|' not in line:
                    continue
                
                # Skip header separator line (|---|---|)
                if i == 1 and ('-' in line or ':' in line):
                    continue
                
                # Split by | and clean up
                cells = [cell.strip() for cell in line.split('|')]
                
                # Remove empty first/last cells (from leading/trailing |)
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]
                
                if cells:  # Only add non-empty rows
                    table_data.append(cells)
            
            return table_data if len(table_data) >= 2 else None
            
        except Exception as e:
            logger.warning(f"Failed to parse markdown table: {e}")
            return None

    def _calculate_docling_table_confidence(self, table_data: List[List[str]]) -> float:
        """Calculate confidence score for Docling table"""
        try:
            base_confidence = 0.7  # Base confidence for Docling extraction
            
            # Bonus for Vietnamese content
            vietnamese_bonus = 0.0
            all_text = ' '.join([' '.join(row) for row in table_data]).lower()
            
            for category, keywords in self.vietnamese_table_patterns.items():
                matches = sum(1 for keyword in keywords if keyword in all_text)
                if matches > 0:
                    vietnamese_bonus += 0.1 * min(matches / len(keywords), 0.3)
            
            # Bonus for table structure quality
            structure_bonus = 0.0
            if len(table_data) >= 3:  # Good number of rows
                structure_bonus += 0.1
            
            # Check for consistent column count
            if table_data:
                col_counts = [len(row) for row in table_data]
                if len(set(col_counts)) == 1:  # All rows have same column count
                    structure_bonus += 0.1
            
            # Bonus for numeric data (often indicates structured tables)
            numeric_bonus = 0.0
            total_cells = sum(len(row) for row in table_data)
            numeric_cells = 0
            
            for row in table_data:
                for cell in row:
                    if re.search(r'\d+', cell):
                        numeric_cells += 1
            
            if total_cells > 0:
                numeric_ratio = numeric_cells / total_cells
                numeric_bonus = min(numeric_ratio * 0.2, 0.2)
            
            final_confidence = min(base_confidence + vietnamese_bonus + structure_bonus + numeric_bonus, 1.0)
            return final_confidence
            
        except Exception as e:
            logger.warning(f"Failed to calculate confidence: {e}")
            return 0.7  # Default confidence

    def _process_docling_table(self, docling_table: DoclingTable) -> ProcessedTable:
        """Convert DoclingTable to ProcessedTable format"""
        try:
            # Generate structured content
            structured_content = self._format_table_for_rag(docling_table.headers, docling_table.rows)
            
            # Generate summary
            summary = self._generate_table_summary(docling_table.headers, docling_table.rows)
            
            # Determine table type
            table_type = self._classify_table_type(docling_table.headers, docling_table.rows)
            
            return ProcessedTable(
                raw_content=docling_table.content,
                structured_content=structured_content,
                summary=summary,
                headers=docling_table.headers,
                rows=docling_table.rows,
                table_type=table_type,
                confidence=docling_table.confidence,
                metadata={
                    'extraction_method': 'docling',
                    'table_index': docling_table.table_index,
                    'page_number': docling_table.page_number,
                    'markdown_content': docling_table.markdown_content
                }
            )
            
        except Exception as e:
            logger.warning(f"Failed to process Docling table: {e}")
            return None

    def _create_table_chunk_from_docling(self, processed_table: ProcessedTable, 
                                       docling_table: DoclingTable, 
                                       file_path: str) -> TableChunk:
        """Create TableChunk from processed Docling table"""
        try:
            chunk_id = f"docling_{Path(file_path).stem}_table_{docling_table.table_index}"
            
            return TableChunk(
                chunk_id=chunk_id,
                content=processed_table.structured_content,
                summary=processed_table.summary,
                table_type=processed_table.table_type,
                confidence_score=processed_table.confidence,
                source_location=f"Page {docling_table.page_number}, Table {docling_table.table_index + 1}",
                extraction_method="docling",
                raw_table_data=processed_table.rows,
                headers=processed_table.headers,
                metadata={
                    **processed_table.metadata,
                    'file_path': file_path,
                    'vietnamese_optimized': True,
                    'docling_processed': True
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to create table chunk: {e}")
            return None

    def analyze_document_with_docling(self, file_path: str) -> Dict[str, Any]:
        """Comprehensive document analysis using Docling"""
        if not self.docling_available:
            return {"error": "Docling server not available"}
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (Path(file_path).name, f, 'application/pdf')}
                response = requests.post(
                    f"{self.docling_server_url}/convert",
                    files=files,
                    timeout=60
                )
            
            if response.status_code != 200:
                return {"error": f"Docling server error: {response.status_code}"}
            
            result = response.json()
            markdown_content = result.get('text', '')
            
            # Analyze content
            sections = markdown_content.split('\n\n')
            table_sections = [s for s in sections if self._contains_markdown_table(s)]
            
            analysis = {
                'file_path': file_path,
                'extraction_method': 'docling',
                'total_sections': len(sections),
                'total_tables': len(table_sections),
                'content_length': len(markdown_content),
                'tables': [],
                'summary': {}
            }
            
            # Analyze each table
            for i, table_section in enumerate(table_sections):
                table_data = self._parse_markdown_table(table_section)
                if table_data:
                    confidence = self._calculate_docling_table_confidence(table_data)
                    analysis['tables'].append({
                        'table_index': i,
                        'headers': table_data[0] if table_data else [],
                        'row_count': len(table_data) - 1 if table_data else 0,
                        'column_count': len(table_data[0]) if table_data else 0,
                        'confidence': confidence
                    })
            
            # Generate summary
            analysis['summary'] = {
                'recommended_method': 'docling' if len(table_sections) > 0 else 'enhanced',
                'vietnamese_content_detected': self._detect_vietnamese_content(markdown_content),
                'table_quality': 'high' if any(t['confidence'] > 0.8 for t in analysis['tables']) else 'medium'
            }
            
            return analysis
            
        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}

    def _detect_vietnamese_content(self, content: str) -> bool:
        """Detect Vietnamese content in text"""
        vietnamese_chars = 'àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ'
        content_lower = content.lower()
        
        # Check for Vietnamese characters
        vietnamese_char_count = sum(1 for char in content_lower if char in vietnamese_chars)
        
        # Check for Vietnamese keywords
        vietnamese_keywords = ['việt nam', 'công ty', 'doanh nghiệp', 'báo cáo', 'tài chính']
        keyword_matches = sum(1 for keyword in vietnamese_keywords if keyword in content_lower)
        
        return vietnamese_char_count > 10 or keyword_matches > 0


def process_document_with_docling(file_path: str, 
                                docling_server_url: str = "http://localhost:5001") -> List[TableChunk]:
    """Main function to process document with Docling"""
    processor = DoclingTableProcessor(docling_server_url)
    
    if file_path.lower().endswith('.pdf'):
        return processor.process_pdf_with_docling(file_path)
    else:
        # Fallback to standard processing for non-PDF files
        return processor.process_document_tables(file_path)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        
        if not Path(file_path).exists():
            print(f"❌ File not found: {file_path}")
            sys.exit(1)
        
        print(f"🔍 Processing {file_path} with Docling...")
        
        # Analyze document
        processor = DoclingTableProcessor()
        analysis = processor.analyze_document_with_docling(file_path)
        
        print(f"📊 Analysis Results:")
        print(f"   Total tables: {analysis.get('total_tables', 0)}")
        print(f"   Total sections: {analysis.get('total_sections', 0)}")
        print(f"   Content length: {analysis.get('content_length', 0)}")
        
        # Process tables
        table_chunks = process_document_with_docling(file_path)
        print(f"✅ Extracted {len(table_chunks)} table chunks")
        
        # Show sample results
        for i, chunk in enumerate(table_chunks[:3]):
            print(f"\n📋 Table {i+1}:")
            print(f"   Type: {chunk.table_type}")
            print(f"   Confidence: {chunk.confidence_score:.2f}")
            print(f"   Location: {chunk.source_location}")
            print(f"   Content preview: {chunk.content[:200]}...")
    
    else:
        print("Usage: python docling_table_processor.py <file_path>")
        print("Example: python docling_table_processor.py knowledge_files/sample.pdf")