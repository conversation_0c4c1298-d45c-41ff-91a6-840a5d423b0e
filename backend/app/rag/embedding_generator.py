import logging
from typing import List
from core.config import settings
import asyncio

# Optional imports with fallbacks
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("sentence_transformers not available. Embedding generation will be disabled.")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("torch not available. Using CPU-only mode.")

logger = logging.getLogger(__name__)

class EmbeddingGenerator:
    _instance = None
    _model = None  # Will be SentenceTransformer if available
    _device: str | None = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(EmbeddingGenerator, cls).__new__(cls)
            # Initialization logic will be called only once
            cls._initialize_model()
        return cls._instance

    @classmethod
    def _initialize_model(cls):
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence_transformers not available. EmbeddingGenerator will not be available.")
            return
            
        if not settings.RAG_EMBEDDING_MODEL_NAME:
            logger.warning("RAG embedding model name is not configured. EmbeddingGenerator will not be available.")
            return

        # Determine device: use CUDA if available and PyTorch recognizes it, otherwise CPU
        if TORCH_AVAILABLE and torch.cuda.is_available():
            cls._device = "cuda"
        # elif TORCH_AVAILABLE and torch.backends.mps.is_available(): # For MacOS Metal Performance Shaders
        #     cls._device = "mps"
        else:
            cls._device = "cpu"
        
        logger.info(f"Initializing SentenceTransformer model: {settings.RAG_EMBEDDING_MODEL_NAME} on device: {cls._device}")
        try:
            cls._model = SentenceTransformer(settings.RAG_EMBEDDING_MODEL_NAME, device=cls._device)
            logger.info(f"SentenceTransformer model {settings.RAG_EMBEDDING_MODEL_NAME} loaded successfully on {cls._device}.")
        except Exception as e:
            cls._model = None # Ensure model is None if loading fails
            logger.error(f"Failed to load SentenceTransformer model {settings.RAG_EMBEDDING_MODEL_NAME}: {e}", exc_info=True)
            # raise RuntimeError(f"Could not initialize SentenceTransformer model: {e}") # Optionally re-raise

    @property
    def is_available(self) -> bool:
        """Check if the embedding model is loaded."""
        return self._model is not None

    async def generate_embeddings(self, texts: List[str]) -> List[List[float]] | None:
        if not self.is_available:
            logger.error("SentenceTransformer model is not available for generating embeddings.")
            return None
        if not texts:
            return []

        logger.info(f"Generating embeddings for {len(texts)} text chunks using {settings.RAG_EMBEDDING_MODEL_NAME} on {self._device}...")
        try:
            # sentence-transformers.encode is CPU-bound for the most part (even if model is on GPU, Python part can be heavy)
            # but can also be I/O bound if models need to be fetched/loaded, or if data transfer to GPU happens.
            # Running in a threadpool to be safe for async context.
            def _encode():
                # The model internally handles batching if you pass a list of sentences.
                embeddings = self._model.encode(texts, convert_to_tensor=False, show_progress_bar=False) # Get numpy arrays
                return [emb.tolist() for emb in embeddings] # Convert numpy arrays to lists of floats
            
            list_of_embeddings = await asyncio.to_thread(_encode)
            logger.info(f"Successfully generated {len(list_of_embeddings)} embeddings.")
            return list_of_embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}", exc_info=True)
            return None

    def get_embedding_dimension(self) -> int | None:
        if not self.is_available:
            logger.error("SentenceTransformer model is not available to get embedding dimension.")
            return None
        try:
            return self._model.get_sentence_embedding_dimension()
        except Exception as e:
            logger.error(f"Error getting embedding dimension: {e}", exc_info=True)
            return None

# For testing the client directly:
if __name__ == '''__main__''':
    async def test_embedding_generator():
        print("Attempting to initialize EmbeddingGenerator...")
        if not settings.RAG_EMBEDDING_MODEL_NAME:
            print("Please ensure RAG_EMBEDDING_MODEL_NAME is set in .env or settings.py")
            return
        try:
            generator = EmbeddingGenerator() # Initialization happens here
            if generator.is_available:
                print(f"EmbeddingGenerator initialized. Model: {settings.RAG_EMBEDDING_MODEL_NAME}, Device: {generator._device}")
                dim = generator.get_embedding_dimension()
                print(f"Embedding dimension: {dim}")

                test_sentences = [
                    "This is a test sentence.",
                    "FastAPI is a modern Python web framework.",
                    "RAG enhances LLMs with external knowledge."
                ]
                embeddings = await generator.generate_embeddings(test_sentences)
                if embeddings:
                    print(f"\nGenerated {len(embeddings)} embeddings:")
                    for i, emb in enumerate(embeddings):
                        print(f"  Sentence {i+1}: Dimension {len(emb)}, First 3 values: {emb[:3]}...")
                else:
                    print("Failed to generate embeddings.")
            else:
                print("EmbeddingGenerator is not available. Check logs for errors.")
        except Exception as e:
            print(f"An error occurred during EmbeddingGenerator test: {e}")
            import traceback
            traceback.print_exc()

    asyncio.run(test_embedding_generator()) 