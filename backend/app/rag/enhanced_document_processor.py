"""
Enhanced Document Processor with LLMSherpa Table Processing
Extends the existing document processor with advanced table extraction
"""

import logging
import os
import sys
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# Import existing document processor
from .document_processor import DocumentProcessor
from ..models.rag_models import DocumentMetadata

# Import LLMSherpa components
try:
    from .llmsherpa_table_processor import LLMSherpaTableProcessor
    from .integrate_llmsherpa_rag import LLMSherpaRAGIntegrator
    from .enhanced_table_processor import EnhancedTableProcessor
    LLMSHERPA_AVAILABLE = True
except ImportError as e:
    try:
        # Fallback to root level imports
        from llmsherpa_table_processor import LLMSherpaTableProcessor
        from integrate_llmsherpa_rag import LLMSherpaRAGIntegrator
        from enhanced_table_processor import EnhancedTableProcessor
        LLMSHERPA_AVAILABLE = True
    except ImportError as e2:
        LLMSHERPA_AVAILABLE = False
        logging.warning(f"LLMSherpa components not available: {e2}")

logger = logging.getLogger(__name__)

class EnhancedDocumentProcessor(DocumentProcessor):
    """Enhanced document processor with advanced table processing capabilities"""
    
    def __init__(self):
        super().__init__()
        self.table_processing_enabled = LLMSHERPA_AVAILABLE
        
        if self.table_processing_enabled:
            try:
                # Initialize table processors
                self.llmsherpa_processor = LLMSherpaTableProcessor()
                self.enhanced_processor = EnhancedTableProcessor()
                
                # Initialize RAG integrator for table storage
                self.rag_integrator = LLMSherpaRAGIntegrator()
                
                logger.info("✅ Enhanced table processing initialized")
                
            except Exception as e:
                logger.warning(f"⚠️  Failed to initialize table processing: {e}")
                self.table_processing_enabled = False
        else:
            logger.warning("⚠️  Table processing disabled - LLMSherpa not available")
    
    def process_document(self, file_path: str) -> List[Dict[str, Any]]:
        """Enhanced document processing with table extraction"""
        
        # Get standard document chunks
        standard_chunks = super().process_document(file_path)
        
        # If table processing is disabled, return standard chunks
        if not self.table_processing_enabled:
            return standard_chunks
        
        # Process tables if file is PDF (LLMSherpa works best with PDF)
        if file_path.lower().endswith('.pdf'):
            try:
                table_chunks = self._process_document_tables(file_path)
                
                if table_chunks:
                    logger.info(f"✅ Extracted {len(table_chunks)} table chunks from {Path(file_path).name}")
                    
                    # Convert table chunks to standard format
                    table_dicts = self._convert_table_chunks_to_dicts(table_chunks, file_path)
                    
                    # Combine with standard chunks
                    all_chunks = standard_chunks + table_dicts
                    
                    # Store table chunks in specialized database
                    self._store_table_chunks(table_chunks)
                    
                    return all_chunks
                else:
                    logger.info(f"📄 No tables found in {Path(file_path).name}")
                    
            except Exception as e:
                logger.error(f"❌ Table processing failed for {file_path}: {e}")
        
        return standard_chunks
    
    def _process_document_tables(self, file_path: str) -> List:
        """Process document tables using hybrid approach"""
        try:
            # Use hybrid processing for best results
            table_chunks = self.rag_integrator.process_document_hybrid(
                file_path, 
                force_method=None  # Use hybrid approach
            )
            return table_chunks
            
        except Exception as e:
            logger.error(f"❌ Hybrid table processing failed: {e}")
            
            # Fallback to LLMSherpa only
            try:
                table_chunks = self.llmsherpa_processor.process_pdf_with_llmsherpa(file_path)
                logger.info(f"✅ Fallback LLMSherpa processing successful")
                return table_chunks
            except Exception as e2:
                logger.error(f"❌ LLMSherpa fallback failed: {e2}")
                return []
    
    def _convert_table_chunks_to_dicts(self, table_chunks: List, file_path: str) -> List[Dict[str, Any]]:
        """Convert table chunks to standard document chunk format"""
        table_dicts = []
        
        for chunk in table_chunks:
            try:
                # Create metadata
                metadata = DocumentMetadata(
                    filename=os.path.basename(file_path),
                    source=os.path.basename(file_path),
                    page_number=chunk.metadata.get('page_number'),
                    chunk_type='table',
                    table_type=chunk.table_type,
                    confidence_score=chunk.confidence_score,
                    extraction_method=chunk.metadata.get('extraction_method', 'llmsherpa')
                )
                
                # Create enhanced content with table structure
                enhanced_content = self._create_enhanced_table_content(chunk)
                
                # Create chunk dictionary
                chunk_dict = {
                    'content': enhanced_content,
                    'metadata': metadata.__dict__,
                    'chunk_id': chunk.chunk_id,
                    'source_location': chunk.source_location,
                    'table_structure': chunk.table_structure,
                    'confidence_score': chunk.confidence_score
                }
                
                table_dicts.append(chunk_dict)
                
            except Exception as e:
                logger.warning(f"⚠️  Failed to convert table chunk: {e}")
                continue
        
        return table_dicts
    
    def _create_enhanced_table_content(self, chunk) -> str:
        """Create enhanced content for table chunks optimized for RAG and LLM querying"""
        
        # Base content
        content_parts = [
            f"🏷️ BẢNG DỮ LIỆU - {chunk.table_type.upper()}",
            f"📍 Vị trí: {chunk.source_location}",
            f"🎯 Độ tin cậy: {chunk.confidence_score:.2f}",
            ""
        ]
        
        # Add table structure if available
        if chunk.table_structure and chunk.table_structure.get('headers'):
            headers = chunk.table_structure['headers']
            content_parts.append(f"📊 Cột dữ liệu: {', '.join(headers[:5])}{'...' if len(headers) > 5 else ''}")
            content_parts.append("")
            
            # Attempt to create a structured markdown table if structured_content is available
            if 'structured_content' in chunk.table_structure and chunk.table_structure['structured_content']:
                content_parts.append("📋 BẢNG DỮ LIỆU CẤU TRÚC (Markdown):")
                content_parts.append(chunk.table_structure['structured_content'])
                content_parts.append("")
            elif 'rows' in chunk.table_structure and chunk.table_structure['rows']:
                # Build a simple markdown table from rows if structured_content isn't directly available
                content_parts.append("📋 BẢNG DỮ LIỆU CẤU TRÚC (Markdown):")
                # Create header row
                header_row = "| " + " | ".join(headers[:len(headers)]) + " |"
                content_parts.append(header_row)
                # Separator row
                separator_row = "| " + " | ".join(["---" for _ in headers[:len(headers)]]) + " |"
                content_parts.append(separator_row)
                # Data rows (limit to first 5 for brevity)
                for row in chunk.table_structure['rows'][:5]:
                    row_str = "| " + " | ".join(str(cell) for cell in row[:len(headers)]) + " |"
                    content_parts.append(row_str)
                if len(chunk.table_structure['rows']) > 5:
                    content_parts.append(f"... (Còn {len(chunk.table_structure['rows']) - 5} hàng khác)")
                content_parts.append("")
        
        # Add main content as fallback or additional context
        content_parts.append("📋 NỘI DUNG BẢNG (Dạng văn bản):")
        content_parts.append(chunk.content)
        
        # Add Vietnamese keywords for better search
        vietnamese_keywords = self._extract_vietnamese_keywords(chunk.content)
        if vietnamese_keywords:
            content_parts.append("")
            content_parts.append(f"🔍 Từ khóa: {', '.join(vietnamese_keywords)}")
        
        return "\n".join(content_parts)
    
    def _extract_vietnamese_keywords(self, content: str) -> List[str]:
        """Extract Vietnamese keywords from table content"""
        vietnamese_patterns = [
            'doanh thu', 'bán hàng', 'tài sản', 'cố định', 'chi phí', 'lợi nhuận',
            'thuế', 'vat', 'phí', 'vnđ', 'đồng', 'triệu', 'tỷ', 'stt', 'số tt',
            'tên', 'họ tên', 'số lượng', 'đơn giá', 'thành tiền', 'tổng cộng',
            'ngày', 'mã', 'ghi chú', 'mô tả', 'diễn giải', 'nội dung'
        ]
        
        content_lower = content.lower()
        found_keywords = []
        
        for pattern in vietnamese_patterns:
            if pattern in content_lower:
                found_keywords.append(pattern)
        
        return found_keywords[:8]  # Limit to 8 keywords
    
    def _store_table_chunks(self, table_chunks: List):
        """Store table chunks in specialized database"""
        try:
            # Use the RAG integrator to store chunks
            self.rag_integrator._store_chunks(table_chunks)
            logger.info(f"💾 Stored {len(table_chunks)} table chunks in database")
            
        except Exception as e:
            logger.error(f"❌ Failed to store table chunks: {e}")
    
    def get_table_processing_stats(self) -> Dict[str, Any]:
        """Get table processing statistics"""
        if not self.table_processing_enabled:
            return {"table_processing": "disabled"}
        
        try:
            stats = self.rag_integrator.get_processing_stats()
            stats['table_processing'] = 'enabled'
            stats['llmsherpa_available'] = True
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get table stats: {e}")
            return {"table_processing": "error", "error": str(e)}
    
    def search_tables(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search specifically in table content"""
        if not self.table_processing_enabled:
            return []
        
        try:
            results = self.rag_integrator.search_tables(query, limit=limit)
            return results
            
        except Exception as e:
            logger.error(f"❌ Table search failed: {e}")
            return []

# For backward compatibility
def create_enhanced_document_processor() -> EnhancedDocumentProcessor:
    """Factory function to create enhanced document processor"""
    return EnhancedDocumentProcessor()

# Test the processor
if __name__ == "__main__":
    processor = EnhancedDocumentProcessor()
    
    print("🧪 Enhanced Document Processor Test")
    print("=" * 40)
    print(f"Table processing enabled: {processor.table_processing_enabled}")
    
    if processor.table_processing_enabled:
        stats = processor.get_table_processing_stats()
        print(f"Processing stats: {stats}")
        
        # Test with sample file if available
        test_file = "sample_vietnamese_tables.pdf"
        if os.path.exists(test_file):
            print(f"\n📄 Testing with: {test_file}")
            chunks = processor.process_document(test_file)
            print(f"✅ Processed {len(chunks)} chunks")
            
            # Show table chunks
            table_chunks = [c for c in chunks if c.get('metadata', {}).get('chunk_type') == 'table']
            print(f"📊 Table chunks: {len(table_chunks)}")
            
        else:
            print(f"⚠️  Test file {test_file} not found")
    else:
        print("❌ Table processing not available")
