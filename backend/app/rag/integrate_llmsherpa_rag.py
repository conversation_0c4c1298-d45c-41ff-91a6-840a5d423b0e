#!/usr/bin/env python3
"""
Integration script for LLMSherpa with existing Vietnamese RAG system
Combines LLMSherpa table processing with enhanced table RAG
"""

import os
import sys
import sqlite3
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import existing components
try:
    from enhanced_table_processor import TableChunk, EnhancedTableProcessor
    from llmsherpa_table_processor import LLMSherpaTableProcessor, process_document_with_llmsherpa
    PROCESSORS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Import warning: {e}")
    PROCESSORS_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMSherpaRAGIntegrator:
    """Integrates LLMSherpa with existing RAG system"""
    
    def __init__(self, db_path: str = "enhanced_tables.db", 
                 nlm_ingestor_url: str = "http://localhost:5010/api/parseDocument?renderFormat=all"):
        self.db_path = db_path
        self.nlm_ingestor_url = nlm_ingestor_url
        
        # Initialize processors
        if PROCESSORS_AVAILABLE:
            self.enhanced_processor = EnhancedTableProcessor()
            self.llmsherpa_processor = LLMSherpaTableProcessor(nlm_ingestor_url)
        else:
            logger.warning("Processors not available - limited functionality")
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize enhanced database with LLMSherpa support"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create enhanced table chunks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS llmsherpa_table_chunks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chunk_id TEXT UNIQUE NOT NULL,
                    file_path TEXT NOT NULL,
                    content TEXT NOT NULL,
                    table_structure TEXT,  -- JSON
                    metadata TEXT,         -- JSON
                    confidence_score REAL,
                    table_type TEXT,
                    extraction_method TEXT,
                    source_location TEXT,
                    html_content TEXT,
                    bbox TEXT,            -- JSON for bounding box
                    page_number INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create index for faster searches
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_llmsherpa_file_path 
                ON llmsherpa_table_chunks(file_path)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_llmsherpa_table_type 
                ON llmsherpa_table_chunks(table_type)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_llmsherpa_extraction_method 
                ON llmsherpa_table_chunks(extraction_method)
            ''')
            
            # Create comparison results table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processing_comparisons (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    enhanced_chunks INTEGER,
                    llmsherpa_chunks INTEGER,
                    enhanced_time REAL,
                    llmsherpa_time REAL,
                    recommended_method TEXT,
                    comparison_data TEXT,  -- JSON
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Database initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
    
    def process_document_hybrid(self, file_path: str, 
                              force_method: Optional[str] = None) -> List[TableChunk]:
        """Process document using hybrid approach (LLMSherpa + Enhanced)"""
        
        if not PROCESSORS_AVAILABLE:
            logger.error("Processors not available")
            return []
        
        logger.info(f"🔍 Processing document: {Path(file_path).name}")
        
        all_chunks = []
        processing_stats = {
            'file_path': file_path,
            'enhanced_chunks': 0,
            'llmsherpa_chunks': 0,
            'enhanced_time': 0,
            'llmsherpa_time': 0,
            'total_chunks': 0,
            'recommended_method': 'hybrid'
        }
        
        # Method 1: LLMSherpa (primary for PDFs)
        if force_method != 'enhanced' and file_path.lower().endswith('.pdf'):
            try:
                import time
                start_time = time.time()
                
                llmsherpa_chunks = self.llmsherpa_processor.process_pdf_with_llmsherpa(file_path)
                
                processing_stats['llmsherpa_time'] = time.time() - start_time
                processing_stats['llmsherpa_chunks'] = len(llmsherpa_chunks)
                
                all_chunks.extend(llmsherpa_chunks)
                logger.info(f"✅ LLMSherpa extracted {len(llmsherpa_chunks)} table chunks")
                
            except Exception as e:
                logger.warning(f"⚠️  LLMSherpa processing failed: {e}")
        
        # Method 2: Enhanced Table Processor (fallback or supplement)
        if force_method != 'llmsherpa':
            try:
                import time
                start_time = time.time()
                
                enhanced_chunks = self.enhanced_processor.process_document_tables(file_path)
                
                processing_stats['enhanced_time'] = time.time() - start_time
                processing_stats['enhanced_chunks'] = len(enhanced_chunks)
                
                # Add enhanced chunks that don't overlap with LLMSherpa
                unique_enhanced = self._filter_unique_chunks(enhanced_chunks, all_chunks)
                all_chunks.extend(unique_enhanced)
                
                logger.info(f"✅ Enhanced processor extracted {len(enhanced_chunks)} chunks ({len(unique_enhanced)} unique)")
                
            except Exception as e:
                logger.warning(f"⚠️  Enhanced processing failed: {e}")
        
        # Deduplicate and rank all chunks
        final_chunks = self._deduplicate_and_rank_chunks(all_chunks)
        processing_stats['total_chunks'] = len(final_chunks)
        
        # Store chunks in database
        self._store_chunks(final_chunks)
        
        # Store processing comparison
        self._store_processing_stats(processing_stats)
        
        logger.info(f"🎯 Final result: {len(final_chunks)} unique table chunks")
        
        return final_chunks
    
    def _filter_unique_chunks(self, new_chunks: List[TableChunk], 
                            existing_chunks: List[TableChunk]) -> List[TableChunk]:
        """Filter out chunks that are similar to existing ones"""
        unique_chunks = []
        
        for new_chunk in new_chunks:
            is_unique = True
            
            for existing_chunk in existing_chunks:
                # Check similarity based on content and location
                if self._are_chunks_similar(new_chunk, existing_chunk):
                    is_unique = False
                    break
            
            if is_unique:
                unique_chunks.append(new_chunk)
        
        return unique_chunks
    
    def _are_chunks_similar(self, chunk1: TableChunk, chunk2: TableChunk, 
                          similarity_threshold: float = 0.8) -> bool:
        """Check if two chunks are similar"""
        
        # Compare content similarity
        content1 = chunk1.content.lower().replace(' ', '')
        content2 = chunk2.content.lower().replace(' ', '')
        
        if len(content1) == 0 or len(content2) == 0:
            return False
        
        # Simple similarity check
        shorter = min(len(content1), len(content2))
        longer = max(len(content1), len(content2))
        
        if shorter / longer < 0.5:  # Very different lengths
            return False
        
        # Check common characters
        common_chars = sum(1 for c in content1 if c in content2)
        similarity = common_chars / max(len(content1), len(content2))
        
        return similarity >= similarity_threshold
    
    def _deduplicate_and_rank_chunks(self, chunks: List[TableChunk]) -> List[TableChunk]:
        """Deduplicate and rank chunks by confidence and method"""
        
        if not chunks:
            return []
        
        # Group similar chunks
        groups = []
        for chunk in chunks:
            added_to_group = False
            
            for group in groups:
                if self._are_chunks_similar(chunk, group[0]):
                    group.append(chunk)
                    added_to_group = True
                    break
            
            if not added_to_group:
                groups.append([chunk])
        
        # Select best chunk from each group
        final_chunks = []
        for group in groups:
            # Sort by confidence and prefer LLMSherpa method
            best_chunk = max(group, key=lambda c: (
                c.confidence_score,
                1 if c.metadata.get('extraction_method') == 'llmsherpa' else 0
            ))
            final_chunks.append(best_chunk)
        
        # Sort final chunks by confidence
        final_chunks.sort(key=lambda c: c.confidence_score, reverse=True)
        
        return final_chunks
    
    def _store_chunks(self, chunks: List[TableChunk]):
        """Store chunks in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for chunk in chunks:
                # Prepare data
                table_structure = json.dumps(chunk.table_structure, ensure_ascii=False)
                metadata = json.dumps(chunk.metadata, ensure_ascii=False)
                bbox = json.dumps(chunk.metadata.get('bbox')) if chunk.metadata.get('bbox') else None
                
                # Insert or update
                cursor.execute('''
                    INSERT OR REPLACE INTO llmsherpa_table_chunks 
                    (chunk_id, file_path, content, table_structure, metadata, 
                     confidence_score, table_type, extraction_method, source_location,
                     html_content, bbox, page_number, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    chunk.chunk_id,
                    chunk.metadata.get('file_path', ''),
                    chunk.content,
                    table_structure,
                    metadata,
                    chunk.confidence_score,
                    chunk.table_type,
                    chunk.metadata.get('extraction_method', 'unknown'),
                    chunk.source_location,
                    chunk.metadata.get('html_content', ''),
                    bbox,
                    chunk.metadata.get('page_number'),
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"💾 Stored {len(chunks)} chunks in database")
            
        except Exception as e:
            logger.error(f"❌ Failed to store chunks: {e}")
    
    def _store_processing_stats(self, stats: Dict[str, Any]):
        """Store processing comparison statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO processing_comparisons 
                (file_path, enhanced_chunks, llmsherpa_chunks, enhanced_time, 
                 llmsherpa_time, recommended_method, comparison_data)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                stats['file_path'],
                stats['enhanced_chunks'],
                stats['llmsherpa_chunks'],
                stats['enhanced_time'],
                stats['llmsherpa_time'],
                stats['recommended_method'],
                json.dumps(stats, ensure_ascii=False)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Failed to store processing stats: {e}")
    
    def search_tables(self, query: str, table_type: Optional[str] = None,
                     min_confidence: float = 0.6, limit: int = 10) -> List[Dict[str, Any]]:
        """Search tables in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Build query
            sql = '''
                SELECT chunk_id, file_path, content, table_type, confidence_score,
                       extraction_method, source_location, html_content, page_number
                FROM llmsherpa_table_chunks 
                WHERE confidence_score >= ? AND content LIKE ?
            '''
            params = [min_confidence, f'%{query}%']
            
            if table_type:
                sql += ' AND table_type = ?'
                params.append(table_type)
            
            sql += ' ORDER BY confidence_score DESC LIMIT ?'
            params.append(limit)
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            conn.close()
            
            # Format results
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'chunk_id': row[0],
                    'file_path': row[1],
                    'content': row[2],
                    'table_type': row[3],
                    'confidence_score': row[4],
                    'extraction_method': row[5],
                    'source_location': row[6],
                    'html_content': row[7],
                    'page_number': row[8]
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ Search failed: {e}")
            return []
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get overall stats
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_chunks,
                    COUNT(DISTINCT file_path) as total_files,
                    AVG(confidence_score) as avg_confidence,
                    COUNT(CASE WHEN extraction_method = 'llmsherpa' THEN 1 END) as llmsherpa_chunks,
                    COUNT(CASE WHEN extraction_method != 'llmsherpa' THEN 1 END) as enhanced_chunks
                FROM llmsherpa_table_chunks
            ''')
            
            overall_stats = cursor.fetchone()
            
            # Get table type distribution
            cursor.execute('''
                SELECT table_type, COUNT(*) as count
                FROM llmsherpa_table_chunks
                GROUP BY table_type
                ORDER BY count DESC
            ''')
            
            table_types = cursor.fetchall()
            
            # Get processing comparison stats
            cursor.execute('''
                SELECT 
                    AVG(enhanced_time) as avg_enhanced_time,
                    AVG(llmsherpa_time) as avg_llmsherpa_time,
                    AVG(enhanced_chunks) as avg_enhanced_chunks,
                    AVG(llmsherpa_chunks) as avg_llmsherpa_chunks
                FROM processing_comparisons
            ''')
            
            comparison_stats = cursor.fetchone()
            
            conn.close()
            
            return {
                'overall': {
                    'total_chunks': overall_stats[0],
                    'total_files': overall_stats[1],
                    'avg_confidence': overall_stats[2],
                    'llmsherpa_chunks': overall_stats[3],
                    'enhanced_chunks': overall_stats[4]
                },
                'table_types': [{'type': row[0], 'count': row[1]} for row in table_types],
                'processing_comparison': {
                    'avg_enhanced_time': comparison_stats[0] or 0,
                    'avg_llmsherpa_time': comparison_stats[1] or 0,
                    'avg_enhanced_chunks': comparison_stats[2] or 0,
                    'avg_llmsherpa_chunks': comparison_stats[3] or 0
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get stats: {e}")
            return {}
    
    def process_knowledge_files(self, knowledge_dir: str = "knowledge_files"):
        """Process all files in knowledge directory"""
        knowledge_path = Path(knowledge_dir)
        
        if not knowledge_path.exists():
            logger.warning(f"Knowledge directory not found: {knowledge_dir}")
            return
        
        # Find all supported files
        supported_extensions = ['.pdf', '.docx', '.doc', '.xlsx', '.xls']
        files_to_process = []
        
        for ext in supported_extensions:
            files_to_process.extend(knowledge_path.glob(f'*{ext}'))
        
        if not files_to_process:
            logger.warning(f"No supported files found in {knowledge_dir}")
            return
        
        logger.info(f"📁 Found {len(files_to_process)} files to process")
        
        # Process each file
        total_chunks = 0
        for file_path in files_to_process:
            try:
                logger.info(f"📄 Processing: {file_path.name}")
                chunks = self.process_document_hybrid(str(file_path))
                total_chunks += len(chunks)
                logger.info(f"✅ Extracted {len(chunks)} chunks from {file_path.name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process {file_path.name}: {e}")
        
        logger.info(f"🎯 Total chunks extracted: {total_chunks}")
        
        # Show final statistics
        stats = self.get_processing_stats()
        if stats:
            print("\n📊 Processing Summary:")
            print(f"   Total chunks: {stats['overall']['total_chunks']}")
            print(f"   Total files: {stats['overall']['total_files']}")
            print(f"   Average confidence: {stats['overall']['avg_confidence']:.3f}")
            print(f"   LLMSherpa chunks: {stats['overall']['llmsherpa_chunks']}")
            print(f"   Enhanced chunks: {stats['overall']['enhanced_chunks']}")

def main():
    """Main function for command line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LLMSherpa RAG Integration")
    parser.add_argument('command', choices=['process', 'search', 'stats', 'process-all'],
                       help='Command to execute')
    parser.add_argument('--file', '-f', help='File to process')
    parser.add_argument('--query', '-q', help='Search query')
    parser.add_argument('--table-type', '-t', help='Filter by table type')
    parser.add_argument('--confidence', '-c', type=float, default=0.6,
                       help='Minimum confidence score')
    parser.add_argument('--method', '-m', choices=['llmsherpa', 'enhanced', 'hybrid'],
                       default='hybrid', help='Processing method')
    parser.add_argument('--knowledge-dir', '-d', default='knowledge_files',
                       help='Knowledge files directory')
    
    args = parser.parse_args()
    
    # Initialize integrator
    integrator = LLMSherpaRAGIntegrator()
    
    if args.command == 'process':
        if not args.file:
            print("❌ File path required for process command")
            sys.exit(1)
        
        if not Path(args.file).exists():
            print(f"❌ File not found: {args.file}")
            sys.exit(1)
        
        force_method = args.method if args.method != 'hybrid' else None
        chunks = integrator.process_document_hybrid(args.file, force_method)
        
        print(f"✅ Processed {args.file}")
        print(f"📊 Extracted {len(chunks)} table chunks")
        
        for i, chunk in enumerate(chunks[:3]):
            print(f"\n📋 Table {i+1}:")
            print(f"   Type: {chunk.table_type}")
            print(f"   Confidence: {chunk.confidence_score:.3f}")
            print(f"   Method: {chunk.metadata.get('extraction_method', 'unknown')}")
            print(f"   Content: {chunk.content[:150]}...")
    
    elif args.command == 'search':
        if not args.query:
            print("❌ Query required for search command")
            sys.exit(1)
        
        results = integrator.search_tables(
            args.query, args.table_type, args.confidence
        )
        
        print(f"🔍 Search results for: '{args.query}'")
        print(f"📊 Found {len(results)} matching tables")
        
        for i, result in enumerate(results):
            print(f"\n📋 Result {i+1}:")
            print(f"   File: {Path(result['file_path']).name}")
            print(f"   Type: {result['table_type']}")
            print(f"   Confidence: {result['confidence_score']:.3f}")
            print(f"   Method: {result['extraction_method']}")
            print(f"   Content: {result['content'][:150]}...")
    
    elif args.command == 'stats':
        stats = integrator.get_processing_stats()
        
        if stats:
            print("📊 Processing Statistics:")
            print(f"   Total chunks: {stats['overall']['total_chunks']}")
            print(f"   Total files: {stats['overall']['total_files']}")
            print(f"   Average confidence: {stats['overall']['avg_confidence']:.3f}")
            print(f"   LLMSherpa chunks: {stats['overall']['llmsherpa_chunks']}")
            print(f"   Enhanced chunks: {stats['overall']['enhanced_chunks']}")
            
            print(f"\n📋 Table Types:")
            for table_type in stats['table_types']:
                print(f"   {table_type['type']}: {table_type['count']}")
            
            comp = stats['processing_comparison']
            print(f"\n⏱️  Processing Comparison:")
            print(f"   Avg Enhanced time: {comp['avg_enhanced_time']:.2f}s")
            print(f"   Avg LLMSherpa time: {comp['avg_llmsherpa_time']:.2f}s")
            print(f"   Avg Enhanced chunks: {comp['avg_enhanced_chunks']:.1f}")
            print(f"   Avg LLMSherpa chunks: {comp['avg_llmsherpa_chunks']:.1f}")
        else:
            print("❌ No statistics available")
    
    elif args.command == 'process-all':
        integrator.process_knowledge_files(args.knowledge_dir)

if __name__ == "__main__":
    main()