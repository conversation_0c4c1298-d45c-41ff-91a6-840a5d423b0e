"""
Text Table Processor for Vietnamese RAG System
Specialized for extracting tables from .txt files containing Vietnamese business documents
"""

import re
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TextTableChunk:
    content: str
    table_structure: Dict[str, Any]
    metadata: Dict[str, Any]
    chunk_id: str
    confidence_score: float
    source_location: str
    table_type: str

class TextTableProcessor:
    """Processor for extracting tables from text files"""
    
    def __init__(self):
        self.min_confidence = 0.6
        
        # Vietnamese table patterns
        self.table_patterns = {
            'header_indicators': [
                'STT', 'Số TT', 'TT', 'Họ và tên', 'Họ tên', 'Tên',
                'Mã', 'Code', 'ID', 'Số', 'Ngày', 'Tháng', 'Năm',
                'Đơn giá', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> lượng', 'SL', '<PERSON>h<PERSON><PERSON> tiền',
                '<PERSON><PERSON> chú', '<PERSON>ứ<PERSON> vụ', '<PERSON><PERSON> phận', '<PERSON>òng ban'
            ],
            'table_separators': [
                '|', '│', '┃', '┊', '┋', '┌', '┐', '└', '┘',
                '├', '┤', '┬', '┴', '┼', '─', '━', '═', '╤', '╧',
                '╟', '╢', '╥', '╨', '╫', '╪', '╬'
            ],
            'alignment_patterns': [
                r'^\s*\|\s*.*\s*\|\s*$',  # |  content  |
                r'^\s*.*\s+.*\s+.*$',     # column1  column2  column3
                r'^\s*\d+\s+.*$',         # 1  content
                r'^\s*[A-Z]\s+.*$'        # A  content
            ]
        }
        
        # Vietnamese form patterns
        self.form_patterns = {
            'bảng_chấm_công': r'BẢNG CHẤM CÔNG',
            'bảng_thanh_toán': r'BẢNG THANH TOÁN',
            'bảng_lương': r'BẢNG.*LƯƠNG',
            'bảng_thưởng': r'BẢNG.*THƯỞNG',
            'danh_sách': r'DANH SÁCH',
            'quy_trình': r'QUY TRÌNH'
        }
    
    def process_text_file(self, file_path: str) -> List[TextTableChunk]:
        """Main entry point for processing text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all potential table sections
            table_sections = self._identify_table_sections(content)
            
            # Process each section
            table_chunks = []
            for i, section in enumerate(table_sections):
                chunk = self._process_table_section(section, file_path, i)
                if chunk and chunk.confidence_score >= self.min_confidence:
                    table_chunks.append(chunk)
            
            logger.info(f"✅ Extracted {len(table_chunks)} tables from {Path(file_path).name}")
            return table_chunks
            
        except Exception as e:
            logger.error(f"Error processing text file {file_path}: {e}")
            return []
    
    def _identify_table_sections(self, content: str) -> List[Dict[str, Any]]:
        """Identify potential table sections in text"""
        sections = []
        lines = content.split('\n')
        
        # Look for table headers/titles
        for i, line in enumerate(lines):
            # Check for table title patterns
            for pattern_name, pattern in self.form_patterns.items():
                if re.search(pattern, line, re.IGNORECASE):
                    # Found a table title, extract the surrounding content
                    section = self._extract_table_section(lines, i, pattern_name)
                    if section:
                        sections.append(section)
                        break
        
        # Look for structured data patterns
        structured_sections = self._find_structured_sections(lines)
        sections.extend(structured_sections)
        
        return sections
    
    def _extract_table_section(self, lines: List[str], title_index: int, table_type: str) -> Optional[Dict[str, Any]]:
        """Extract table section starting from title"""
        
        # Look ahead for table content
        start_index = title_index
        end_index = len(lines)
        
        # Find the actual table start (usually a few lines after title)
        table_start = None
        for i in range(title_index + 1, min(title_index + 10, len(lines))):
            if self._looks_like_table_header(lines[i]):
                table_start = i
                break
        
        if not table_start:
            return None
        
        # Find table end (empty line or next section)
        table_end = len(lines)
        for i in range(table_start + 1, len(lines)):
            if (not lines[i].strip() or 
                self._looks_like_new_section(lines[i]) or
                i - table_start > 50):  # Max 50 lines per table
                table_end = i
                break
        
        # Extract table content
        table_lines = lines[title_index:table_end]
        
        return {
            'title_line': lines[title_index],
            'table_lines': table_lines,
            'start_line': title_index,
            'end_line': table_end,
            'table_type': table_type,
            'content': '\n'.join(table_lines)
        }
    
    def _find_structured_sections(self, lines: List[str]) -> List[Dict[str, Any]]:
        """Find sections with structured data patterns"""
        sections = []
        
        i = 0
        while i < len(lines):
            if self._looks_like_table_header(lines[i]):
                # Found potential table start
                start = i
                
                # Find table end
                end = i + 1
                consecutive_table_lines = 1
                
                while end < len(lines) and consecutive_table_lines > 0:
                    if self._looks_like_table_row(lines[end]):
                        consecutive_table_lines += 1
                    elif lines[end].strip() == '':
                        consecutive_table_lines -= 1
                    else:
                        break
                    end += 1
                
                if end - start >= 3:  # At least header + 2 rows
                    sections.append({
                        'title_line': f'Structured Table {len(sections) + 1}',
                        'table_lines': lines[start:end],
                        'start_line': start,
                        'end_line': end,
                        'table_type': 'structured_data',
                        'content': '\n'.join(lines[start:end])
                    })
                
                i = end
            else:
                i += 1
        
        return sections
    
    def _looks_like_table_header(self, line: str) -> bool:
        """Check if line looks like a table header"""
        line = line.strip()
        if not line:
            return False
        
        # Check for header indicators
        for indicator in self.table_patterns['header_indicators']:
            if indicator in line:
                return True
        
        # Check for column separators
        separator_count = sum(1 for sep in self.table_patterns['table_separators'] if sep in line)
        if separator_count >= 2:
            return True
        
        # Check for multiple words that could be column headers
        words = line.split()
        if len(words) >= 3 and any(word in self.table_patterns['header_indicators'] for word in words):
            return True
        
        return False
    
    def _looks_like_table_row(self, line: str) -> bool:
        """Check if line looks like a table row"""
        line = line.strip()
        if not line:
            return False
        
        # Check alignment patterns
        for pattern in self.table_patterns['alignment_patterns']:
            if re.match(pattern, line):
                return True
        
        # Check for separators
        separator_count = sum(1 for sep in self.table_patterns['table_separators'] if sep in line)
        if separator_count >= 1:
            return True
        
        # Check for structured data (number + text pattern)
        if re.match(r'^\s*\d+\s+.+', line):
            return True
        
        return False
    
    def _looks_like_new_section(self, line: str) -> bool:
        """Check if line starts a new section"""
        line = line.strip().upper()
        
        section_indicators = [
            'PHẦN', 'CHƯƠNG', 'MỤC', 'ĐIỀU', 'KHOẢN',
            'BẢNG', 'DANH SÁCH', 'QUY TRÌNH', 'THỦ TỤC'
        ]
        
        return any(indicator in line for indicator in section_indicators)
    
    def _process_table_section(self, section: Dict[str, Any], file_path: str, section_index: int) -> Optional[TextTableChunk]:
        """Process a table section into a TableChunk"""
        
        try:
            # Parse table structure
            table_data = self._parse_table_structure(section['table_lines'])
            
            if not table_data or len(table_data['rows']) < 1:
                return None
            
            # Calculate confidence
            confidence = self._calculate_confidence(table_data, section)
            
            # Create structured content
            structured_content = self._create_structured_content(table_data, section)
            
            # Create metadata
            metadata = {
                'file_path': file_path,
                'section_index': section_index,
                'table_type': section['table_type'],
                'start_line': section['start_line'],
                'end_line': section['end_line'],
                'headers': table_data.get('headers', []),
                'row_count': len(table_data['rows']),
                'column_count': len(table_data.get('headers', [])),
                'vietnamese_content': True,
                'confidence_factors': {
                    'structure_quality': confidence,
                    'header_quality': len(table_data.get('headers', [])) / 10,
                    'vietnamese_indicators': 1.0 if self._has_vietnamese_content(section['content']) else 0.0
                }
            }
            
            # Create chunk
            chunk = TextTableChunk(
                content=structured_content,
                table_structure=table_data,
                metadata=metadata,
                chunk_id=f"text_table_{section_index}_{Path(file_path).stem}",
                confidence_score=confidence,
                source_location=f"{Path(file_path).name}#lines_{section['start_line']}-{section['end_line']}",
                table_type=section['table_type']
            )
            
            return chunk
            
        except Exception as e:
            logger.error(f"Error processing table section: {e}")
            return None
    
    def _parse_table_structure(self, lines: List[str]) -> Optional[Dict[str, Any]]:
        """Parse table structure from lines"""
        
        if not lines:
            return None
        
        # Find header row
        header_index = None
        for i, line in enumerate(lines):
            if self._looks_like_table_header(line):
                header_index = i
                break
        
        if header_index is None:
            return None
        
        # Extract headers
        header_line = lines[header_index].strip()
        headers = self._parse_table_row(header_line)
        
        # Extract data rows
        rows = []
        for i in range(header_index + 1, len(lines)):
            line = lines[i].strip()
            if self._looks_like_table_row(line):
                row = self._parse_table_row(line)
                if row:
                    rows.append(row)
        
        return {
            'headers': headers,
            'rows': rows,
            'raw_lines': lines
        }
    
    def _parse_table_row(self, line: str) -> List[str]:
        """Parse a single table row"""
        
        # Method 1: Split by separators
        for sep in ['|', '│', '┃']:
            if sep in line:
                parts = [part.strip() for part in line.split(sep) if part.strip()]
                if len(parts) >= 2:
                    return parts
        
        # Method 2: Split by multiple spaces
        parts = re.split(r'\s{2,}', line.strip())
        if len(parts) >= 2:
            return parts
        
        # Method 3: Try to identify columns by patterns
        # Look for number + text pattern
        match = re.match(r'^(\d+)\s+(.+)', line)
        if match:
            return [match.group(1), match.group(2)]
        
        # Fallback: return as single column
        return [line.strip()] if line.strip() else []
    
    def _calculate_confidence(self, table_data: Dict[str, Any], section: Dict[str, Any]) -> float:
        """Calculate confidence score for the table"""
        
        score = 0.0
        factors = []
        
        # Header quality (0-0.3)
        if table_data.get('headers'):
            header_score = min(len(table_data['headers']) / 5, 0.3)
            score += header_score
            factors.append(f"headers: {header_score:.2f}")
        
        # Row consistency (0-0.3)
        rows = table_data.get('rows', [])
        if rows:
            expected_cols = len(table_data.get('headers', []))
            consistent_rows = sum(1 for row in rows if len(row) == expected_cols)
            consistency_score = (consistent_rows / len(rows)) * 0.3
            score += consistency_score
            factors.append(f"consistency: {consistency_score:.2f}")
        
        # Vietnamese content (0-0.2)
        if self._has_vietnamese_content(section['content']):
            score += 0.2
            factors.append("vietnamese: 0.20")
        
        # Table type recognition (0-0.2)
        if section['table_type'] != 'structured_data':
            score += 0.2
            factors.append("type_recognized: 0.20")
        
        logger.debug(f"Confidence calculation: {' + '.join(factors)} = {score:.2f}")
        return min(score, 1.0)
    
    def _create_structured_content(self, table_data: Dict[str, Any], section: Dict[str, Any]) -> str:
        """Create structured content for RAG"""
        
        content_parts = []
        
        # Add title
        content_parts.append(f"BẢNG DỮ LIỆU: {section['title_line']}")
        content_parts.append(f"Loại: {section['table_type']}")
        content_parts.append("")
        
        # Add table in markdown format
        headers = table_data.get('headers', [])
        rows = table_data.get('rows', [])
        
        if headers and rows:
            # Create markdown table
            content_parts.append("| " + " | ".join(headers) + " |")
            content_parts.append("| " + " | ".join(["---"] * len(headers)) + " |")
            
            for row in rows:
                # Pad row to match header count
                padded_row = row + [""] * (len(headers) - len(row))
                content_parts.append("| " + " | ".join(padded_row[:len(headers)]) + " |")
        
        content_parts.append("")
        
        # Add summary
        summary = f"Bảng có {len(rows)} dòng dữ liệu và {len(headers)} cột."
        if headers:
            summary += f" Các cột bao gồm: {', '.join(headers[:5])}{'...' if len(headers) > 5 else ''}."
        
        content_parts.append(f"TÓM TẮT: {summary}")
        
        return "\n".join(content_parts)
    
    def _has_vietnamese_content(self, text: str) -> bool:
        """Check if text contains Vietnamese content"""
        vietnamese_chars = 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ'
        return any(char in text.lower() for char in vietnamese_chars)

def process_text_file_tables(file_path: str) -> List[TextTableChunk]:
    """Convenience function for processing text files"""
    processor = TextTableProcessor()
    return processor.process_text_file(file_path) 