"""
LLMSherpa Table Processor for Vietnamese RAG System
Integrates LLMSherpa's LayoutPDFReader with existing enhanced table processing
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import requests

# Import existing enhanced table processor
from enhanced_table_processor import TableChunk, ProcessedTable, EnhancedTableProcessor

try:
    from llmsherpa.readers import LayoutPDFReader
    LLMSHERPA_AVAILABLE = True
except ImportError:
    LLMSHERPA_AVAILABLE = False
    print("⚠️  LLMSherpa not available. Install with: pip install llmsherpa")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class LLMSherpaTable:
    """Enhanced table structure from LLMSherpa"""
    content: str
    html_content: str
    rows: List[List[str]]
    headers: List[str]
    bbox: Optional[Dict[str, float]]  # Bounding box coordinates
    page_number: int
    table_index: int
    confidence: float

class LLMSherpaTableProcessor(EnhancedTableProcessor):
    """Enhanced table processor using LLMSherpa's LayoutPDFReader"""
    
    def __init__(self, nlm_ingestor_url: str = "http://localhost:5010/api/parseDocument?renderFormat=all"):
        super().__init__()
        
        self.nlm_ingestor_url = nlm_ingestor_url
        
        # Check if LLMSherpa is available and server is running
        if LLMSHERPA_AVAILABLE:
            self.llmsherpa_reader = self._init_llmsherpa_reader()
        else:
            self.llmsherpa_reader = None
            logger.warning("LLMSherpa package not available")
        
        self.min_confidence = 0.5  # Table confidence threshold
        
        # Vietnamese table enhancement patterns
        self.vietnamese_table_patterns = {
            'header_indicators': [
                'STT', 'Số TT', 'TT', '#', 'Thứ tự',
                'Tên', 'Họ tên', 'Họ và tên', 'Name',
                'Mô tả', 'Diễn giải', 'Nội dung', 'Description',
                'Số lượng', 'SL', 'Quantity', 'Qty',
                'Đơn giá', 'Giá', 'Price', 'Unit Price',
                'Thành tiền', 'Total', 'Amount', 'Tổng tiền',
                'Ngày', 'Date', 'Thời gian', 'Time',
                'Mã', 'Code', 'ID', 'Mã số',
                'Ghi chú', 'Note', 'Remarks', 'Chú thích'
            ],
            'financial_keywords': [
                'thành tiền', 'tổng cộng', 'tổng tiền', 'doanh thu',
                'chi phí', 'lợi nhuận', 'thuế', 'vat', 'phí',
                'vnđ', 'vnd', 'đồng', 'triệu', 'tỷ'
            ],
            'process_keywords': [
                'bước', 'thủ tục', 'quy trình', 'trách nhiệm',
                'đơn vị', 'phòng ban', 'thời gian thực hiện',
                'kết quả', 'yêu cầu', 'điều kiện'
            ]
        }

    def _init_llmsherpa_reader(self):
        """Initialize LLMSherpa reader with server availability check"""
        try:
            # Test server availability first
            test_response = requests.get(self.nlm_ingestor_url.split('/api')[0] + '/health', timeout=5)
            if test_response.status_code == 200:
                logger.info(f"✅ NLM-Ingestor server available at {self.nlm_ingestor_url}")
                return LayoutPDFReader(self.nlm_ingestor_url)
            else:
                logger.warning(f"⚠️  NLM-Ingestor server not responding (status: {test_response.status_code})")
                return self._try_fallback_servers()
                
        except requests.exceptions.RequestException:
            logger.warning(f"⚠️  Cannot connect to NLM-Ingestor server at {self.nlm_ingestor_url}")
            return self._try_fallback_servers()
        except Exception as e:
            logger.warning(f"⚠️  LLMSherpa initialization failed: {e}")
            return None
    
    def _try_fallback_servers(self):
        """Try alternative LLMSherpa servers"""
        fallback_urls = [
            "https://nlmatics.com/nlm-ingestor",  # Public server
            "http://localhost:5001/api/parseDocument?renderFormat=all",  # Alternative port
            "http://127.0.0.1:5010/api/parseDocument?renderFormat=all"  # Localhost alternative
        ]
        
        for url in fallback_urls:
            try:
                logger.info(f"🔄 Trying fallback server: {url}")
                test_url = url.split('/api')[0] + ('/health' if 'nlmatics.com' not in url else '')
                
                if 'nlmatics.com' in url:
                    # For public server, just try to create reader
                    reader = LayoutPDFReader(url)
                    logger.info(f"✅ Using public NLM-Ingestor server: {url}")
                    return reader
                else:
                    # For local servers, check health first
                    test_response = requests.get(test_url, timeout=3)
                    if test_response.status_code == 200:
                        reader = LayoutPDFReader(url)
                        logger.info(f"✅ Using fallback server: {url}")
                        return reader
                        
            except Exception as e:
                logger.debug(f"Fallback server {url} failed: {e}")
                continue
        
        logger.warning("❌ All LLMSherpa servers unavailable, using enhanced processing only")
        return None

    def process_pdf_with_llmsherpa(self, file_path: str) -> List[TableChunk]:
        """Process PDF using LLMSherpa's LayoutPDFReader"""
        if not self.llmsherpa_reader:
            logger.info(f"🔄 LLMSherpa not available for {Path(file_path).name}, using enhanced processing")
            return self._process_pdf_tables(file_path)
        
        try:
            # Read document with LLMSherpa
            logger.info(f"🔍 Processing {Path(file_path).name} with LLMSherpa...")
            doc = self.llmsherpa_reader.read_pdf(file_path)
            
            table_count = len(doc.tables())
            logger.info(f"📄 LLMSherpa found {table_count} tables in {Path(file_path).name}")
            
            if table_count == 0:
                logger.info("📝 No tables found by LLMSherpa, trying enhanced processing as backup")
                enhanced_tables = self._process_pdf_tables(file_path)
                if enhanced_tables:
                    logger.info(f"✅ Enhanced processing found {len(enhanced_tables)} table(s)")
                return enhanced_tables
            
            table_chunks = []
            
            # Process each table found by LLMSherpa
            for table_idx, table in enumerate(doc.tables()):
                llmsherpa_table = self._extract_llmsherpa_table(table, table_idx)
                
                if llmsherpa_table and llmsherpa_table.confidence >= self.min_confidence:
                    # Convert to ProcessedTable format
                    processed_table = self._process_llmsherpa_table(llmsherpa_table)
                    
                    # Create table chunk
                    chunk = self._create_table_chunk_from_llmsherpa(
                        processed_table, llmsherpa_table, file_path
                    )
                    table_chunks.append(chunk)
                    logger.info(f"✅ Processed table {table_idx + 1} (confidence: {llmsherpa_table.confidence:.2f})")
                else:
                    logger.debug(f"⚠️  Skipped table {table_idx + 1} (low confidence or extraction failed)")
            
            # Also extract sections that might contain tabular data
            section_tables = self._extract_section_tables(doc, file_path)
            if section_tables:
                table_chunks.extend(section_tables)
                logger.info(f"📋 Found {len(section_tables)} additional section tables")
            
            # If no tables were processed successfully, fallback to enhanced processing
            if not table_chunks:
                logger.warning("🔄 No tables met confidence threshold, trying enhanced processing")
                enhanced_tables = self._process_pdf_tables(file_path)
                if enhanced_tables:
                    logger.info(f"✅ Enhanced processing rescued {len(enhanced_tables)} table(s)")
                return enhanced_tables
            
            # Deduplicate and rank
            final_tables = self._deduplicate_tables(table_chunks)
            logger.info(f"🎯 Final result: {len(final_tables)} unique table chunks from LLMSherpa")
            return final_tables
            
        except Exception as e:
            logger.error(f"❌ LLMSherpa processing failed for {Path(file_path).name}: {e}")
            logger.info("🔄 Falling back to enhanced processing...")
            
            # Fallback to standard processing
            enhanced_tables = self._process_pdf_tables(file_path)
            if enhanced_tables:
                logger.info(f"✅ Enhanced processing fallback found {len(enhanced_tables)} table(s)")
            else:
                logger.warning("⚠️  Both LLMSherpa and enhanced processing found no tables")
            return enhanced_tables

    def _extract_llmsherpa_table(self, table, table_idx: int) -> Optional[LLMSherpaTable]:
        """Extract table data from LLMSherpa table object"""
        try:
            # Get table content in different formats
            html_content = table.to_html()
            text_content = table.to_text()
            
            # Parse HTML to extract rows and headers
            rows, headers = self._parse_table_html(html_content)
            
            # Get bounding box if available
            bbox = None
            if hasattr(table, 'bbox') and table.bbox:
                try:
                    if hasattr(table.bbox, 'x0'):
                        bbox = {
                            'x0': table.bbox.x0,
                            'y0': table.bbox.y0,
                            'x1': table.bbox.x1,
                            'y1': table.bbox.y1
                        }
                    elif isinstance(table.bbox, (list, tuple)) and len(table.bbox) >= 4:
                        bbox = {
                            'x0': table.bbox[0],
                            'y0': table.bbox[1],
                            'x1': table.bbox[2],
                            'y1': table.bbox[3]
                        }
                except (AttributeError, IndexError) as e:
                    logger.debug(f"Could not extract bbox: {e}")
                    bbox = None
            
            # Calculate confidence based on structure and content
            confidence = self._calculate_llmsherpa_table_confidence(rows, headers, text_content)
            
            # Get page number if available
            page_number = getattr(table, 'page_idx', 0) + 1
            
            return LLMSherpaTable(
                content=text_content,
                html_content=html_content,
                rows=rows,
                headers=headers,
                bbox=bbox,
                page_number=page_number,
                table_index=table_idx,
                confidence=confidence
            )
            
        except Exception as e:
            logger.warning(f"Failed to extract LLMSherpa table {table_idx}: {e}")
            return None

    def _parse_table_html(self, html_content: str) -> Tuple[List[List[str]], List[str]]:
        """Parse HTML table content to extract rows and headers"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find table element
            table_elem = soup.find('table')
            if not table_elem:
                return [], []
            
            # Extract headers
            headers = []
            header_row = table_elem.find('tr')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(self._clean_cell_text(th.get_text()))
            
            # Extract data rows
            rows = []
            for tr in table_elem.find_all('tr')[1:]:  # Skip header row
                row = []
                for td in tr.find_all(['td', 'th']):
                    row.append(self._clean_cell_text(td.get_text()))
                if row:  # Only add non-empty rows
                    rows.append(row)
            
            return rows, headers
            
        except ImportError:
            logger.warning("BeautifulSoup not available, using simple parsing")
            return self._simple_parse_table_html(html_content)
        except Exception as e:
            logger.warning(f"HTML parsing failed: {e}")
            return [], []

    def _simple_parse_table_html(self, html_content: str) -> Tuple[List[List[str]], List[str]]:
        """Simple HTML table parsing without BeautifulSoup"""
        import re
        
        # Extract table rows using regex
        row_pattern = r'<tr[^>]*>(.*?)</tr>'
        cell_pattern = r'<t[hd][^>]*>(.*?)</t[hd]>'
        
        rows = []
        headers = []
        
        table_rows = re.findall(row_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for i, row_html in enumerate(table_rows):
            cells = re.findall(cell_pattern, row_html, re.DOTALL | re.IGNORECASE)
            clean_cells = [self._clean_cell_text(re.sub(r'<[^>]+>', '', cell)) for cell in cells]
            
            if i == 0:  # First row as headers
                headers = clean_cells
            else:
                if clean_cells:  # Only add non-empty rows
                    rows.append(clean_cells)
        
        return rows, headers

    def _calculate_llmsherpa_table_confidence(self, rows: List[List[str]], 
                                            headers: List[str], content: str) -> float:
        """Calculate confidence score for LLMSherpa extracted table"""
        confidence = 0.0
        
        # Base confidence from LLMSherpa extraction
        confidence += 0.3
        
        # Structure quality
        if headers and len(headers) > 1:
            confidence += 0.2
        
        if rows and len(rows) > 0:
            confidence += 0.2
        
        # Vietnamese content indicators
        content_lower = content.lower()
        
        # Check for Vietnamese table indicators
        header_matches = sum(1 for indicator in self.vietnamese_table_patterns['header_indicators'] 
                           if indicator.lower() in content_lower)
        if header_matches > 0:
            confidence += min(0.2, header_matches * 0.05)
        
        # Check for financial content
        financial_matches = sum(1 for keyword in self.vietnamese_table_patterns['financial_keywords']
                              if keyword in content_lower)
        if financial_matches > 0:
            confidence += min(0.1, financial_matches * 0.02)
        
        # Check for process content
        process_matches = sum(1 for keyword in self.vietnamese_table_patterns['process_keywords']
                            if keyword in content_lower)
        if process_matches > 0:
            confidence += min(0.1, process_matches * 0.02)
        
        return min(1.0, confidence)

    def _process_llmsherpa_table(self, llmsherpa_table: LLMSherpaTable) -> ProcessedTable:
        """Convert LLMSherpa table to ProcessedTable format"""
        # Use existing processing logic from parent class
        processed = self._process_raw_table(
            [llmsherpa_table.headers] + llmsherpa_table.rows,
            f"llmsherpa_table_{llmsherpa_table.table_index}"
        )
        
        # Enhance with LLMSherpa-specific data
        processed.metadata.update({
            'extraction_method': 'llmsherpa',
            'html_content': llmsherpa_table.html_content,
            'bbox': llmsherpa_table.bbox,
            'page_number': llmsherpa_table.page_number,
            'llmsherpa_confidence': llmsherpa_table.confidence
        })
        
        return processed

    def _create_table_chunk_from_llmsherpa(self, processed_table: ProcessedTable, 
                                         llmsherpa_table: LLMSherpaTable, 
                                         file_path: str) -> TableChunk:
        """Create TableChunk from LLMSherpa processed table"""
        chunk_id = f"llmsherpa_{Path(file_path).stem}_p{llmsherpa_table.page_number}_t{llmsherpa_table.table_index}"
        
        # Enhanced content with HTML structure
        enhanced_content = f"""
BẢNG DỮ LIỆU (Trích xuất bằng LLMSherpa)
Trang: {llmsherpa_table.page_number}
Loại: {processed_table.table_type}

{processed_table.structured_content}

TÓM TẮT: {processed_table.summary}

HTML STRUCTURE:
{llmsherpa_table.html_content}
"""
        
        metadata = {
            'file_path': file_path,
            'page_number': llmsherpa_table.page_number,
            'table_index': llmsherpa_table.table_index,
            'extraction_method': 'llmsherpa',
            'bbox': llmsherpa_table.bbox,
            'html_content': llmsherpa_table.html_content,
            'headers': processed_table.headers,
            'row_count': len(processed_table.rows),
            'column_count': len(processed_table.headers),
            'table_type': processed_table.table_type,
            'vietnamese_content': self._has_vietnamese_content(processed_table.raw_content),
            'confidence_factors': self._get_confidence_factors(processed_table.headers, processed_table.rows)
        }
        
        return TableChunk(
            content=enhanced_content,
            table_structure={
                'headers': processed_table.headers,
                'rows': processed_table.rows,
                'html': llmsherpa_table.html_content
            },
            metadata=metadata,
            chunk_id=chunk_id,
            confidence_score=max(processed_table.confidence, llmsherpa_table.confidence),
            source_location=f"{file_path}#page_{llmsherpa_table.page_number}",
            table_type=processed_table.table_type
        )

    def _extract_section_tables(self, doc, file_path: str) -> List[TableChunk]:
        """Extract tables from document sections using LLMSherpa"""
        section_tables = []
        
        try:
            # Iterate through sections to find tabular content
            for section_idx, section in enumerate(doc.sections()):
                section_text = section.to_text()
                
                # Check if section contains tabular data
                if self._contains_tabular_data(section_text):
                    # Try to extract table structure from section
                    table_data = self._extract_table_from_section(section, section_idx)
                    
                    if table_data:
                        processed_table = self._process_raw_table(
                            table_data, f"section_table_{section_idx}"
                        )
                        
                        if processed_table.confidence >= self.min_confidence:
                            chunk = self._create_section_table_chunk(
                                processed_table, section, file_path, section_idx
                            )
                            section_tables.append(chunk)
        
        except Exception as e:
            logger.warning(f"Section table extraction failed: {e}")
        
        return section_tables

    def _contains_tabular_data(self, text: str) -> bool:
        """Check if text contains tabular data patterns"""
        # Look for Vietnamese table indicators
        indicators = self.vietnamese_table_patterns['header_indicators']
        text_lower = text.lower()
        
        indicator_count = sum(1 for indicator in indicators if indicator.lower() in text_lower)
        
        # Look for structured data patterns
        import re
        structured_patterns = [
            r'\d+\s+[^\d\s]+\s+\d+',  # Number + text + number
            r'[^\n]*\|\s*[^\n]*\|\s*[^\n]*',  # Pipe-separated values
            r'[^\n]*\t[^\n]*\t[^\n]*',  # Tab-separated values
            r'\d+\.\s+[^\n]+:\s+[^\n]+',  # Numbered list with values
        ]
        
        pattern_matches = sum(1 for pattern in structured_patterns 
                            if re.search(pattern, text))
        
        return indicator_count >= 2 or pattern_matches >= 2

    def _extract_table_from_section(self, section, section_idx: int) -> Optional[List[List[str]]]:
        """Extract table structure from a document section"""
        try:
            text = section.to_text()
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            # Try to identify table rows
            table_rows = []
            current_table = []
            
            for line in lines:
                if self._is_table_row(line):
                    row_data = self._split_table_row(line)
                    if row_data and len(row_data) > 1:
                        current_table.append(row_data)
                else:
                    if len(current_table) >= 2:  # At least header + 1 data row
                        table_rows.extend(current_table)
                    current_table = []
            
            # Don't forget the last table
            if len(current_table) >= 2:
                table_rows.extend(current_table)
            
            return table_rows if len(table_rows) >= 2 else None
            
        except Exception as e:
            logger.warning(f"Failed to extract table from section {section_idx}: {e}")
            return None

    def _create_section_table_chunk(self, processed_table: ProcessedTable, 
                                  section, file_path: str, section_idx: int) -> TableChunk:
        """Create TableChunk from section-extracted table"""
        chunk_id = f"section_{Path(file_path).stem}_s{section_idx}"
        
        # Get section title if available
        section_title = getattr(section, 'title', f'Section {section_idx + 1}')
        
        enhanced_content = f"""
BẢNG DỮ LIỆU (Từ phần: {section_title})
Loại: {processed_table.table_type}

{processed_table.structured_content}

TÓM TẮT: {processed_table.summary}
"""
        
        metadata = {
            'file_path': file_path,
            'section_index': section_idx,
            'section_title': section_title,
            'extraction_method': 'llmsherpa_section',
            'headers': processed_table.headers,
            'row_count': len(processed_table.rows),
            'column_count': len(processed_table.headers),
            'table_type': processed_table.table_type,
            'vietnamese_content': self._has_vietnamese_content(processed_table.raw_content)
        }
        
        return TableChunk(
            content=enhanced_content,
            table_structure={
                'headers': processed_table.headers,
                'rows': processed_table.rows
            },
            metadata=metadata,
            chunk_id=chunk_id,
            confidence_score=processed_table.confidence,
            source_location=f"{file_path}#section_{section_idx}",
            table_type=processed_table.table_type
        )

    def analyze_document_with_llmsherpa(self, file_path: str) -> Dict[str, Any]:
        """Comprehensive document analysis using LLMSherpa"""
        if not self.llmsherpa_reader:
            return {"error": "LLMSherpa not available"}
        
        try:
            doc = self.llmsherpa_reader.read_pdf(file_path)
            
            analysis = {
                'file_path': file_path,
                'total_pages': len(doc.pages()) if hasattr(doc, 'pages') else 0,
                'total_sections': len(doc.sections()),
                'total_tables': len(doc.tables()),
                'extraction_method': 'llmsherpa',
                'tables': [],
                'sections_with_tables': [],
                'summary': {}
            }
            
            # Analyze each table
            for idx, table in enumerate(doc.tables()):
                table_analysis = {
                    'index': idx,
                    'page': getattr(table, 'page_idx', 0) + 1,
                    'html_content': table.to_html(),
                    'text_content': table.to_text(),
                    'bbox': getattr(table, 'bbox', None),
                    'estimated_rows': len(table.to_text().split('\n')),
                    'contains_vietnamese': self._has_vietnamese_content(table.to_text())
                }
                analysis['tables'].append(table_analysis)
            
            # Analyze sections for tabular content
            for idx, section in enumerate(doc.sections()):
                section_text = section.to_text()
                if self._contains_tabular_data(section_text):
                    section_analysis = {
                        'index': idx,
                        'title': getattr(section, 'title', f'Section {idx + 1}'),
                        'has_tabular_data': True,
                        'text_preview': section_text[:200] + '...' if len(section_text) > 200 else section_text
                    }
                    analysis['sections_with_tables'].append(section_analysis)
            
            # Generate summary
            analysis['summary'] = {
                'total_extractable_tables': len(analysis['tables']) + len(analysis['sections_with_tables']),
                'high_confidence_tables': len([t for t in analysis['tables'] if t.get('estimated_rows', 0) > 2]),
                'vietnamese_content_detected': any(t.get('contains_vietnamese', False) for t in analysis['tables']),
                'recommended_processing': 'llmsherpa' if analysis['total_tables'] > 0 else 'standard'
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"LLMSherpa analysis failed: {e}")
            return {"error": str(e)}

def process_document_with_llmsherpa(file_path: str, 
                                  nlm_ingestor_url: str = "http://localhost:5010/api/parseDocument?renderFormat=all") -> List[TableChunk]:
    """Main function to process document with LLMSherpa"""
    processor = LLMSherpaTableProcessor(nlm_ingestor_url)
    
    if file_path.lower().endswith('.pdf'):
        return processor.process_pdf_with_llmsherpa(file_path)
    else:
        # Fallback to standard processing for non-PDF files
        return processor.process_document_tables(file_path)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        
        if not Path(file_path).exists():
            print(f"❌ File not found: {file_path}")
            sys.exit(1)
        
        print(f"🔍 Processing {file_path} with LLMSherpa...")
        
        # Analyze document
        processor = LLMSherpaTableProcessor()
        analysis = processor.analyze_document_with_llmsherpa(file_path)
        
        print(f"📊 Analysis Results:")
        print(f"   Total tables: {analysis.get('total_tables', 0)}")
        print(f"   Total sections: {analysis.get('total_sections', 0)}")
        print(f"   Sections with tables: {len(analysis.get('sections_with_tables', []))}")
        
        # Process tables
        table_chunks = process_document_with_llmsherpa(file_path)
        print(f"✅ Extracted {len(table_chunks)} table chunks")
        
        # Show sample results
        for i, chunk in enumerate(table_chunks[:3]):
            print(f"\n📋 Table {i+1}:")
            print(f"   Type: {chunk.table_type}")
            print(f"   Confidence: {chunk.confidence_score:.2f}")
            print(f"   Location: {chunk.source_location}")
            print(f"   Content preview: {chunk.content[:200]}...")
    
    else:
        print("Usage: python llmsherpa_table_processor.py <file_path>")
        print("Example: python llmsherpa_table_processor.py knowledge_files/sample.pdf")