from typing import List, Dict, Optional, Any
import json
from pathlib import Path
import logging
import os
import uuid
from datetime import datetime
import re
from mem0 import Memory
from ..config.memory_config import Mem0Config, DEFAULT_CONFIG, MemoryMetadata

logger = logging.getLogger(__name__)

class MemoryService:
    def __init__(self):
        self.settings_file = Path("data/memory_settings.json")
        self.settings_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Load default settings
        self._settings = DEFAULT_CONFIG.copy()
        
        # Load existing settings if available
        self._load_settings()
        
        # Initialize Mem0 instance
        self._mem0 = None
        self._initialize_mem0()
        
        # Vietnamese stop words for keyword extraction
        self.vietnamese_stop_words = {
            'của', 'và', 'trong', 'cho', 'với', 'từ', 'theo', 'về',
            'là', 'có', 'được', 'sẽ', 'đã', 'này', 'đó', 'các',
            'những', 'nhiều', 'một', 'hai', 'ba', 'bốn', 'năm',
            'thì', 'để', 'khi', 'nếu', 'mà', 'như', 'vì', 'do'
        }

    def _load_settings(self):
        """Load settings from file if it exists"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file) as f:
                    stored_settings = json.load(f)
                    self._settings.update(stored_settings)
        except Exception as e:
            logger.error(f"Error loading settings: {e}")

    def _save_settings(self):
        """Save current settings to file"""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self._settings, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
    
    def _initialize_mem0(self):
        """Initialize Mem0 instance with current settings"""
        try:
            if not self._settings["enabled"]:
                logger.info("Mem0 is disabled in settings")
                return
                
            # Create Mem0 config
            config = {
                "embedder": {
                    "provider": self._settings["embedding_provider"]["provider"],
                    "config": {
                        "model": self._settings["embedding_provider"]["model"],
                        "api_key": self._settings["embedding_provider"]["api_key"]
                    }
                }
            }
            
            # Initialize Mem0
            logger.info(f"Initializing Mem0 with {config['embedder']['provider']} embeddings")
            self._mem0 = Memory.from_config(config)
            logger.info("Mem0 initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Mem0: {e}")
            self._mem0 = None

    def get_settings(self) -> Dict[str, Any]:
        """Get current memory settings"""
        return self._settings

    def update_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update memory settings"""
        # Update settings
        self._settings.update(settings)
        self._save_settings()
        
        # Reinitialize Mem0 with new settings
        self._initialize_mem0()
        
        return self._settings

    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text, optimized for Vietnamese"""
        if not text or not self._settings.get("auto_keyword_extraction", True):
            return []
            
        # Simple keyword extraction - remove stop words and get meaningful terms
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [word for word in words
                   if len(word) > 2 and word not in self.vietnamese_stop_words]
        
        # Return top 10 most relevant keywords
        return list(set(keywords))[:10]
    
    def _extract_tags(self, text: str, source: str) -> List[str]:
        """Extract tags based on content analysis"""
        if not text or not self._settings.get("auto_tag_extraction", True):
            return []
            
        tags = []
        text_lower = text.lower()
        
        # Content type tags
        if '?' in text:
            tags.append('question')
        if any(word in text_lower for word in ['cảm ơn', 'thanks', 'thank you']):
            tags.append('gratitude')
        if any(word in text_lower for word in ['lỗi', 'error', 'bug', 'problem']):
            tags.append('technical_issue')
        if any(word in text_lower for word in ['code', 'coding', 'programming', 'lập trình']):
            tags.append('programming')
        if any(word in text_lower for word in ['rag', 'memory', 'bộ nhớ', 'tìm kiếm']):
            tags.append('rag_system')
        
        # Source-based tags
        tags.append(f'source_{source}')
        
        return tags
    
    def _detect_language(self, text: str) -> str:
        """Detect primary language of text"""
        if not text:
            return "unknown"
            
        # Simple heuristic - count Vietnamese vs English characters
        vietnamese_chars = len(re.findall(r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', text.lower()))
        english_chars = len(re.findall(r'[a-z]', text.lower()))
        
        if vietnamese_chars > english_chars * 0.1:
            return "vi"
        elif english_chars > 0:
            return "en"
        else:
            return "mixed"
    
    def _determine_context_type(self, text: str, role: str) -> str:
        """Determine the context type of the message"""
        if not text:
            return "unknown"
            
        text_lower = text.lower()
        
        if role == "user":
            if '?' in text:
                return "question"
            elif any(word in text_lower for word in ['làm', 'tạo', 'viết', 'create', 'make', 'write']):
                return "instruction"
            else:
                return "casual"
        elif role == "assistant":
            return "answer"
        else:
            return "system"
    
    def _create_enhanced_metadata(
        self,
        message: Dict,
        session_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> MemoryMetadata:
        """Create enhanced metadata for a memory entry"""
        content = message.get("content", "")
        role = message.get("role", "user")
        
        return MemoryMetadata(
            timestamp=datetime.now(),
            session_id=session_id or str(uuid.uuid4()),
            conversation_id=conversation_id or str(uuid.uuid4()),
            source=role,
            tags=self._extract_tags(content, role),
            keywords=self._extract_keywords(content),
            user_feedback=None,  # Will be set later via feedback API
            confidence_score=None,  # Could be set by embedding model
            context_type=self._determine_context_type(content, role),
            language=self._detect_language(content),
            topic=None,  # Could be enhanced with topic modeling
            importance=1  # Default importance
        )

    async def add_memory(
        self,
        messages: List[Dict],
        user_id: str = "default_user",
        session_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> None:
        """Add a new memory from conversation messages with enhanced metadata"""
        try:
            if not self._mem0 or not self._settings["enabled"]:
                logger.debug("Mem0 not available or disabled, skipping memory addition")
                return
            
            # If metadata enhancement is enabled, enrich the messages
            if self._settings.get("enable_metadata", True):
                enhanced_messages = []
                for message in messages:
                    enhanced_message = message.copy()
                    
                    # Create enhanced metadata
                    msg_metadata = self._create_enhanced_metadata(
                        message, session_id, conversation_id
                    )
                    
                    # Add metadata to message
                    enhanced_message["metadata"] = {
                        "timestamp": msg_metadata.timestamp.isoformat(),
                        "session_id": msg_metadata.session_id,
                        "conversation_id": msg_metadata.conversation_id,
                        "source": msg_metadata.source,
                        "tags": msg_metadata.tags,
                        "keywords": msg_metadata.keywords,
                        "context_type": msg_metadata.context_type,
                        "language": msg_metadata.language,
                        "importance": msg_metadata.importance
                    }
                    
                    # Add any additional metadata provided
                    if metadata:
                        enhanced_message["metadata"].update(metadata)
                    
                    enhanced_messages.append(enhanced_message)
                
                # Add enhanced memory using Mem0
                self._mem0.add(enhanced_messages, user_id=user_id)
                logger.debug(f"Added enhanced memory for user {user_id} with metadata")
            else:
                # Add memory using Mem0 without enhancement
                self._mem0.add(messages, user_id=user_id)
                logger.debug(f"Added basic memory for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error adding memory: {str(e)}")
            raise

    async def get_relevant_memories(
        self,
        query: str,
        user_id: str = "default_user",
        limit: int = 3,
        filters: Optional[Dict] = None
    ) -> Dict:
        """Retrieve relevant memories for the given query with metadata filtering"""
        try:
            if not self._mem0 or not self._settings["enabled"]:
                logger.debug("Mem0 not available or disabled, returning empty memories")
                return {"results": []}
            
            # Search memories using Mem0
            memories = self._mem0.search(query=query, user_id=user_id, limit=limit * 2)  # Get more to filter
            results = memories.get('results', [])
            
            # Apply metadata filters if provided
            if filters and self._settings.get("enable_metadata", True):
                filtered_results = []
                for memory in results:
                    memory_data = memory.get('memory', '')
                    
                    # Try to extract metadata from memory content
                    # (This is a simplified approach - in production, metadata should be stored separately)
                    should_include = True
                    
                    # Filter by session_id
                    if filters.get('session_id') and filters['session_id'] not in memory_data:
                        should_include = False
                    
                    # Filter by source
                    if filters.get('source') and filters['source'] not in memory_data:
                        should_include = False
                    
                    # Filter by tags
                    if filters.get('tags'):
                        required_tags = filters['tags'] if isinstance(filters['tags'], list) else [filters['tags']]
                        if not any(tag in memory_data.lower() for tag in required_tags):
                            should_include = False
                    
                    # Filter by time range (if timestamp is available)
                    if filters.get('since') or filters.get('until'):
                        # This would require proper timestamp storage in metadata
                        pass
                    
                    if should_include:
                        filtered_results.append(memory)
                
                results = filtered_results[:limit]
            else:
                results = results[:limit]
            
            # Enhance results with metadata information
            enhanced_results = []
            for memory in results:
                enhanced_memory = memory.copy()
                
                # Add metadata analysis
                memory_content = memory.get('memory', '')
                enhanced_memory['analysis'] = {
                    'keywords': self._extract_keywords(memory_content),
                    'language': self._detect_language(memory_content),
                    'estimated_importance': self._estimate_importance(memory_content, memory.get('score', 0))
                }
                
                enhanced_results.append(enhanced_memory)
            
            logger.debug(f"Retrieved {len(enhanced_results)} enhanced memories for user {user_id}")
            
            return {"results": enhanced_results}
            
        except Exception as e:
            logger.error(f"Error retrieving memories: {str(e)}")
            return {"results": []}
    
    def _estimate_importance(self, content: str, relevance_score: float) -> int:
        """Estimate importance of memory based on content and relevance"""
        importance = 1
        
        # Boost importance for questions and technical content
        if '?' in content:
            importance += 1
        if any(word in content.lower() for word in ['error', 'bug', 'problem', 'lỗi']):
            importance += 2
        if any(word in content.lower() for word in ['code', 'programming', 'lập trình']):
            importance += 1
        
        # Factor in relevance score
        if relevance_score > 0.8:
            importance += 1
        
        return min(importance, 5)  # Cap at 5

    async def enhance_prompt_with_memories(
        self,
        messages: List[Dict],
        user_id: str = "default_user",
        limit: int = 3,
        session_id: Optional[str] = None
    ) -> List[Dict]:
        """Enhance the conversation with relevant memories using rich metadata"""
        try:
            if not self._mem0 or not self._settings["enabled"]:
                logger.debug("Mem0 not available or disabled, returning original messages")
                return messages
                
            # Get the last user message to search for relevant memories
            last_user_message = None
            current_context_type = None
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    last_user_message = msg.get("content", "")
                    current_context_type = self._determine_context_type(last_user_message, "user")
                    break
            
            if not last_user_message:
                logger.debug("No user message found, returning original messages")
                return messages
            
            # Create filters for more relevant memory retrieval
            filters = {}
            if session_id:
                filters['session_id'] = session_id
            
            # Prioritize memories from same context type
            if current_context_type:
                filters['context_type'] = current_context_type
                
            # Search for relevant memories with metadata filtering
            memories = await self.get_relevant_memories(
                last_user_message, user_id, limit, filters
            )
            memory_results = memories.get("results", [])
            
            if not memory_results:
                logger.debug("No relevant memories found, returning original messages")
                return messages
            
            # Format memories with enhanced context using metadata
            memory_contexts = []
            for mem in memory_results:
                memory_text = mem['memory']
                analysis = mem.get('analysis', {})
                
                # Create rich context with metadata
                context_parts = [f"Memory: {memory_text}"]
                
                if analysis.get('keywords'):
                    context_parts.append(f"Keywords: {', '.join(analysis['keywords'][:5])}")
                
                if analysis.get('language'):
                    context_parts.append(f"Language: {analysis['language']}")
                
                if analysis.get('estimated_importance', 1) > 2:
                    context_parts.append("(High importance)")
                
                memory_contexts.append(" | ".join(context_parts))
            
            memory_context = "\n".join([f"- {ctx}" for ctx in memory_contexts])
            
            # Create enhanced messages with richer context
            enhanced_messages = messages.copy()
            
            # Determine language for system message
            detected_language = self._detect_language(last_user_message)
            
            # Create context-aware system message
            if detected_language == "vi":
                system_context = f"""Bạn là một AI assistant thông minh. Sử dụng những ký ức sau từ các cuộc trò chuyện trước để đưa ra phản hồi cá nhân hóa và chính xác hơn:

{memory_context}

Lưu ý: Ưu tiên thông tin từ những ký ức có độ quan trọng cao và phù hợp với ngữ cảnh hiện tại."""
            else:
                system_context = f"""You are an intelligent AI assistant. Use the following memories from previous conversations to provide more personalized and accurate responses:

{memory_context}

Note: Prioritize information from high-importance memories that are relevant to the current context."""
            
            # Add memory context to system message or create new one
            system_message_found = False
            for i, msg in enumerate(enhanced_messages):
                if msg.get("role") == "system":
                    # Append to existing system message
                    enhanced_messages[i]["content"] += f"\n\n{system_context}"
                    system_message_found = True
                    break
            
            if not system_message_found:
                # Insert new system message at the beginning
                system_msg = {
                    "role": "system",
                    "content": system_context
                }
                enhanced_messages.insert(0, system_msg)
            
            logger.debug(f"Enhanced prompt with {len(memory_results)} enriched memories for user {user_id}")
            return enhanced_messages
            
        except Exception as e:
            logger.error(f"Error enhancing prompt with memories: {str(e)}")
            return messages

    async def get_all_memories(self, user_id: str = "default_user") -> List[Dict]:
        """Get all memories for a specific user"""
        try:
            if not self._mem0 or not self._settings["enabled"]:
                logger.debug("Mem0 not available or disabled, returning empty list")
                return []
                
            # Get all memories for the user
            memories = self._mem0.get_all(user_id=user_id)
            logger.debug(f"Retrieved {len(memories) if memories else 0} memories for user {user_id}")
            
            return memories if memories else []
            
        except Exception as e:
            logger.error(f"Error getting all memories: {str(e)}")
            return []

    async def add_user_feedback(
        self,
        memory_id: str,
        feedback: str,
        user_id: str = "default_user"
    ) -> None:
        """Add user feedback to a memory for learning purposes"""
        try:
            if not self._settings.get("track_user_feedback", True):
                logger.debug("User feedback tracking disabled")
                return
                
            # In a full implementation, this would update the memory metadata
            # For now, we'll log it for future processing
            logger.info(f"User feedback for memory {memory_id}: {feedback} (user: {user_id})")
            
            # Store feedback in a separate tracking system
            feedback_data = {
                "memory_id": memory_id,
                "user_id": user_id,
                "feedback": feedback,
                "timestamp": datetime.now().isoformat()
            }
            
            # Save to feedback file for processing
            feedback_file = Path("data/memory_feedback.jsonl")
            feedback_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(feedback_file, "a") as f:
                f.write(json.dumps(feedback_data) + "\n")
                
        except Exception as e:
            logger.error(f"Error adding user feedback: {str(e)}")
            raise
    
    async def get_memory_analytics(self, user_id: str = "default_user") -> Dict:
        """Get analytics about user's memory usage"""
        try:
            if not self._mem0 or not self._settings["enabled"]:
                return {"error": "Memory service not available"}
            
            memories = await self.get_all_memories(user_id)
            
            if not memories:
                return {"total_memories": 0}
            
            # Analyze memories
            total_memories = len(memories)
            languages = {}
            sources = {}
            topics = {}
            
            for memory in memories:
                content = memory.get('memory', '')
                
                # Language analysis
                lang = self._detect_language(content)
                languages[lang] = languages.get(lang, 0) + 1
                
                # Source analysis (simplified)
                if 'user:' in content.lower():
                    sources['user'] = sources.get('user', 0) + 1
                elif 'assistant:' in content.lower():
                    sources['assistant'] = sources.get('assistant', 0) + 1
                else:
                    sources['unknown'] = sources.get('unknown', 0) + 1
            
            return {
                "total_memories": total_memories,
                "languages": languages,
                "sources": sources,
                "topics": topics,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting memory analytics: {str(e)}")
            return {"error": str(e)}

    async def delete_memory(self, memory_id: str, user_id: str = "default_user") -> None:
        """Delete a specific memory"""
        try:
            if not self._mem0 or not self._settings["enabled"]:
                logger.debug("Mem0 not available or disabled, skipping memory deletion")
                return
                
            # Delete the specific memory
            self._mem0.delete(memory_id)
            logger.info(f"Deleted memory {memory_id} for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error deleting memory {memory_id}: {str(e)}")
            raise

# Create global instance
memory_service = MemoryService() 