"""
Enhanced RAG Service with LLMSherpa Table Processing
Extension of existing RAG service with advanced table extraction
"""

import logging
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add current directory for LLMSherpa imports
current_dir = Path(__file__).parent.parent
sys.path.append(str(current_dir / "rag"))
sys.path.append("/home/<USER>/AccA")

from .rag_service import RAGService
from ..models.rag_models import DocumentUploadResponse
from ..core.config import settings

# Enhanced imports
try:
    from llmsherpa_table_processor import LLMSherpaTableProcessor
    from integrate_llmsherpa_rag import LLMSherpaRAGIntegrator
    LLMSHERPA_AVAILABLE = True
except ImportError as e:
    LLMSHERPA_AVAILABLE = False
    logging.warning(f"LLMSherpa not available: {e}")

logger = logging.getLogger(__name__)

class EnhancedRAGService(RAGService):
    """Enhanced RAG Service with LLMSherpa table processing"""
    
    def __init__(self):
        super().__init__()
        self.table_processing_enabled = LLMSHERPA_AVAILABLE
        
        if self.table_processing_enabled:
            try:
                self.llmsherpa_processor = LLMSherpaTableProcessor()
                self.rag_integrator = LLMSherpaRAGIntegrator()
                logger.info("✅ Enhanced table processing initialized")
            except Exception as e:
                logger.warning(f"⚠️  Table processing init failed: {e}")
                self.table_processing_enabled = False
        else:
            logger.warning("⚠️  LLMSherpa table processing disabled")
    
    def is_enhanced_ready(self) -> bool:
        """Check if enhanced processing is available"""
        return self.is_ready() and self.table_processing_enabled
    
    async def process_document_upload_enhanced(self, file) -> DocumentUploadResponse:
        """Enhanced document upload with table processing"""
        
        # Start with standard processing
        standard_response = await super().process_document_upload(file)
        
        if not self.table_processing_enabled:
            return standard_response
        
        # Enhance with table processing for PDFs
        file_location = os.path.join(settings.RAG_DOCUMENTS_DIR, file.filename)
        
        if file.filename.lower().endswith('.pdf'):
            try:
                logger.info(f"🔍 Processing tables in: {file.filename}")
                
                # Process tables using LLMSherpa
                table_chunks = self.rag_integrator.process_document_hybrid(
                    file_location, 
                    force_method=None  # Use hybrid approach
                )
                
                if table_chunks:
                    logger.info(f"📊 Found {len(table_chunks)} table chunks")
                    
                    # Store table chunks in specialized database
                    self.rag_integrator._store_chunks(table_chunks)
                    
                    # Update response
                    standard_response.message += f" Enhanced with {len(table_chunks)} table chunks."
                    standard_response.chunks_added += len(table_chunks)
                else:
                    logger.info(f"📄 No tables found in {file.filename}")
                    
            except Exception as e:
                logger.error(f"❌ Enhanced table processing failed: {e}")
                standard_response.message += f" (Table processing failed: {str(e)})"
        
        return standard_response
    
    def search_tables(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search specifically in table content"""
        if not self.table_processing_enabled:
            return []
        
        try:
            results = self.rag_integrator.search_tables(query, limit=limit)
            logger.info(f"🔍 Table search for '{query}': {len(results)} results")
            return results
        except Exception as e:
            logger.error(f"❌ Table search failed: {e}")
            return []
    
    def get_table_processing_stats(self) -> Dict[str, Any]:
        """Get table processing statistics"""
        if not self.table_processing_enabled:
            return {"table_processing": "disabled"}
        
        try:
            stats = self.rag_integrator.get_processing_stats()
            stats.update({
                "table_processing": "enabled",
                "llmsherpa_available": True,
                "enhanced_service": "active"
            })
            return stats
        except Exception as e:
            logger.error(f"❌ Failed to get table stats: {e}")
            return {"table_processing": "error", "error": str(e)}
    
    def get_enhanced_status(self) -> Dict[str, Any]:
        """Get enhanced service status"""
        base_status = self.get_vector_store_status()
        
        enhanced_status = {
            "base_rag": base_status,
            "table_processing": {
                "enabled": self.table_processing_enabled,
                "llmsherpa_available": LLMSHERPA_AVAILABLE
            }
        }
        
        if self.table_processing_enabled:
            try:
                table_stats = self.get_table_processing_stats()
                enhanced_status["table_stats"] = table_stats
            except Exception as e:
                enhanced_status["table_processing"]["error"] = str(e)
        
        return enhanced_status

# Factory function
def create_enhanced_rag_service() -> EnhancedRAGService:
    """Create enhanced RAG service instance"""
    return EnhancedRAGService()

# Test the service
if __name__ == "__main__":
    print("🧪 Testing Enhanced RAG Service")
    print("=" * 40)
    
    try:
        service = EnhancedRAGService()
        print(f"✅ Service initialized")
        print(f"📊 Ready: {service.is_ready()}")
        print(f"🔧 Enhanced Ready: {service.is_enhanced_ready()}")
        
        status = service.get_enhanced_status()
        print(f"📋 Status: {status}")
        
        if service.table_processing_enabled:
            stats = service.get_table_processing_stats()
            print(f"📈 Table Stats: {stats}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc() 