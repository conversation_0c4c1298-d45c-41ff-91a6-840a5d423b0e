import logging
import os
import shutil
from fastapi import UploadFile
from typing import List, Dict, Any

from core.config import settings
# Import EnhancedDocumentProcessor instead of basic DocumentProcessor
try:
    from app.rag.enhanced_document_processor import EnhancedDocumentProcessor
    ENHANCED_PROCESSOR_AVAILABLE = True
except ImportError:
    try:
        from rag.enhanced_document_processor import EnhancedDocumentProcessor
        ENHANCED_PROCESSOR_AVAILABLE = True
    except ImportError:
        from rag.document_processor import DocumentProcessor
        ENHANCED_PROCESSOR_AVAILABLE = False
    
from rag.embedding_generator import EmbeddingGenerator
from rag.vector_store import FaissVectorStore
from services.llm_service import llm_service # To generate final answer
from models.rag_models import RAGQueryRequest, DocumentChunk, DocumentUploadResponse
from models.llm_models import ChatResponse, LLMProvider, ChatRequest # For synthesis

logger = logging.getLogger(__name__)

class RAGService:
    def __init__(self):
        logger.info("Initializing RAGService...")
        
        # Use EnhancedDocumentProcessor if available, fallback to basic DocumentProcessor
        if ENHANCED_PROCESSOR_AVAILABLE:
            self.document_processor = EnhancedDocumentProcessor()
            logger.info("✅ Using EnhancedDocumentProcessor with table processing")
        else:
            self.document_processor = DocumentProcessor()
            logger.warning("⚠️  Using basic DocumentProcessor - table processing not available")
            
        self.embedding_generator = EmbeddingGenerator()
        
        # Initialize LLMSherpa enhanced processing (keep as additional layer)
        self.llmsherpa_processor = None
        self.rag_integrator = None
        try:
            from llmsherpa_table_processor import LLMSherpaTableProcessor
            from integrate_llmsherpa_rag import LLMSherpaRAGIntegrator
            
            self.llmsherpa_processor = LLMSherpaTableProcessor()
            self.rag_integrator = LLMSherpaRAGIntegrator()
            logger.info("✅ LLMSherpa enhanced table processing initialized as additional layer")
        except ImportError as e:
            logger.info(f"📄 LLMSherpa not available, using enhanced processor only: {e}")
        except Exception as e:
            logger.warning(f"⚠️  LLMSherpa initialization failed: {e}")
        
        # Initialize FaissVectorStore, attempting to get dimension from EmbeddingGenerator if not already known
        embedding_dim = None
        if self.embedding_generator.is_available:
            embedding_dim = self.embedding_generator.get_embedding_dimension()
            if embedding_dim:
                logger.info(f"Retrieved embedding dimension from EmbeddingGenerator: {embedding_dim}")
            else:
                logger.warning("Could not get embedding dimension from EmbeddingGenerator. FaissVectorStore might fail or use a default.")
        else:
            logger.warning("EmbeddingGenerator not available during RAGService init. FaissVectorStore dimension might be an issue.")
        
        try:
            self.vector_store = FaissVectorStore(embedding_dimension=embedding_dim)
            logger.info("FaissVectorStore initialized successfully within RAGService.")
        except RuntimeError as e:
            logger.error(f"Failed to initialize FaissVectorStore in RAGService: {e}. RAG functionality will be impaired.")
            self.vector_store = None # Ensure it's None if init fails

    def is_ready(self) -> bool:
        """Check if all components of RAGService are available."""
        return (
            self.document_processor is not None and 
            self.embedding_generator is not None and self.embedding_generator.is_available and
            self.vector_store is not None and self.vector_store.index is not None
        )

    async def process_document_upload(self, file: UploadFile) -> DocumentUploadResponse:
        if not self.is_ready():
            logger.error("RAGService is not ready. Cannot process document upload.")
            return DocumentUploadResponse(filename=file.filename, message="Error: RAGService is not properly initialized.", chunks_added=0)

        # Save the uploaded file temporarily to the configured documents directory
        file_location = os.path.join(settings.RAG_DOCUMENTS_DIR, file.filename)
        try:
            with open(file_location, "wb+") as file_object:
                shutil.copyfileobj(file.file, file_object)
            logger.info(f"Uploaded file '{file.filename}' saved to '{file_location}'")
        except Exception as e:
            logger.error(f"Failed to save uploaded file {file.filename}: {e}", exc_info=True)
            return DocumentUploadResponse(filename=file.filename, message=f"Error saving file: {str(e)}", chunks_added=0)
        finally:
            file.file.close() # Ensure file stream is closed

        # 1. Process the document (extract text, split into chunks)
        logger.info(f"Processing document: {file.filename}")
        
        # Enhanced processing for PDFs with tables (Vietnamese support) - Additional LLMSherpa layer
        llmsherpa_chunks = []
        if (file.filename.lower().endswith('.pdf') and 
            self.llmsherpa_processor and self.rag_integrator):
            try:
                logger.info(f"🚀 Additional LLMSherpa table processing: {file.filename}")
                llmsherpa_chunks = self.rag_integrator.process_document_hybrid(file_location)
                if llmsherpa_chunks:
                    logger.info(f"📊 LLMSherpa extracted {len(llmsherpa_chunks)} additional enhanced chunks")
            except Exception as e:
                logger.warning(f"⚠️  LLMSherpa processing failed, using enhanced processor only: {e}")
        
        # Primary processing with EnhancedDocumentProcessor (includes table processing)
        chunks_data_list = self.document_processor.process_document(file_location)
        
        # Add LLMSherpa chunks if available (as additional enhancement)
        total_chunks = len(chunks_data_list)
        texts_to_embed = []
        
        # Add standard/enhanced chunk texts to embedding list
        for chunk_data in chunks_data_list:
            texts_to_embed.append(chunk_data["text"])
        
        if llmsherpa_chunks:
            # Convert LLMSherpa chunks to standard format and add as additional chunks
            for chunk in llmsherpa_chunks:
                chunk_data = {
                    "text": chunk.content,
                    "metadata": {
                        "filename": file.filename,
                        "chunk_type": f"llmsherpa_table_{chunk.table_type}",
                        "confidence": chunk.confidence_score,
                        "source_location": chunk.source_location,
                        "processing_method": "llmsherpa_additional"
                    }
                }
                chunks_data_list.append(chunk_data)
                texts_to_embed.append(chunk.content)
                total_chunks += 1
        
        if not chunks_data_list:
            logger.warning(f"No chunks extracted from document: {file.filename}")
            return DocumentUploadResponse(filename=file.filename, message="No text chunks extracted from the document.", chunks_added=0)

        # 2. Generate embeddings for the chunks
        logger.info(f"Generating embeddings for {len(texts_to_embed)} chunks from {file.filename}")
        embeddings = await self.embedding_generator.generate_embeddings(texts_to_embed)
        if not embeddings or len(embeddings) != len(texts_to_embed):
            logger.error(f"Failed to generate embeddings for chunks from {file.filename}. Expected {len(texts_to_embed)}, got {len(embeddings) if embeddings else 0}.")
            # os.remove(file_location) # Clean up saved file
            return DocumentUploadResponse(filename=file.filename, message="Error generating embeddings for document chunks.", chunks_added=0)

        # 3. Add chunks and their embeddings to the vector store
        chunks_with_embeddings = list(zip(chunks_data_list, embeddings))
        chunks_added_count = self.vector_store.add_chunks(chunks_with_embeddings)
        
        logger.info(f"Successfully processed and added {chunks_added_count} chunks from {file.filename} to the vector store.")
        
        # The original uploaded file is kept in RAG_DOCUMENTS_DIR for now.
        # Could add logic here to move it to a persistent archive or delete if only chunks are needed.

        # Create enhanced response message
        enhancement_info = ""
        if ENHANCED_PROCESSOR_AVAILABLE:
            enhancement_info = " (Enhanced table processing enabled)"
        if llmsherpa_chunks:
            enhancement_info += f" + {len(llmsherpa_chunks)} LLMSherpa chunks"
        
        return DocumentUploadResponse(
            filename=file.filename,
            message=f"Document processed successfully. {chunks_added_count} chunks added to RAG store.{enhancement_info}",
            document_id=file.filename, # Using filename as a simple ID for now
            chunks_added=chunks_added_count
        )

    async def answer_query_with_rag(self, request: RAGQueryRequest) -> ChatResponse:
        if not self.is_ready():
            logger.error("RAGService is not ready. Cannot answer query with RAG.")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL, # Or a default
                model="rag_unavailable",
                response=None,
                error="Error: RAGService is not properly initialized or unavailable."
            )

        logger.info(f"Processing RAG query: {request.prompt[:100]}...")

        # 1. Generate embedding for the user query
        query_embedding_list = await self.embedding_generator.generate_embeddings([request.prompt])
        if not query_embedding_list or not query_embedding_list[0]:
            logger.error("Failed to generate embedding for RAG query.")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL, model="rag_query_embedding_error",
                response=None, error="Failed to generate query embedding."
            )
        query_embedding = query_embedding_list[0]

        # 2. Retrieve relevant chunks from the vector store
        top_k = request.top_k if request.top_k is not None else settings.RAG_TOP_K_RESULTS
        retrieved_chunks = await self.vector_store.search_similar_chunks(query_embedding, top_k=top_k)
        
        if not retrieved_chunks:
            logger.info("No relevant chunks found in RAG store for the query. Answering with LLM directly (or indicate no context).")
            # Option 1: Answer with LLM without context
            # Option 2: Return a message that no context was found
            # For now, let's try to synthesize with LLM but indicate no specific context was found.
            context_for_llm = "No specific context found in documents for this query."
        else:
            context_for_llm = "\n\n---\nRelevant Context from Documents:\n"
            for i, chunk in enumerate(retrieved_chunks):
                context_for_llm += f"Chunk {i+1} (Source: {chunk.metadata.filename}, Page: {chunk.metadata.page_number or 'N/A'}):\n{chunk.text}\n\n"
            context_for_llm += "---\n"

        # 3. Augment the prompt and generate response using an LLM (e.g., local Gemma or specified)
        augmented_prompt = f"{context_for_llm}\n\nUser Query: {request.prompt}\n\nBased ONLY on the provided context (if any) and your general knowledge if no context is relevant, answer the user query."
        
        logger.info(f"Augmented prompt for LLM (first 200 chars): {augmented_prompt[:200]}...")

        # Using local Gemma as default for synthesis, can be made configurable
        # This assumes llm_service is available and Gemma client within it is functional.
        if llm_service.gemma_mediapipe_client and llm_service.gemma_mediapipe_client.model_loaded:
            gemma_request = ChatRequest(prompt=augmented_prompt)
            final_response = await llm_service.process_chat(gemma_request)
            # Modify the model name in response to indicate RAG was used
            if final_response.model:
                final_response.model = f"{final_response.model}-rag"
            else: # Should not happen if gemma_mediapipe_client is available
                final_response.model = "gemma_local-rag"
            return final_response
        else:
            logger.warning("Local Gemma client not available for RAG synthesis. Cannot generate final answer.")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model="gemma_unavailable_for_rag",
                response=None,
                error="Local Gemma model is not available to synthesize RAG answer."
            )

    async def get_retrieved_chunks_for_query(self, query: str, top_k: int | None = None) -> List[DocumentChunk]:
        """Utility to just get the retrieved chunks for a query (for debugging/inspection)."""
        if not self.is_ready():
            logger.error("RAGService is not ready. Cannot retrieve chunks.")
            return []
        
        query_embedding_list = await self.embedding_generator.generate_embeddings([query])
        if not query_embedding_list or not query_embedding_list[0]:
            logger.error("Failed to generate embedding for RAG chunk retrieval.")
            return []
        query_embedding = query_embedding_list[0]
        
        k_to_use = top_k if top_k is not None else settings.RAG_TOP_K_RESULTS
        retrieved_chunks = await self.vector_store.search_similar_chunks(query_embedding, top_k=k_to_use)
        return retrieved_chunks

    def get_vector_store_status(self) -> Dict[str, Any]:
        if not self.vector_store:
            return {"status": "error", "message": "Vector store not initialized."}
        return {
            "status": "ready" if self.vector_store.index is not None else "initializing_failed",
            "index_path": self.vector_store.index_path,
            "metadata_path": self.vector_store.metadata_path,
            "total_vectors": self.vector_store.get_total_vectors(),
            "embedding_dimension": self.vector_store.embedding_dimension
        }

    def clear_rag_store(self) -> Dict[str, str]:
        if not self.vector_store:
            logger.error("Vector store not initialized. Cannot clear.")
            return {"status": "error", "message": "Vector store not initialized."}
        try:
            self.vector_store.clear_index()
            # Also clear the RAG_DOCUMENTS_DIR if desired, but be cautious
            # For now, just clearing the index.
            # shutil.rmtree(settings.RAG_DOCUMENTS_DIR)
            # os.makedirs(settings.RAG_DOCUMENTS_DIR, exist_ok=True)
            logger.info("RAG vector store (FAISS index and metadata) cleared.")
            return {"status": "success", "message": "RAG vector store cleared successfully."}
        except Exception as e:
            logger.error(f"Error clearing RAG store: {e}", exc_info=True)
            return {"status": "error", "message": f"Error clearing RAG store: {str(e)}"}

# Singleton instance
rag_service = RAGService() 