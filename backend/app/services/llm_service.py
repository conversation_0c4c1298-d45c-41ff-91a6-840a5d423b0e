import logging
from core.config import settings
# from llm_clients.openai_client import Open<PERSON><PERSON><PERSON>
# from llm_clients.gemini_client import <PERSON><PERSON><PERSON>
# from llm_clients.deepseek_client import DeepSeekClient
# from llm_clients.gemma_client import GemmaLocal<PERSON>lient
# from llm_clients.gemma_3_4b_client import Gemma3_4BLocalClient
from services.gemma3n_mediapipe_service import gemma3n_mediapipe_service
from models.llm_models import (
    LLMProvider,
    ChatResponse, ChatRequest
)
from PIL import Image
import io

logger = logging.getLogger(__name__)

class LLMService:
    def __init__(self):
        self.gemma_mediapipe_client = gemma3n_mediapipe_service

    async def process_chat(self, request: ChatRequest) -> ChatResponse:
        logger.info(f"Processing local Gemma text chat request.")
        if not self.gemma_mediapipe_client or not self.gemma_mediapipe_client.model_loaded:
            logger.error("Local Gemma client is not available for text chat.")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model="gemma-3n-e4b-mediapipe",
                response=None,
                error="Local Gemma client is not available or model not loaded."
            )
        
        response_text = self.gemma_mediapipe_client.generate_response(
            request.prompt
        )
        error_message = None
        if isinstance(response_text, str) and "Error:" in response_text:
            error_message = response_text
            response_text = None

        return ChatResponse(
            provider=LLMProvider.GEMMA_LOCAL,
            model="gemma-3n-e4b-mediapipe",
            response=response_text,
            error=error_message
        )

# Instantiate the service for use in API endpoints
llm_service = LLMService() 