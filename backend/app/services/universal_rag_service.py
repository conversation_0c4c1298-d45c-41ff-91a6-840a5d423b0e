import logging
import asyncio
from typing import Dict, Any, Optional
from core.config import settings
from services.rag_service import rag_service
from llm_clients.openai_client import OpenAIClient
from llm_clients.gemini_client import GeminiClient
from llm_clients.deepseek_client import DeepSeekClient
from llm_clients.gemma_client import GemmaLocal<PERSON>lient
from llm_clients.gemma_3_4b_client import Gemma3_4BLocalClient
from llm_clients.ollama_client import OllamaClient
from llm_clients.jan_nano_client import JanNanoClient
from services.gemma3n_mediapipe_service import gemma3n_mediapipe_service
from models.llm_models import ChatResponse, LLMProvider, ChatRequest
from models.rag_models import RAGQueryRequest

logger = logging.getLogger(__name__)

class UniversalRAGService:
    """
    Universal RAG Service that supports all models (local and external APIs)
    """
    
    def __init__(self):
        logger.info("Initializing Universal RAG Service...")
        
        # Initialize all LLM clients
        self.clients = {}
        self._initialize_clients()
        
        # Model mapping for easy access
        self.model_mapping = {
            # Local models
            "gemma-3n-e4b-mediapipe": "gemma_mediapipe",
            "gemma-2b-it": "gemma_local",
            "gemma-3-4b": "gemma_3_4b",
            
            # Jan-nano models (tool use specialists)
            "jan-nano:q3_k_s": "jan_nano",
            "jan-nano:q4_k_m": "jan_nano", 
            "jan-nano:q5_k_m": "jan_nano",
            "jan-nano:q6_k": "jan_nano",
            "jan-nano:q8_0": "jan_nano",
            "jan-nano": "jan_nano",  # Default variant
            
            # Ollama models
            "llama3.1:8b": "ollama",
            "llama3:8b": "ollama",
            "gemma2:9b": "ollama",
            "gemma2:2b": "ollama",
            "gemma:2b": "ollama",
            
            # OpenAI models
            "gpt-3.5-turbo": "openai",
            "gpt-4": "openai", 
            "gpt-4o": "openai",
            "gpt-4-turbo": "openai",
            
            # Google models
            "gemini-pro": "gemini",
            "gemini-1.5-flash": "gemini",
            "gemini-1.5-flash-latest": "gemini",
            "gemini-1.5-pro": "gemini",
            
            # DeepSeek models
            "deepseek-chat": "deepseek",
            "deepseek-coder": "deepseek"
        }
        
        logger.info(f"Universal RAG Service initialized with {len(self.clients)} clients")
    
    def _initialize_clients(self):
        """Initialize all available LLM clients"""
        
        # OpenAI Client
        if settings.OPENAI_API_KEY:
            try:
                self.clients["openai"] = OpenAIClient()
                logger.info("✅ OpenAI client initialized")
            except Exception as e:
                logger.warning(f"❌ OpenAI client failed: {e}")
        else:
            logger.warning("⚠️ OpenAI API key not found")
        
        # Gemini Client
        if settings.GEMINI_API_KEY:
            try:
                self.clients["gemini"] = GeminiClient()
                logger.info("✅ Gemini client initialized")
            except Exception as e:
                logger.warning(f"❌ Gemini client failed: {e}")
        else:
            logger.warning("⚠️ Gemini API key not found")
        
        # DeepSeek Client
        if settings.DEEPSEEK_API_KEY:
            try:
                self.clients["deepseek"] = DeepSeekClient()
                logger.info("✅ DeepSeek client initialized")
            except Exception as e:
                logger.warning(f"❌ DeepSeek client failed: {e}")
        else:
            logger.warning("⚠️ DeepSeek API key not found")
        
        # Local Gemma MediaPipe (already initialized)
        if gemma3n_mediapipe_service and gemma3n_mediapipe_service.model_loaded:
            self.clients["gemma_mediapipe"] = gemma3n_mediapipe_service
            logger.info("✅ Gemma 3N MediaPipe client available")
        else:
            logger.warning("⚠️ Gemma 3N MediaPipe not available")
        
        # Local Gemma 2B
        try:
            self.clients["gemma_local"] = GemmaLocalClient()
            logger.info("✅ Gemma 2B local client initialized")
        except Exception as e:
            logger.warning(f"❌ Gemma 2B local client failed: {e}")
        
        # Local Gemma 3 4B
        try:
            self.clients["gemma_3_4b"] = Gemma3_4BLocalClient()
            logger.info("✅ Gemma 3 4B local client initialized")
        except Exception as e:
            logger.warning(f"❌ Gemma 3 4B local client failed: {e}")
        
        # Ollama Client
        try:
            self.clients["ollama"] = OllamaClient()
            logger.info("✅ Ollama client initialized")
        except Exception as e:
            logger.warning(f"❌ Ollama client failed: {e}")
        
        # Jan-nano Client (tool use specialist)
        try:
            self.clients["jan_nano"] = JanNanoClient()
            logger.info("✅ Jan-nano client initialized")
        except Exception as e:
            logger.warning(f"❌ Jan-nano client failed: {e}")
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get list of all available models with RAG support"""
        models = []
        
        for model_id, client_type in self.model_mapping.items():
            if client_type in self.clients:
                models.append({
                    "id": f"{model_id}-rag",
                    "object": "model",
                    "created": 1704067200,
                    "owned_by": f"{client_type}-rag",
                    "rag_enabled": True,
                    "original_model": model_id
                })
        
        return {
            "object": "list",
            "data": models
        }
    
    async def query_with_rag(self, request: RAGQueryRequest) -> ChatResponse:
        """
        Universal RAG query that works with any model
        """
        if not rag_service.is_ready():
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model="rag_unavailable",
                response=None,
                error="RAG service is not ready"
            )
        
        # Extract model name (remove -rag suffix if present)
        model_name = request.model or "gemma-3n-e4b-mediapipe"
        if model_name.endswith("-rag"):
            model_name = model_name[:-4]
        
        logger.info(f"Processing RAG query for model: {model_name}")
        
        # 1. Get relevant context from RAG
        context = await self._get_rag_context(request.prompt, request.top_k)
        
        # 2. Create augmented prompt
        augmented_prompt = self._create_augmented_prompt(request.prompt, context)
        
        # 3. Route to appropriate model
        try:
            response = await self._route_to_model(model_name, augmented_prompt)
            
            # Mark response as RAG-enhanced
            if response.model:
                response.model = f"{response.model}-rag"
            
            return response
            
        except Exception as e:
            logger.error(f"Error in RAG query for {model_name}: {e}")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=f"{model_name}-rag-error",
                response=None,
                error=f"RAG query failed: {str(e)}"
            )
    
    async def _get_rag_context(self, query: str, top_k: Optional[int] = None) -> str:
        """Get relevant context from RAG store"""
        try:
            # Generate embedding for query
            query_embedding_list = await rag_service.embedding_generator.generate_embeddings([query])
            if not query_embedding_list or not query_embedding_list[0]:
                return "No context available - embedding generation failed."
            
            # Search for relevant chunks
            k = top_k or settings.RAG_TOP_K_RESULTS
            retrieved_chunks = await rag_service.vector_store.search_similar_chunks(
                query_embedding_list[0], top_k=k
            )
            
            if not retrieved_chunks:
                return "No relevant context found in knowledge base."
            
            # Format context
            context = "\n\n---\nRelevant Context from Knowledge Base:\n"
            for i, chunk in enumerate(retrieved_chunks):
                context += f"Document {i+1} (Source: {chunk.metadata.filename}):\n{chunk.text}\n\n"
            context += "---\n"
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting RAG context: {e}")
            return "Error retrieving context from knowledge base."
    
    def _create_augmented_prompt(self, original_prompt: str, context: str) -> str:
        """Create augmented prompt with context"""
        return f"""{context}

User Query: {original_prompt}

Instructions: Based on the provided context from the knowledge base and your general knowledge, please provide a comprehensive answer to the user's query. If the context is relevant, prioritize information from the knowledge base. If no relevant context is available, answer based on your general knowledge."""
    
    async def _route_to_model(self, model_name: str, prompt: str) -> ChatResponse:
        """Route request to appropriate model client"""
        
        # Get client type for this model
        client_type = self.model_mapping.get(model_name)
        if not client_type:
            raise ValueError(f"Unknown model: {model_name}")
        
        # Get client instance
        client = self.clients.get(client_type)
        if not client:
            raise ValueError(f"Client not available for {client_type}")
        
        # Route to specific client
        if client_type == "openai":
            return await self._call_openai(client, model_name, prompt)
        elif client_type == "gemini":
            return await self._call_gemini(client, model_name, prompt)
        elif client_type == "deepseek":
            return await self._call_deepseek(client, model_name, prompt)
        elif client_type == "ollama":
            return await self._call_ollama(client, model_name, prompt)
        elif client_type == "gemma_mediapipe":
            return await self._call_gemma_mediapipe(client, model_name, prompt)
        elif client_type == "gemma_local":
            return await self._call_gemma_local(client, model_name, prompt)
        elif client_type == "gemma_3_4b":
            return await self._call_gemma_3_4b(client, model_name, prompt)
        elif client_type == "jan_nano":
            return await self._call_jan_nano(client, model_name, prompt)
        else:
            raise ValueError(f"Unknown client type: {client_type}")
    
    async def _call_openai(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call OpenAI API"""
        try:
            response_text = await client.generate_response(prompt, model=model_name)
            return ChatResponse(
                provider=LLMProvider.OPENAI,
                model=model_name,
                response=response_text,
                error=None
            )
        except Exception as e:
            logger.error(f"OpenAI call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.OPENAI,
                model=model_name,
                response=None,
                error=f"OpenAI API error: {str(e)}"
            )
    
    async def _call_gemini(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call Gemini API"""
        try:
            response_text = await client.generate_response(prompt)
            return ChatResponse(
                provider=LLMProvider.GEMINI,
                model=model_name,
                response=response_text,
                error=None
            )
        except Exception as e:
            logger.error(f"Gemini call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.GEMINI,
                model=model_name,
                response=None,
                error=f"Gemini API error: {str(e)}"
            )
    
    async def _call_deepseek(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call DeepSeek API"""
        try:
            response_text = await client.generate_response(prompt, model=model_name)
            return ChatResponse(
                provider=LLMProvider.DEEPSEEK,
                model=model_name,
                response=response_text,
                error=None
            )
        except Exception as e:
            logger.error(f"DeepSeek call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.DEEPSEEK,
                model=model_name,
                response=None,
                error=f"DeepSeek API error: {str(e)}"
            )
    
    async def _call_ollama(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call Ollama local model"""
        try:
            return await client.chat_completion(model_name, prompt)
        except Exception as e:
            logger.error(f"Ollama call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.OLLAMA,
                model=model_name,
                response=None,
                error=f"Ollama error: {str(e)}"
            )
    
    async def _call_gemma_mediapipe(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call Gemma MediaPipe local model"""
        try:
            response_text = client.generate_response(prompt)
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=model_name,
                response=response_text,
                error=None
            )
        except Exception as e:
            logger.error(f"Gemma MediaPipe call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=model_name,
                response=None,
                error=f"Gemma MediaPipe error: {str(e)}"
            )
    
    async def _call_gemma_local(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call Gemma 2B local model"""
        try:
            response_text = await client.generate_text_response(prompt)
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=model_name,
                response=response_text,
                error=None
            )
        except Exception as e:
            logger.error(f"Gemma local call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=model_name,
                response=None,
                error=f"Gemma local error: {str(e)}"
            )
    
    async def _call_gemma_3_4b(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call Gemma 3 4B local model"""
        try:
            response_text = await client.generate_text_response(prompt)
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=model_name,
                response=response_text,
                error=None
            )
        except Exception as e:
            logger.error(f"Gemma 3 4B call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.GEMMA_LOCAL,
                model=model_name,
                response=None,
                error=f"Gemma 3 4B error: {str(e)}"
            )
    
    async def _call_jan_nano(self, client, model_name: str, prompt: str) -> ChatResponse:
        """Call Jan-nano model (tool use specialist)"""
        try:
            # Extract variant from model name (e.g., jan-nano:q4_k_m -> q4_k_m)
            variant = "q4_k_m"  # Default variant
            if ":" in model_name:
                variant = model_name.split(":")[-1]
            
            # Use Jan-nano's chat completion with variant
            response = await client.chat_completion(prompt, model_variant=variant)
            
            # Jan-nano client already returns ChatResponse format
            return response
            
        except Exception as e:
            logger.error(f"Jan-nano call failed: {e}")
            return ChatResponse(
                provider=LLMProvider.OLLAMA,
                model=model_name,
                response=None,
                error=f"Jan-nano error: {str(e)}"
            )

# Singleton instance
universal_rag_service = UniversalRAGService() 