"""
Depreciation RAG Service
Integrates depreciation table data into the RAG system for LLM access
"""

import sqlite3
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class DepreciationRAGService:
    """Service to provide depreciation table data for RAG queries"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            self.db_path = Path(__file__).parent.parent / "rag" / "enhanced_tables.db"
        else:
            self.db_path = Path(db_path)
    
    def get_depreciation_context(self, query: str = None) -> str:
        """
        Get depreciation context for RAG system
        Returns formatted text that can be used as context for LLM responses
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Get all depreciation tables
            cursor.execute('''
                SELECT table_type, confidence_score, content, headers, metadata
                FROM enhanced_table_chunks 
                WHERE content LIKE '%khấu hao%' OR content LIKE '%depreciat%'
                ORDER BY confidence_score DESC
            ''')
            
            results = cursor.fetchall()
            
            if not results:
                return "Không tìm thấy thông tin về khấu hao trong cơ sở dữ liệu."
            
            # Build context
            context_parts = []
            context_parts.append("# THÔNG TIN KHẤU HAO TÀI SẢN CỐ ĐỊNH - MOBIFONE (VB 94)")
            context_parts.append("=" * 60)
            context_parts.append("")
            
            # Add summary
            context_parts.append(f"📊 TỔNG QUAN:")
            context_parts.append(f"- Tổng số bảng khấu hao: {len(results)}")
            context_parts.append(f"- Nguồn: VB_DEN_94_Quy trình QL tài sản cố định và công cụ dụng cụ lần 5")
            context_parts.append("")
            
            # Add key regulations
            regulations = set()
            for _, _, content, _, _ in results:
                if "TT-BTC" in content:
                    lines = content.split('\n')
                    for line in lines:
                        if "TT-BTC" in line and line.strip():
                            regulations.add(line.strip())
            
            if regulations:
                context_parts.append("🏛️ CĂN CỨ PHÁP LÝ:")
                for reg in list(regulations)[:3]:  # Top 3 regulations
                    context_parts.append(f"- {reg}")
                context_parts.append("")
            
            # Add table contents grouped by type
            table_types = {}
            for table_type, confidence, content, headers, metadata in results:
                if table_type not in table_types:
                    table_types[table_type] = []
                table_types[table_type].append((confidence, content, headers))
            
            for table_type, tables in table_types.items():
                context_parts.append(f"## 📋 {table_type.upper()}:")
                
                # Show top 2 tables of each type
                for i, (confidence, content, headers) in enumerate(tables[:2], 1):
                    context_parts.append(f"### Bảng {i} (Độ tin cậy: {confidence:.2f}):")
                    
                    # Extract key information from content
                    lines = content.split('\n')
                    key_lines = []
                    for line in lines:
                        line = line.strip()
                        if (line and 
                            not line.startswith('BẢNG DỮ LIỆU:') and 
                            not line.startswith('Loại:') and
                            not line.startswith('TÓM TẮT:') and
                            '|' not in line):
                            key_lines.append(line)
                    
                    # Show first few key lines
                    for line in key_lines[:5]:
                        context_parts.append(f"- {line}")
                    
                    context_parts.append("")
            
            # Add specific search results if query provided
            if query:
                context_parts.append("## 🔍 KẾT QUẢ TÌM KIẾM LIÊN QUAN:")
                search_results = self._search_depreciation_content(query)
                if search_results:
                    for i, result in enumerate(search_results[:3], 1):
                        context_parts.append(f"### Kết quả {i}:")
                        context_parts.append(f"- Loại: {result['table_type']}")
                        context_parts.append(f"- Độ tin cậy: {result['confidence']:.2f}")
                        for line in result['relevant_lines']:
                            context_parts.append(f"- {line}")
                        context_parts.append("")
            
            conn.close()
            return '\n'.join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting depreciation context: {e}")
            return f"Lỗi khi truy xuất thông tin khấu hao: {e}"
    
    def _search_depreciation_content(self, query: str) -> List[Dict[str, Any]]:
        """Search for specific content in depreciation tables"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            search_pattern = f"%{query}%"
            cursor.execute('''
                SELECT table_type, confidence_score, content
                FROM enhanced_table_chunks 
                WHERE (content LIKE '%khấu hao%' OR content LIKE '%depreciat%')
                  AND content LIKE ?
                ORDER BY confidence_score DESC
                LIMIT 5
            ''', (search_pattern,))
            
            results = cursor.fetchall()
            
            search_results = []
            for table_type, confidence, content in results:
                # Extract lines containing the query
                lines = content.split('\n')
                relevant_lines = []
                for line in lines:
                    if query.lower() in line.lower() and line.strip():
                        relevant_lines.append(line.strip())
                
                if relevant_lines:
                    search_results.append({
                        'table_type': table_type,
                        'confidence': confidence,
                        'relevant_lines': relevant_lines[:3]  # Top 3 relevant lines
                    })
            
            conn.close()
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching depreciation content: {e}")
            return []
    
    def get_depreciation_summary(self) -> Dict[str, Any]:
        """Get a summary of depreciation information"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Get statistics
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_count,
                    AVG(confidence_score) as avg_confidence,
                    MAX(confidence_score) as max_confidence
                FROM enhanced_table_chunks 
                WHERE content LIKE '%khấu hao%' OR content LIKE '%depreciat%'
            ''')
            
            stats = cursor.fetchone()
            
            # Get table types
            cursor.execute('''
                SELECT table_type, COUNT(*) as count
                FROM enhanced_table_chunks 
                WHERE content LIKE '%khấu hao%' OR content LIKE '%depreciat%'
                GROUP BY table_type
                ORDER BY count DESC
            ''')
            
            type_breakdown = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                'total_tables': stats[0],
                'average_confidence': round(stats[1], 2) if stats[1] else 0,
                'max_confidence': stats[2],
                'table_types': type_breakdown,
                'summary': f"Tìm thấy {stats[0]} bảng khấu hao với độ tin cậy trung bình {round(stats[1], 2) if stats[1] else 0}"
            }
            
        except Exception as e:
            logger.error(f"Error getting depreciation summary: {e}")
            return {'error': str(e)}

# Global service instance
depreciation_service = DepreciationRAGService()

def get_depreciation_context_for_llm(query: str = None) -> str:
    """
    Main function to get depreciation context for LLM
    This can be called from other parts of the system
    """
    return depreciation_service.get_depreciation_context(query) 