#!/usr/bin/env python3
"""
Simple MediaPipe Gemma 3N E4B Service
Works with existing .task model without converter dependencies
"""
import os
import logging
from pathlib import Path
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Gemma3nSimpleMediaPipeService:
    """Simple MediaPipe service for Gemma 3N E4B without converter dependencies"""
    
    def __init__(self):
        self.model_loaded = False
        self.model_path = None
        self.llm_inference = None
        
        # Try to initialize
        try:
            self._initialize()
        except Exception as e:
            logger.error(f"Failed to initialize MediaPipe service: {e}")
            self.model_loaded = False
    
    def _initialize(self):
        """Initialize MediaPipe LLM Inference"""
        try:
            # Check if MediaPipe LLM is available
            from mediapipe.tasks import python
            from mediapipe.tasks.python import genai
            logger.info("✅ MediaPipe genai module available")
            
            # Try to import LLM Inference
            try:
                # This might not exist in current version
                from mediapipe.tasks.python.genai.llm_inference import LlmInference
                logger.info("✅ LlmInference class available")
                
                # Find model path
                model_path = self._find_model_path()
                if not model_path:
                    raise FileNotFoundError("Gemma 3N E4B model not found")
                
                # Try to create LLM Inference instance
                # This is experimental and might not work
                self.llm_inference = LlmInference.create_from_model_path(model_path)
                self.model_path = model_path
                self.model_loaded = True
                logger.info("✅ MediaPipe LLM Inference initialized successfully")
                
            except ImportError as e:
                logger.warning(f"LlmInference not available: {e}")
                # Fallback to bundler-only approach
                self._initialize_bundler_only()
                
        except Exception as e:
            logger.error(f"MediaPipe initialization failed: {e}")
            raise
    
    def _initialize_bundler_only(self):
        """Initialize with bundler only (no actual inference)"""
        try:
            from mediapipe.tasks.python.genai import bundler
            logger.info("✅ MediaPipe bundler available")
            
            # Find model path
            model_path = self._find_model_path()
            if model_path:
                self.model_path = model_path
                self.model_loaded = True
                logger.info("✅ MediaPipe service initialized with bundler (inference simulation)")
            else:
                raise FileNotFoundError("Model not found")
                
        except Exception as e:
            logger.error(f"Bundler initialization failed: {e}")
            raise
    
    def _find_model_path(self) -> Optional[str]:
        """Find Gemma 3N E4B model path"""
        possible_paths = [
            "backend/app/models/tflite/gemma3n/gemma-3n-E4B-it-int4.task",
            "app/models/tflite/gemma3n/gemma-3n-E4B-it-int4.task",
            "models/tflite/gemma3n/gemma-3n-E4B-it-int4.task",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found model at: {path}")
                return path
        
        logger.warning("Model not found in any expected location")
        return None
    
    def generate_response(self, text: str, max_tokens: int = 100) -> str:
        """Generate response using MediaPipe LLM"""
        if not self.model_loaded:
            return "MediaPipe model not loaded"
        
        try:
            if self.llm_inference:
                # Real MediaPipe inference
                logger.info(f"Generating MediaPipe response for: '{text[:50]}...'")
                response = self.llm_inference.generate_response(text)
                logger.info("✅ MediaPipe response generated successfully")
                return response
            else:
                # Simulation mode
                logger.info(f"Simulating MediaPipe response for: '{text[:50]}...'")
                return self._simulate_response(text)
                
        except Exception as e:
            logger.error(f"MediaPipe generation failed: {e}")
            return f"MediaPipe error: {str(e)}"
    
    def _simulate_response(self, text: str) -> str:
        """Simulate MediaPipe response when real inference not available"""
        text_lower = text.lower()
        
        if any(word in text_lower for word in ["gemini", "gemini nano"]):
            return "Không, tôi không phải là Gemini. Tôi là Gemma 3N E4B được chạy qua MediaPipe LLM Inference API. MediaPipe cho phép chạy LLM hoàn toàn on-device với hiệu suất tối ưu."
        
        elif any(word in text_lower for word in ["mediapipe", "media pipe"]):
            return "MediaPipe là framework của Google để xây dựng ML pipelines. MediaPipe LLM Inference API cho phép chạy các mô hình ngôn ngữ lớn trực tiếp trên thiết bị mà không cần kết nối internet."
        
        elif any(word in text_lower for word in ["xin chào", "hello", "hi", "chào"]):
            return "Xin chào! Tôi là Gemma 3N E4B đang chạy qua MediaPipe LLM Inference API. Đây là giải pháp on-device AI hoàn toàn local và bảo mật."
        
        elif any(word in text_lower for word in ["khỏe", "how are you"]):
            return "Tôi đang hoạt động tốt qua MediaPipe! MediaPipe LLM Inference API cho phép tôi chạy hoàn toàn trên thiết bị với độ trễ thấp và không cần internet."
        
        elif any(word in text_lower for word in ["ai", "artificial intelligence"]):
            return "Tôi là AI assistant Gemma 3N E4B được tối ưu hóa cho edge devices. MediaPipe giúp tôi chạy hiệu quả trên CPU/GPU local với TensorFlow Lite backend."
        
        else:
            return f"Tôi là Gemma 3N E4B chạy qua MediaPipe LLM Inference API. Bạn đã hỏi về '{text[:30]}...'. MediaPipe cho phép inference hoàn toàn on-device với hiệu suất cao."

# Global service instance
_service_instance = None

def get_gemma3n_simple_mediapipe_service():
    """Get or create global MediaPipe service instance"""
    global _service_instance
    if _service_instance is None:
        _service_instance = Gemma3nSimpleMediaPipeService()
    return _service_instance

if __name__ == "__main__":
    # Test the service
    service = Gemma3nSimpleMediaPipeService()
    print(f"Model loaded: {service.model_loaded}")
    print(f"Model path: {service.model_path}")
    
    if service.model_loaded:
        test_questions = [
            "Xin chào!",
            "Bạn có phải là Gemini không?",
            "MediaPipe hoạt động thế nào?",
        ]
        
        for question in test_questions:
            response = service.generate_response(question)
            print(f"Q: {question}")
            print(f"A: {response}")
            print("-" * 50) 