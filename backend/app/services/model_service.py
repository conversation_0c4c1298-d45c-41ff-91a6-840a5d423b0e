from typing import Dict, Any, Optional
import json
from pathlib import Path
import logging
import aiohttp
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class ModelService:
    def __init__(self):
        self.settings_file = Path("data/model_settings.json")
        self.settings_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Default settings
        self._settings = {
            "selected": "balanced",
            "context_length": 32768,
            "max_tokens": 4096,
            "external_models": [
                {
                    "name": "Local Llama",
                    "endpoint": "http://localhost:11434/v1",
                    "api_key": "",
                    "model": "llama2",
                    "enabled": True
                }
            ],
            "custom_endpoint": {
                "enabled": False,
                "url": "",
                "api_key": "",
                "model": "",
                "timeout": 30000,
                "max_retries": 3,
                "concurrent_requests": 5
            }
        }
        
        # Initialize session pool
        self._session_pool = None
        self._semaphore = None
        
        # Load existing settings if available
        self._load_settings()

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session pool"""
        if self._session_pool is None:
            self._session_pool = aiohttp.ClientSession()
        return self._session_pool

    async def _get_semaphore(self) -> asyncio.Semaphore:
        """Get or create semaphore for concurrent requests"""
        if self._semaphore is None:
            max_concurrent = self._settings["custom_endpoint"].get("concurrent_requests", 5)
            self._semaphore = asyncio.Semaphore(max_concurrent)
        return self._semaphore

    def _load_settings(self):
        """Load settings from file if it exists"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file) as f:
                    stored_settings = json.load(f)
                    self._settings.update(stored_settings)
        except Exception as e:
            logger.error(f"Error loading model settings: {e}")

    def _save_settings(self):
        """Save current settings to file"""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self._settings, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving model settings: {e}")

    def get_settings(self) -> Dict[str, Any]:
        """Get current model settings"""
        return self._settings

    def update_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update model settings"""
        # Validate required fields
        required_fields = ["selected", "context_length", "max_tokens", "external_models", "custom_endpoint"]
        for field in required_fields:
            if field not in settings:
                raise ValueError(f"Missing required field: {field}")

        # Validate values
        if settings["context_length"] < 1024 or settings["context_length"] > 32768:
            raise ValueError("context_length must be between 1024 and 32768")
        
        if settings["max_tokens"] < 256 or settings["max_tokens"] > 4096:
            raise ValueError("max_tokens must be between 256 and 4096")

        # Validate external models
        for model in settings["external_models"]:
            required_model_fields = ["name", "endpoint", "api_key", "model", "enabled"]
            for field in required_model_fields:
                if field not in model:
                    raise ValueError(f"Missing required field '{field}' in external model configuration")

        # Validate custom endpoint
        required_endpoint_fields = ["enabled", "url", "api_key", "model", "timeout", "max_retries", "concurrent_requests"]
        for field in required_endpoint_fields:
            if field not in settings["custom_endpoint"]:
                raise ValueError(f"Missing required field '{field}' in custom endpoint configuration")

        # Validate performance settings
        custom_endpoint = settings["custom_endpoint"]
        if custom_endpoint["timeout"] < 1000 or custom_endpoint["timeout"] > 300000:
            raise ValueError("timeout must be between 1000 and 300000 ms")
        
        if custom_endpoint["max_retries"] < 0 or custom_endpoint["max_retries"] > 5:
            raise ValueError("max_retries must be between 0 and 5")
        
        if custom_endpoint["concurrent_requests"] < 1 or custom_endpoint["concurrent_requests"] > 50:
            raise ValueError("concurrent_requests must be between 1 and 50")

        # Update settings and recreate session pool if needed
        if self._settings.get("custom_endpoint", {}).get("concurrent_requests") != custom_endpoint["concurrent_requests"]:
            self._semaphore = None

        self._settings.update(settings)
        self._save_settings()
        return self._settings

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def test_model_connection(self, endpoint: str, api_key: str, model: str) -> bool:
        """Test connection to a model endpoint with retry logic"""
        try:
            session = await self._get_session()
            semaphore = await self._get_semaphore()

            headers = {
                "Content-Type": "application/json"
            }
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            async with semaphore:
                async with session.post(
                    f"{endpoint}/chat/completions",
                    headers=headers,
                    json={
                        "model": model,
                        "messages": [{"role": "user", "content": "Hello"}],
                        "max_tokens": 5
                    },
                    timeout=aiohttp.ClientTimeout(total=self._settings["custom_endpoint"]["timeout"] / 1000)
                ) as response:
                    if response.status == 200:
                        return True
                    else:
                        logger.error(f"Model test failed: {await response.text()}")
                        return False
        except Exception as e:
            logger.error(f"Error testing model connection: {e}")
            return False

    async def close(self):
        """Close the session pool"""
        if self._session_pool:
            await self._session_pool.close()
            self._session_pool = None 