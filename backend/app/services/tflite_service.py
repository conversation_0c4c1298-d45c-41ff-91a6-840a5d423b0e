"""
TensorFlow Lite Service for AI Assistant <PERSON><PERSON>
Provides lightweight ML inference capabilities for image processing,
document analysis, and preprocessing tasks.
"""

import os
import io
import logging
import numpy as np
from typing import Optional, Dict, Any, List
from PIL import Image

try:
    import tensorflow as tf
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

from core.config import settings

logger = logging.getLogger(__name__)

class TensorFlowLiteService:
    """TensorFlow Lite inference service for various ML tasks."""
    
    def __init__(self):
        self.settings = settings
        self.models: Dict[str, tf.lite.Interpreter] = {}
        self.model_configs: Dict[str, Dict[str, Any]] = {}
        
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available. TFLite service will be limited.")
            return
            
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize available TensorFlow Lite models."""
        models_dir = os.path.join(os.path.dirname(__file__), "..", "models", "tflite")
        
        if not os.path.exists(models_dir):
            os.makedirs(models_dir, exist_ok=True)
            logger.info(f"Created TFLite models directory: {models_dir}")
        
        # Model configurations
        self.model_configs = {
            "image_classifier": {
                "path": os.path.join(models_dir, "mobilenet_v2_1.0_224.tflite"),
                "input_size": (224, 224),
                "labels_file": os.path.join(models_dir, "imagenet_labels.txt"),
                "description": "MobileNet v2 image classifier"
            },
            "object_detector": {
                "path": os.path.join(models_dir, "detect.tflite"),
                "input_size": (300, 300),
                "labels_file": os.path.join(models_dir, "coco_labels.txt"),
                "description": "COCO object detection"
            },
            "text_classifier": {
                "path": os.path.join(models_dir, "text_classifier.tflite"),
                "description": "Text sentiment/classification"
            }
        }
        
        # Load available models
        for model_name, config in self.model_configs.items():
            model_path = config["path"]
            if os.path.exists(model_path):
                try:
                    interpreter = tf.lite.Interpreter(model_path=model_path)
                    interpreter.allocate_tensors()
                    self.models[model_name] = interpreter
                    logger.info(f"Loaded TFLite model: {model_name}")
                except Exception as e:
                    logger.error(f"Failed to load model {model_name}: {e}")
            else:
                logger.info(f"Model not found: {model_path}")
    
    def list_available_models(self) -> List[Dict[str, str]]:
        """List all available TensorFlow Lite models."""
        return [
            {
                "name": name,
                "description": config.get("description", ""),
                "loaded": name in self.models
            }
            for name, config in self.model_configs.items()
        ]
    
    def preprocess_image(self, image_data: bytes, target_size: tuple = (224, 224), dtype=np.uint8) -> np.ndarray:
        """Preprocess image data for TensorFlow Lite inference."""
        try:
            # Load image from bytes
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize image
            image = image.resize(target_size, Image.Resampling.LANCZOS)
            
            # Convert to numpy array 
            if dtype == np.uint8:
                img_array = np.array(image, dtype=np.uint8)
            else:  # float32
                img_array = np.array(image, dtype=np.float32)
                img_array = img_array / 255.0  # Normalize to [0, 1]
            
            # Add batch dimension
            img_array = np.expand_dims(img_array, axis=0)
            
            return img_array
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            raise
    
    def classify_image(self, image_data: bytes) -> Dict[str, Any]:
        """Classify image using TensorFlow Lite image classifier."""
        if "image_classifier" not in self.models:
            return {"error": "Image classifier model not available"}
        
        try:
            interpreter = self.models["image_classifier"]
            config = self.model_configs["image_classifier"]
            
            # Get input details to determine expected dtype
            input_details = interpreter.get_input_details()
            expected_dtype = input_details[0]['dtype']
            
            # Preprocess image with correct dtype
            input_data = self.preprocess_image(image_data, config["input_size"], expected_dtype)
            
            # Set input tensor
            interpreter.set_tensor(input_details[0]['index'], input_data)
            
            # Run inference
            interpreter.invoke()
            
            # Get output tensor
            output_details = interpreter.get_output_details()
            output_data = interpreter.get_tensor(output_details[0]['index'])
            
            # Get top predictions
            predictions = output_data[0]
            top_indices = np.argsort(predictions)[-5:][::-1]  # Top 5
            
            # Load labels if available
            labels = self._load_labels(config.get("labels_file"))
            
            results = []
            for idx in top_indices:
                confidence = float(predictions[idx])
                label = labels[idx] if labels and idx < len(labels) else f"Class_{idx}"
                results.append({
                    "label": label,
                    "confidence": confidence,
                    "class_id": int(idx)
                })
            
            return {
                "success": True,
                "predictions": results,
                "model": "image_classifier"
            }
            
        except Exception as e:
            logger.error(f"Error in image classification: {e}")
            return {"error": str(e)}
    
    def detect_objects(self, image_data: bytes) -> Dict[str, Any]:
        """Detect objects in image using TensorFlow Lite object detector."""
        if "object_detector" not in self.models:
            return {"error": "Object detector model not available"}
        
        try:
            interpreter = self.models["object_detector"]
            config = self.model_configs["object_detector"]
            
            # Get input details to determine expected dtype
            input_details = interpreter.get_input_details()
            expected_dtype = input_details[0]['dtype']
            
            # Preprocess image with correct dtype
            input_data = self.preprocess_image(image_data, config["input_size"], expected_dtype)
            
            # Set input tensor
            interpreter.set_tensor(input_details[0]['index'], input_data)
            
            # Run inference
            interpreter.invoke()
            
            # Get output tensors (assuming standard object detection format)
            output_details = interpreter.get_output_details()
            
            # Extract detection results
            boxes = interpreter.get_tensor(output_details[0]['index'])[0]  # Bounding boxes
            classes = interpreter.get_tensor(output_details[1]['index'])[0]  # Class IDs
            scores = interpreter.get_tensor(output_details[2]['index'])[0]  # Confidence scores
            
            # Load labels
            labels = self._load_labels(config.get("labels_file"))
            
            # Filter by confidence threshold
            confidence_threshold = 0.5
            detections = []
            
            for i in range(len(scores)):
                if scores[i] > confidence_threshold:
                    class_id = int(classes[i])
                    label = labels[class_id] if labels and class_id < len(labels) else f"Class_{class_id}"
                    
                    detections.append({
                        "label": label,
                        "confidence": float(scores[i]),
                        "bbox": [float(x) for x in boxes[i]],  # [y1, x1, y2, x2]
                        "class_id": class_id
                    })
            
            return {
                "success": True,
                "detections": detections,
                "model": "object_detector"
            }
            
        except Exception as e:
            logger.error(f"Error in object detection: {e}")
            return {"error": str(e)}
    
    def analyze_image_for_rag(self, image_data: bytes) -> Dict[str, Any]:
        """Analyze image for RAG system - extract text, objects, and descriptions."""
        results = {
            "image_classification": self.classify_image(image_data),
            "object_detection": self.detect_objects(image_data),
            "text_summary": ""
        }
        
        # Generate text summary for RAG
        summary_parts = []
        
        # Add classification results
        if results["image_classification"].get("success"):
            top_prediction = results["image_classification"]["predictions"][0]
            summary_parts.append(f"Image appears to be: {top_prediction['label']} (confidence: {top_prediction['confidence']:.2f})")
        
        # Add object detection results
        if results["object_detection"].get("success") and results["object_detection"]["detections"]:
            objects = [det["label"] for det in results["object_detection"]["detections"][:3]]  # Top 3
            summary_parts.append(f"Detected objects: {', '.join(objects)}")
        
        results["text_summary"] = ". ".join(summary_parts)
        
        return results
    
    def _load_labels(self, labels_file: Optional[str]) -> Optional[List[str]]:
        """Load labels from file."""
        if not labels_file or not os.path.exists(labels_file):
            return None
        
        try:
            with open(labels_file, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f.readlines()]
        except Exception as e:
            logger.error(f"Error loading labels from {labels_file}: {e}")
            return None
    
    def health_check(self) -> Dict[str, Any]:
        """Check health of TensorFlow Lite service."""
        return {
            "tensorflow_available": TENSORFLOW_AVAILABLE,
            "models_loaded": len(self.models),
            "available_models": self.list_available_models()
        }


# Global service instance
tflite_service = TensorFlowLiteService() 