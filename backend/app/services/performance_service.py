from typing import Dict, Any
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class PerformanceService:
    def __init__(self):
        self.settings_file = Path("data/performance_settings.json")
        self.settings_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Default settings
        self._settings = {
            "max_concurrent_uploads": 5,
            "max_concurrent_searches": 20,
            "max_workers": 4,
            "max_memory_usage": 2048,
            "preset": "production"
        }
        
        # Load existing settings if available
        self._load_settings()

    def _load_settings(self):
        """Load settings from file if it exists"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file) as f:
                    stored_settings = json.load(f)
                    self._settings.update(stored_settings)
        except Exception as e:
            logger.error(f"Error loading performance settings: {e}")

    def _save_settings(self):
        """Save current settings to file"""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self._settings, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving performance settings: {e}")

    def get_settings(self) -> Dict[str, Any]:
        """Get current performance settings"""
        return self._settings

    def update_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update performance settings"""
        # Validate required fields
        required_fields = ["max_concurrent_uploads", "max_concurrent_searches", 
                         "max_workers", "max_memory_usage", "preset"]
        for field in required_fields:
            if field not in settings:
                raise ValueError(f"Missing required field: {field}")

        # Validate values
        if settings["max_concurrent_uploads"] < 1 or settings["max_concurrent_uploads"] > 20:
            raise ValueError("max_concurrent_uploads must be between 1 and 20")
        
        if settings["max_concurrent_searches"] < 1 or settings["max_concurrent_searches"] > 50:
            raise ValueError("max_concurrent_searches must be between 1 and 50")
        
        if settings["max_workers"] < 1 or settings["max_workers"] > 8:
            raise ValueError("max_workers must be between 1 and 8")
        
        if settings["max_memory_usage"] < 512 or settings["max_memory_usage"] > 8192:
            raise ValueError("max_memory_usage must be between 512 and 8192")

        # Update settings
        self._settings.update(settings)
        self._save_settings()
        return self._settings 