"""
Open WebUI RAG Service
Integrates advanced RAG features from Open WebUI including:
- Knowledge Collections management
- Hybrid Search (BM25 + Vector similarity)
- CrossEncoder re-ranking  
- Full Context Mode
- Multi-format document processing
- Web content retrieval
"""

import os
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import tempfile
import shutil

# Core dependencies
import numpy as np
from sentence_transformers import SentenceTransformer, CrossEncoder
from rank_bm25 import BM25Okapi
import faiss

# Document processing
import fitz  # PyMuPDF
from docx import Document
import openpyxl
import markdown
from bs4 import BeautifulSoup
import html2text
import requests
import filetype
import chardet

# Text processing
from langchain_text_splitters import RecursiveCharacterTextSplitter, TokenTextSplitter
import tiktoken

# Models
from pydantic import BaseModel
from core.config import settings

logger = logging.getLogger(__name__)

class KnowledgeCollection(BaseModel):
    """Knowledge Collection model"""
    id: str
    name: str
    description: str = ""
    visibility: str = "private"  # private, public
    created_at: str
    updated_at: str
    documents: List[Dict] = []
    chunk_strategy: str = "recursive"  # recursive, semantic, token
    chunk_size: int = 512
    chunk_overlap: int = 50
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"

class DocumentChunk(BaseModel):
    """Document chunk with metadata"""
    id: str
    collection_id: str
    document_id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    bm25_score: Optional[float] = None
    similarity_score: Optional[float] = None
    rerank_score: Optional[float] = None

class RAGConfig(BaseModel):
    """RAG Configuration"""
    chunk_size: int = settings.OPENWEBUI_RAG_CHUNK_SIZE
    chunk_overlap: int = settings.OPENWEBUI_RAG_CHUNK_OVERLAP
    top_k: int = settings.OPENWEBUI_RAG_TOP_K
    similarity_threshold: float = settings.OPENWEBUI_RAG_SIMILARITY_THRESHOLD
    use_hybrid_search: bool = settings.OPENWEBUI_RAG_USE_HYBRID_SEARCH
    use_reranking: bool = settings.OPENWEBUI_RAG_USE_RERANKING
    full_context_mode: bool = settings.OPENWEBUI_RAG_FULL_CONTEXT_MODE
    embedding_model: str = settings.OPENWEBUI_RAG_EMBEDDING_MODEL
    rerank_model: str = settings.OPENWEBUI_RAG_RERANK_MODEL

class OpenWebUIRAGService:
    """Enhanced RAG Service with Open WebUI features"""
    
    def __init__(self):
        self.config = RAGConfig()
        self.collections: Dict[str, KnowledgeCollection] = {}
        self.embedding_model: Optional[SentenceTransformer] = None
        self.rerank_model: Optional[CrossEncoder] = None
        self.vector_index: Optional[faiss.IndexFlatIP] = None
        self.bm25_indices: Dict[str, BM25Okapi] = {}
        self.chunks: Dict[str, List[DocumentChunk]] = {}
        
        # Storage paths
        self.collections_dir = Path(settings.OPENWEBUI_RAG_COLLECTIONS_DIR)
        self.collections_dir.mkdir(exist_ok=True, parents=True)
        
        # Initialize models flag (will be initialized on first use)
        self._models_initialized = False
    
    async def _initialize_models(self):
        """Initialize embedding and reranking models"""
        if self._models_initialized:
            return
            
        try:
            logger.info("Initializing Open WebUI RAG models...")
            
            # Load embedding model
            self.embedding_model = SentenceTransformer(self.config.embedding_model)
            logger.info(f"Loaded embedding model: {self.config.embedding_model}")
            
            # Load reranking model if enabled
            if self.config.use_reranking:
                self.rerank_model = CrossEncoder(self.config.rerank_model)
                logger.info(f"Loaded reranking model: {self.config.rerank_model}")
            
            # Load existing collections
            await self._load_collections()
            
            self._models_initialized = True
            logger.info("Open WebUI RAG Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Open WebUI RAG models: {e}")
    
    async def _load_collections(self):
        """Load existing collections from disk"""
        try:
            collections_file = self.collections_dir / "collections.json"
            if collections_file.exists():
                with open(collections_file, 'r', encoding='utf-8') as f:
                    collections_data = json.load(f)
                
                for collection_data in collections_data:
                    collection = KnowledgeCollection(**collection_data)
                    self.collections[collection.id] = collection
                    
                    # Load collection chunks
                    await self._load_collection_chunks(collection.id)
                
                logger.info(f"Loaded {len(self.collections)} collections")
        
        except Exception as e:
            logger.error(f"Error loading collections: {e}")
    
    async def _load_collection_chunks(self, collection_id: str):
        """Load chunks for a specific collection"""
        try:
            chunks_file = self.collections_dir / f"{collection_id}_chunks.json"
            if chunks_file.exists():
                with open(chunks_file, 'r', encoding='utf-8') as f:
                    chunks_data = json.load(f)
                
                chunks = [DocumentChunk(**chunk_data) for chunk_data in chunks_data]
                self.chunks[collection_id] = chunks
                
                # Rebuild BM25 index
                if chunks:
                    corpus = [chunk.content for chunk in chunks]
                    tokenized_corpus = [doc.split() for doc in corpus]
                    self.bm25_indices[collection_id] = BM25Okapi(tokenized_corpus)
                
                logger.info(f"Loaded {len(chunks)} chunks for collection {collection_id}")
        
        except Exception as e:
            logger.error(f"Error loading chunks for collection {collection_id}: {e}")
    
    async def create_collection(self, name: str, description: str = "", visibility: str = "private") -> str:
        """Create a new knowledge collection"""
        import uuid
        from datetime import datetime
        
        collection_id = str(uuid.uuid4())
        collection = KnowledgeCollection(
            id=collection_id,
            name=name,
            description=description,
            visibility=visibility,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        self.collections[collection_id] = collection
        self.chunks[collection_id] = []
        
        await self._save_collections()
        
        logger.info(f"Created collection: {name} (ID: {collection_id})")
        return collection_id
    
    async def _save_collections(self):
        """Save collections to disk"""
        try:
            collections_data = [collection.dict() for collection in self.collections.values()]
            collections_file = self.collections_dir / "collections.json"
            
            with open(collections_file, 'w', encoding='utf-8') as f:
                json.dump(collections_data, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            logger.error(f"Error saving collections: {e}")
    
    async def _save_collection_chunks(self, collection_id: str):
        """Save chunks for a specific collection"""
        try:
            if collection_id in self.chunks:
                chunks_data = [chunk.dict() for chunk in self.chunks[collection_id]]
                chunks_file = self.collections_dir / f"{collection_id}_chunks.json"
                
                with open(chunks_file, 'w', encoding='utf-8') as f:
                    json.dump(chunks_data, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            logger.error(f"Error saving chunks for collection {collection_id}: {e}")
    
    async def process_document(self, collection_id: str, file_path: str, filename: str) -> Dict[str, Any]:
        """Process a document and add to collection"""
        try:
            # Extract text from document
            text_content = await self._extract_text_from_file(file_path, filename)
            
            if not text_content.strip():
                return {"status": "error", "message": "No text content extracted from document"}
            
            # Get collection
            if collection_id not in self.collections:
                return {"status": "error", "message": f"Collection {collection_id} not found"}
            
            collection = self.collections[collection_id]
            
            # Create document chunks
            chunks = await self._create_document_chunks(
                text_content, 
                collection_id, 
                filename, 
                collection.chunk_strategy,
                collection.chunk_size,
                collection.chunk_overlap
            )
            
            # Generate embeddings
            if self.embedding_model:
                for chunk in chunks:
                    embedding = self.embedding_model.encode(chunk.content)
                    chunk.embedding = embedding.tolist()
            
            # Add to collection
            if collection_id not in self.chunks:
                self.chunks[collection_id] = []
            
            self.chunks[collection_id].extend(chunks)
            
            # Update BM25 index
            await self._update_bm25_index(collection_id)
            
            # Update collection metadata
            collection.documents.append({
                "filename": filename,
                "chunks_count": len(chunks),
                "processed_at": __import__('datetime').datetime.now().isoformat()
            })
            collection.updated_at = __import__('datetime').datetime.now().isoformat()
            
            # Save to disk
            await self._save_collections()
            await self._save_collection_chunks(collection_id)
            
            logger.info(f"Processed document {filename}: {len(chunks)} chunks created")
            
            return {
                "status": "success",
                "message": f"Document processed successfully: {len(chunks)} chunks created",
                "chunks_count": len(chunks),
                "collection_id": collection_id
            }
        
        except Exception as e:
            logger.error(f"Error processing document {filename}: {e}")
            return {"status": "error", "message": f"Error processing document: {str(e)}"}
    
    async def _extract_text_from_file(self, file_path: str, filename: str) -> str:
        """Extract text from various file formats"""
        try:
            # Detect file type
            kind = filetype.guess(file_path)
            file_extension = Path(filename).suffix.lower()
            
            if kind and kind.mime.startswith('application/pdf') or file_extension == '.pdf':
                return await self._extract_pdf_text(file_path)
            elif file_extension in ['.docx', '.doc']:
                return await self._extract_docx_text(file_path)
            elif file_extension in ['.xlsx', '.xls']:
                return await self._extract_excel_text(file_path)
            elif file_extension in ['.md', '.markdown']:
                return await self._extract_markdown_text(file_path)
            elif file_extension in ['.html', '.htm']:
                return await self._extract_html_text(file_path)
            else:
                # Try as plain text
                return await self._extract_plain_text(file_path)
        
        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {e}")
            return ""
    
    async def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF"""
        try:
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
        except Exception as e:
            logger.error(f"Error extracting PDF text: {e}")
            return ""
    
    async def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            return ""
    
    async def _extract_excel_text(self, file_path: str) -> str:
        """Extract text from Excel"""
        try:
            workbook = openpyxl.load_workbook(file_path)
            text = ""
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"\n--- Sheet: {sheet_name} ---\n"
                for row in sheet.iter_rows(values_only=True):
                    row_text = [str(cell) if cell is not None else "" for cell in row]
                    text += " | ".join(row_text) + "\n"
            return text
        except Exception as e:
            logger.error(f"Error extracting Excel text: {e}")
            return ""
    
    async def _extract_markdown_text(self, file_path: str) -> str:
        """Extract text from Markdown"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # Convert markdown to HTML then to text
            html = markdown.markdown(md_content)
            soup = BeautifulSoup(html, 'html.parser')
            return soup.get_text()
        except Exception as e:
            logger.error(f"Error extracting Markdown text: {e}")
            return ""
    
    async def _extract_html_text(self, file_path: str) -> str:
        """Extract text from HTML"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.ignore_images = True
            return h.handle(html_content)
        except Exception as e:
            logger.error(f"Error extracting HTML text: {e}")
            return ""
    
    async def _extract_plain_text(self, file_path: str) -> str:
        """Extract plain text with encoding detection"""
        try:
            # Detect encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                encoding = chardet.detect(raw_data)['encoding']
            
            # Read with detected encoding
            with open(file_path, 'r', encoding=encoding or 'utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error extracting plain text: {e}")
            return ""
    
    async def _create_document_chunks(self, text: str, collection_id: str, filename: str, 
                                      strategy: str, chunk_size: int, chunk_overlap: int) -> List[DocumentChunk]:
        """Create document chunks using specified strategy"""
        import uuid
        
        chunks = []
        
        try:
            if strategy == "token":
                # Token-based splitting
                encoding = tiktoken.get_encoding("cl100k_base")
                splitter = TokenTextSplitter(
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    encoding_name="cl100k_base"
                )
            else:
                # Recursive character splitting (default)
                splitter = RecursiveCharacterTextSplitter(
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    separators=["\n\n", "\n", " ", ""]
                )
            
            # Split text
            text_chunks = splitter.split_text(text)
            
            # Create chunk objects
            for i, chunk_text in enumerate(text_chunks):
                chunk = DocumentChunk(
                    id=str(uuid.uuid4()),
                    collection_id=collection_id,
                    document_id=filename,
                    content=chunk_text.strip(),
                    metadata={
                        "filename": filename,
                        "chunk_index": i,
                        "chunk_size": len(chunk_text),
                        "strategy": strategy
                    }
                )
                chunks.append(chunk)
            
            return chunks
        
        except Exception as e:
            logger.error(f"Error creating chunks: {e}")
            return []
    
    async def _update_bm25_index(self, collection_id: str):
        """Update BM25 index for collection"""
        try:
            if collection_id in self.chunks and self.chunks[collection_id]:
                corpus = [chunk.content for chunk in self.chunks[collection_id]]
                tokenized_corpus = [doc.split() for doc in corpus]
                self.bm25_indices[collection_id] = BM25Okapi(tokenized_corpus)
                logger.info(f"Updated BM25 index for collection {collection_id}")
        
        except Exception as e:
            logger.error(f"Error updating BM25 index: {e}")
    
    async def hybrid_search(self, query: str, collection_id: str, top_k: int = 5) -> List[DocumentChunk]:
        """Perform hybrid search (BM25 + Vector similarity)"""
        try:
            if collection_id not in self.chunks or not self.chunks[collection_id]:
                return []
            
            chunks = self.chunks[collection_id]
            
            # Vector similarity search
            if self.embedding_model:
                query_embedding = self.embedding_model.encode(query)
                
                for chunk in chunks:
                    if chunk.embedding:
                        chunk_embedding = np.array(chunk.embedding)
                        similarity = np.dot(query_embedding, chunk_embedding) / (
                            np.linalg.norm(query_embedding) * np.linalg.norm(chunk_embedding)
                        )
                        chunk.similarity_score = float(similarity)
            
            # BM25 search
            if collection_id in self.bm25_indices:
                tokenized_query = query.split()
                bm25_scores = self.bm25_indices[collection_id].get_scores(tokenized_query)
                
                for i, chunk in enumerate(chunks):
                    chunk.bm25_score = float(bm25_scores[i]) if i < len(bm25_scores) else 0.0
            
            # Combine scores (weighted average)
            for chunk in chunks:
                vector_score = chunk.similarity_score or 0.0
                bm25_score = chunk.bm25_score or 0.0
                chunk.combined_score = 0.7 * vector_score + 0.3 * bm25_score
            
            # Sort by combined score
            chunks.sort(key=lambda x: x.combined_score, reverse=True)
            
            # Re-ranking if enabled
            if self.config.use_reranking and self.rerank_model:
                top_chunks = chunks[:top_k * 2]  # Get more for re-ranking
                
                # Prepare pairs for re-ranking
                pairs = [(query, chunk.content) for chunk in top_chunks]
                rerank_scores = self.rerank_model.predict(pairs)
                
                # Update chunks with rerank scores
                for chunk, score in zip(top_chunks, rerank_scores):
                    chunk.rerank_score = float(score)
                
                # Sort by rerank score
                top_chunks.sort(key=lambda x: x.rerank_score, reverse=True)
                return top_chunks[:top_k]
            
            return chunks[:top_k]
        
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return []
    
    async def retrieve_context(self, query: str, collection_ids: List[str], top_k: int = 5) -> str:
        """Retrieve context from multiple collections"""
        try:
            all_chunks = []
            
            for collection_id in collection_ids:
                if collection_id in self.collections:
                    chunks = await self.hybrid_search(query, collection_id, top_k)
                    all_chunks.extend(chunks)
            
            # Sort all chunks by score
            if self.config.use_reranking:
                all_chunks.sort(key=lambda x: x.rerank_score or 0, reverse=True)
            else:
                all_chunks.sort(key=lambda x: getattr(x, 'combined_score', 0), reverse=True)
            
            # Take top chunks
            selected_chunks = all_chunks[:top_k]
            
            # Format context
            context_parts = []
            for chunk in selected_chunks:
                context_parts.append(f"Source: {chunk.metadata.get('filename', 'Unknown')}")
                context_parts.append(chunk.content)
                context_parts.append("---")
            
            return "\n".join(context_parts)
        
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            return ""
    
    def get_collections(self) -> List[Dict[str, Any]]:
        """Get all collections"""
        collections_list = []
        for collection in self.collections.values():
            collection_dict = collection.dict()
            collection_dict['chunks_count'] = len(self.chunks.get(collection.id, []))
            collections_list.append(collection_dict)
        
        return collections_list
    
    def get_collection(self, collection_id: str) -> Optional[Dict[str, Any]]:
        """Get specific collection"""
        if collection_id in self.collections:
            collection_dict = self.collections[collection_id].dict()
            collection_dict['chunks_count'] = len(self.chunks.get(collection_id, []))
            return collection_dict
        return None
    
    async def delete_collection(self, collection_id: str) -> bool:
        """Delete a collection"""
        try:
            if collection_id in self.collections:
                # Remove from memory
                del self.collections[collection_id]
                if collection_id in self.chunks:
                    del self.chunks[collection_id]
                if collection_id in self.bm25_indices:
                    del self.bm25_indices[collection_id]
                
                # Remove files
                chunks_file = self.collections_dir / f"{collection_id}_chunks.json"
                if chunks_file.exists():
                    chunks_file.unlink()
                
                # Save updated collections
                await self._save_collections()
                
                logger.info(f"Deleted collection {collection_id}")
                return True
        
        except Exception as e:
            logger.error(f"Error deleting collection {collection_id}: {e}")
        
        return False
    
    def is_ready(self) -> bool:
        """Check if service is ready"""
        return self.embedding_model is not None
    
    def get_status(self) -> Dict[str, Any]:
        """Get service status"""
        return {
            "service": "Open WebUI RAG Service",
            "status": "ready" if self.is_ready() else "initializing",
            "collections_count": len(self.collections),
            "total_chunks": sum(len(chunks) for chunks in self.chunks.values()),
            "embedding_model": self.config.embedding_model,
            "rerank_model": self.config.rerank_model if self.config.use_reranking else None,
            "hybrid_search": self.config.use_hybrid_search,
            "full_context_mode": self.config.full_context_mode
        }

# Singleton instance
openwebui_rag_service = OpenWebUIRAGService() 