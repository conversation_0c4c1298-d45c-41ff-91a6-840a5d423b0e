"""
Gemma 3N E4B LiteRT Service
Using Google AI Edge LiteRT for on-device LLM inference
"""
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional
import numpy as np
import sentencepiece as spm
import zipfile
import tempfile

logger = logging.getLogger(__name__)

try:
    from ai_edge_litert.interpreter import Interpreter
    LITERT_AVAILABLE = True
    logger.info("✅ LiteRT available")
except ImportError as e:
    LITERT_AVAILABLE = False
    logger.warning(f"❌ LiteRT not available. Full error: {e}")
    import traceback
    traceback.print_exc()

class Gemma3nMediaPipeService:
    """
    Gemma 3N E4B service using AI Edge LiteRT
    """
    
    def __init__(self):
        self.model_loaded = False
        self.interpreter = None
        self.tokenizer = None
        
        self.config = {
            "max_tokens": 4096,
            "temperature": 1.0,
            "top_k": 64,
            "top_p": 0.95,
            "random_seed": 42,
            "framework": "AI Edge LiteRT",
            "ai_edge_aligned": True
        }
        
        try:
            self._load_model()
            logger.info("✅ Gemma 3N E4B LiteRT Service initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize service: {e}")
            self.model_loaded = False

    def _load_model(self):
        """Load model using AI Edge LiteRT"""
        try:
            if not LITERT_AVAILABLE:
                raise ImportError("LiteRT not available")
            
            app_dir = Path(__file__).parent.parent
            task_path = app_dir / "models" / "tflite" / "gemma3n" / "gemma-3n-E4B-it-int4.task"

            if not task_path.exists():
                raise FileNotFoundError(f"Task file not found: {task_path}")

            with zipfile.ZipFile(task_path, 'r') as zip_ref:
                logger.info(f"Files in task archive: {zip_ref.namelist()}")
                model_content = zip_ref.read("TF_LITE_PREFILL_DECODE")
                tokenizer_content = zip_ref.read("TOKENIZER_MODEL")

            logger.info("Loading Gemma 3N E4B with LiteRT from memory")
            
            self.interpreter = Interpreter(model_content=model_content)
            self.interpreter.allocate_tensors()

            if hasattr(self.interpreter, 'get_signature_list'):
                try:
                    signatures = self.interpreter.get_signature_list()
                    logger.info(f"SIGNATURE_LIST: {signatures}")
                except Exception as e:
                    logger.error(f"COULD_NOT_GET_SIGNATURES: {e}")
            else:
                logger.info("NO_GET_SIGNATURE_LIST_METHOD")
 
            self.tokenizer = spm.SentencePieceProcessor()
            self.tokenizer.load_from_serialized_proto(tokenizer_content)

            self.model_loaded = True
            logger.info("✅ LiteRT model and tokenizer loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load LiteRT model: {e}")
            self.model_loaded = False
            raise

    def generate_response(
        self,
        text: str,
        max_tokens: int = None,
        temperature: float = None,
        top_k: int = None,
        top_p: float = None
    ) -> str:
        """
        Generate response using LiteRT. Temporarily disabled for debugging.
        """
        logger.warning("generate_response is temporarily disabled for debugging.")
        return self._generate_ai_edge_fallback(text)

    def _format_gemma_prompt(self, text: str) -> str:
        """Format prompt according to Gemma chat template"""
        return f"<start_of_turn>user\n{text}<end_of_turn>\n<start_of_turn>model\n"

    def _clean_response(self, response: str) -> str:
        """Clean and format response"""
        if not response:
            return ""
        
        response = response.split("<end_of_turn>")[0]
        for token in ["<start_of_turn>", "user", "model"]:
            response = response.replace(token, "")
        
        response = response.strip()
        response = " ".join(response.split())
        
        if len(response) < 3 or response in ["", ".", "?", "!"]:
            return ""
        
        return response

    def _generate_ai_edge_fallback(self, text: str) -> str:
        """Generate intelligent fallback response"""
        return f"I am a LiteRT-powered model. I received your query: '{text}'"

    def get_model_info(self) -> Dict:
        """Return comprehensive model information"""
        return {
            "model_name": "Gemma 3N E4B LiteRT",
            "version": "litert-v1.0",
            "framework": "Google AI Edge LiteRT",
            "loaded": self.model_loaded,
            "config": self.config,
        }

    def is_available(self) -> bool:
        """Check if service is available"""
        return LITERT_AVAILABLE and self.model_loaded

# Global service instance
gemma3n_mediapipe_service = Gemma3nMediaPipeService()
