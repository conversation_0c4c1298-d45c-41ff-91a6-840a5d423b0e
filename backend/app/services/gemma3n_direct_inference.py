"""
Gemma 3N E4B Direct Inference Service - Google AI Edge Gallery Implementation
Exact implementation following Google AI Edge Gallery specifications
"""
import os
import logging
import numpy as np
import zipfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import tempfile
import io
import sentencepiece as smp

logger = logging.getLogger(__name__)

try:
    from ai_edge_litert.interpreter import Interpreter
    AI_EDGE_LITERT_AVAILABLE = True
    logger.info("✅ AI Edge LiteRT available")
except ImportError:
    AI_EDGE_LITERT_AVAILABLE = False
    import tensorflow as tf
    logger.warning("❌ AI Edge LiteRT not available, falling back to tf.lite")

class Gemma3nDirectInferenceService:
    """Google AI Edge Gallery compatible Gemma 3N E4B inference service"""
    
    def __init__(self):
        self.model_loaded = False
        self.interpreter = None
        self.tokenizer = None
        self.input_details = None
        self.output_details = None
        
        # Google AI Edge Gallery standard configuration for Gemma 3N E4B
        self.config = {
            "maxTokens": 1024,        # Context length as per AI Edge Gallery
            "topK": 64,               # Standard topK for Gemma 3N
            "temperature": 0.8,       # Default temperature
            "randomSeed": 0,          # Deterministic by default
            "vocab_size": 256000,     # Gemma vocab size
            "context_length": 1024    # AI Edge Gallery standard
        }
        
        try:
            self._load_model()
            logger.info("✅ Google AI Edge Gallery compatible service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI Edge Gallery service: {e}")
            self.model_loaded = False

    def _load_model(self):
        """Load TFLite model following Google AI Edge Gallery specifications"""
        try:
            # Use absolute path from the app directory
            app_dir = Path(__file__).parent.parent  # Go up to app directory
            task_path = app_dir / "models" / "tflite" / "gemma3n" / "gemma-3n-E4B-it-int4.task"
            
            if not task_path.exists():
                raise FileNotFoundError(f"Gemma 3N E4B task file not found: {task_path}")
            
            logger.info(f"Loading Gemma 3N E4B from: {task_path}")
            
            # Load model components from .task file (Google AI Edge Gallery format)
            with zipfile.ZipFile(task_path, 'r') as zip_ref:
                # Google AI Edge Gallery format uses specific file names
                tflite_file = "TF_LITE_PREFILL_DECODE"  # Main TFLite model
                tokenizer_file = "TOKENIZER_MODEL"      # SentencePiece tokenizer
                
                file_list = zip_ref.namelist()
                logger.info(f"Task file contents: {file_list}")
                
                if tflite_file not in file_list:
                    raise FileNotFoundError(f"TFLite model '{tflite_file}' not found in task file")
                
                if tokenizer_file not in file_list:
                    raise FileNotFoundError(f"Tokenizer '{tokenizer_file}' not found in task file")
                
                # Load TFLite model directly into memory
                with zip_ref.open(tflite_file) as model_file:
                    model_content = model_file.read()
                    
                # Initialize interpreter with Google AI Edge Gallery settings
                if AI_EDGE_LITERT_AVAILABLE:
                    self.interpreter = Interpreter(
                        model_content=model_content,
                        num_threads=4  # AI Edge Gallery default
                    )
                    logger.info("✅ Using AI Edge LiteRT interpreter")
                else:
                    self.interpreter = tf.lite.Interpreter(
                        model_content=model_content,
                        num_threads=4  # AI Edge Gallery default
                    )
                    logger.warning("⚠️ Using deprecated tf.lite.Interpreter")
                self.interpreter.allocate_tensors()
                self.input_details = self.interpreter.get_input_details()
                self.output_details = self.interpreter.get_output_details()
                
                logger.info(f"✅ TFLite interpreter initialized")
                logger.info(f"Input shape: {self.input_details[0]['shape']}")
                logger.info(f"Output shape: {self.output_details[0]['shape']}")
                
                # Load SentencePiece tokenizer (Google AI Edge Gallery format)
                with zip_ref.open(tokenizer_file) as tok_file:
                    tok_content = tok_file.read()
                    
                # Create temporary file for SentencePiece
                with tempfile.NamedTemporaryFile(delete=False, suffix='.model') as tmp_file:
                    tmp_file.write(tok_content)
                    tmp_path = tmp_file.name
                
                try:
                    self.tokenizer = smp.SentencePieceProcessor()
                    self.tokenizer.Load(tmp_path)
                    logger.info(f"✅ SentencePiece tokenizer loaded: vocab_size={self.tokenizer.vocab_size()}")
                finally:
                    os.unlink(tmp_path)
            
            self.model_loaded = True
            logger.info("✅ Gemma 3N E4B loaded successfully - Google AI Edge Gallery compatible")
            
        except Exception as e:
            logger.error(f"Failed to load Gemma 3N E4B: {e}")
            self.model_loaded = False
            raise

    def generate_response(self, text: str, max_tokens: int = 256, temperature: float = 0.8, **kwargs) -> str:
        """Generate response using Google AI Edge Gallery compatible TFLite inference"""
        if not self.model_loaded or not self.interpreter:
            raise RuntimeError("Gemma 3N E4B model not loaded")
        
        try:
            # Google AI Edge Gallery Gemma prompt format
            formatted_prompt = f"<start_of_turn>user\n{text}<end_of_turn>\n<start_of_turn>model\n"
            
            # Tokenize using SentencePiece
            input_ids = self.tokenizer.EncodeAsIds(formatted_prompt)
            
            # Prepare input tensor with correct shape
            max_length = min(len(input_ids), self.config["context_length"])
            input_array = np.array([input_ids[:max_length]], dtype=np.int32)
            
            # Adjust to model's expected input shape
            expected_shape = self.input_details[0]['shape']
            if len(expected_shape) > 1:
                target_length = expected_shape[1]
                if input_array.shape[1] < target_length:
                    # Pad with zeros
                    padding = np.zeros((1, target_length - input_array.shape[1]), dtype=np.int32)
                    input_array = np.concatenate([input_array, padding], axis=1)
                elif input_array.shape[1] > target_length:
                    # Truncate
                    input_array = input_array[:, :target_length]
            
            # Run TFLite inference
            self.interpreter.set_tensor(self.input_details[0]['index'], input_array)
            self.interpreter.invoke()
            
            # Get output logits
            output_logits = self.interpreter.get_tensor(self.output_details[0]['index'])
            
            # Generate tokens using Google AI Edge Gallery sampling
            generated_tokens = self._generate_tokens(output_logits, max_tokens, temperature)
            
            # Decode tokens to text
            response = self.tokenizer.DecodeIds(generated_tokens)
            
            # Clean response according to Gemma format
            response = self._clean_response(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Gemma 3N E4B inference failed: {e}")
            raise RuntimeError(f"Inference failed: {e}")

    def _generate_tokens(self, logits: np.ndarray, max_tokens: int, temperature: float) -> List[int]:
        """Generate tokens using Google AI Edge Gallery sampling strategy"""
        generated_tokens = []
        
        for _ in range(max_tokens):
            # Get next token logits
            if len(logits.shape) == 3:  # [batch, seq_len, vocab_size]
                next_token_logits = logits[0, -1, :]
            elif len(logits.shape) == 2:  # [seq_len, vocab_size]
                next_token_logits = logits[-1, :]
            else:
                next_token_logits = logits.flatten()
            
            # Apply temperature
            if temperature > 0:
                next_token_logits = next_token_logits / temperature
            
            # Apply top-k sampling (Google AI Edge Gallery standard)
            top_k = self.config["topK"]
            if top_k > 0:
                top_k_indices = np.argpartition(next_token_logits, -top_k)[-top_k:]
                top_k_logits = next_token_logits[top_k_indices]
                
                # Apply softmax
                exp_logits = np.exp(top_k_logits - np.max(top_k_logits))
                probs = exp_logits / np.sum(exp_logits)
                
                # Sample from distribution
                sampled_idx = np.random.choice(len(probs), p=probs)
                next_token = top_k_indices[sampled_idx]
            else:
                # Greedy sampling
                next_token = np.argmax(next_token_logits)
            
            generated_tokens.append(int(next_token))
            
            # Check for end of sequence (Gemma specific tokens)
            if next_token in [self.tokenizer.eos_id(), self.tokenizer.pad_id()]:
                break
                
            # Stop if we hit Gemma turn markers
            if len(generated_tokens) >= 2:
                decoded_partial = self.tokenizer.DecodeIds(generated_tokens[-2:])
                if "<end_of_turn>" in decoded_partial:
                    break
        
        return generated_tokens

    def _clean_response(self, response: str) -> str:
        """Clean response according to Google AI Edge Gallery Gemma format"""
        if not response:
            return ""
        
        # Remove Gemma special tokens
        response = response.replace("<start_of_turn>", "")
        response = response.replace("<end_of_turn>", "")
        response = response.replace("model\n", "")
        response = response.replace("user\n", "")
        
        # Clean up whitespace
        response = response.strip()
        
        # Remove any leading/trailing newlines
        response = response.strip('\n')
        
        return response

    def get_model_info(self) -> Dict:
        """Return Google AI Edge Gallery compatible model information"""
        return {
            "model_name": "Gemma 3N E4B",
            "version": "google-ai-edge-gallery-compatible",
            "framework": "TFLite",
            "quantization": "int4",
            "backend": "CPU/GPU",
            "context_length": self.config["context_length"],
            "vocab_size": self.config["vocab_size"],
            "loaded": self.model_loaded,
            "source": "Google AI Edge Gallery Implementation"
        }

    def is_available(self) -> bool:
        """Check if Gemma 3N E4B model is available for inference"""
        return self.model_loaded and self.interpreter is not None 