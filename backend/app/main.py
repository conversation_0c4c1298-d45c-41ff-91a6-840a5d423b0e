import logging
import os
from fastapi import <PERSON><PERSON><PERSON>, HTTPEx<PERSON>, Request, Depends
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import yaml
from typing import Optional
from pathlib import Path

from app.core.config import settings
from app.services.memory_service import MemoryService
from app.services.model_service import ModelService
from app.services.performance_service import PerformanceService
from app.api.memory_router import router as memory_router
from app.api.v1.api import api_router

# Configure logging
logging.basicConfig(level=settings.LOG_LEVEL)
logger = logging.getLogger(__name__)

# Load OpenAPI spec
openapi_path = Path(__file__).parent.parent / "openapi.yaml"
with open(openapi_path) as f:
    openapi_spec = yaml.safe_load(f)

app = FastAPI(
    title=openapi_spec["info"]["title"],
    description=openapi_spec["info"]["description"],
    version=openapi_spec["info"]["version"],
    openapi_url="/openapi.json",
    docs_url="/docs"
)

# Configure CORS - allow all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Initialize services
memory_service = None
model_service = None
performance_service = None

try:
    memory_service = MemoryService()
    model_service = ModelService()
    performance_service = PerformanceService()
    logger.info("✅ Services initialized successfully")
except ImportError as e:
    logger.warning(f"⚠️  Service initialization error: {str(e)}")

# Pydantic models
class MemorySettings(BaseModel):
    enabled: bool
    openai_api_key: Optional[str] = None
    max_memories: int
    memories_per_query: int
    memory_ttl: int
    min_relevance_score: float

class ModelSettings(BaseModel):
    selected: str
    context_length: int
    max_tokens: int

class PerformanceSettings(BaseModel):
    max_concurrent_uploads: int
    max_concurrent_searches: int
    max_workers: int
    max_memory_usage: int
    preset: str

class TestResponse(BaseModel):
    status: str
    message: str

# API Routes - Memory Settings
@app.get("/api/memory/settings", response_model=MemorySettings)
async def get_memory_settings():
    if not memory_service:
        raise HTTPException(status_code=503, detail="Memory service not available")
    return memory_service.get_settings()

@app.post("/api/memory/settings", response_model=MemorySettings)
async def update_memory_settings(settings: MemorySettings):
    if not memory_service:
        raise HTTPException(status_code=503, detail="Memory service not available")
    return memory_service.update_settings(settings.dict())

@app.post("/api/memory/settings/test", response_model=TestResponse)
async def test_memory_settings():
    if not memory_service:
        raise HTTPException(status_code=503, detail="Memory service not available")
    
    try:
        memory_service.test_settings()
        return TestResponse(status="success", message="Settings tested successfully")
    except Exception as e:
        return TestResponse(status="error", message=str(e))

# API Routes - Model Settings
@app.get("/api/models/settings", response_model=ModelSettings)
async def get_model_settings():
    if not model_service:
        raise HTTPException(status_code=503, detail="Model service not available")
    return model_service.get_settings()

def get_model_service():
    return ModelService()

@app.post("/api/models/settings", response_model=ModelSettings)
async def update_model_settings(settings: ModelSettings):
    if not model_service:
        raise HTTPException(status_code=503, detail="Model service not available")
    return model_service.update_settings(settings.dict())

@app.post("/api/models/settings/test")
async def test_model_connection(
    request: Request,
    model_service: ModelService = Depends(get_model_service)
):
    """Test connection to a model endpoint"""
    data = await request.json()
    endpoint = data.get("endpoint")
    api_key = data.get("api_key")
    model = data.get("model")

    if not endpoint or not model:
        raise HTTPException(status_code=400, detail="Missing required fields")

    success = await model_service.test_model_connection(endpoint, api_key, model)
    return {"success": success, "message": "Connection successful" if success else "Connection failed"}

# API Routes - Performance Settings
@app.get("/api/performance/settings", response_model=PerformanceSettings)
async def get_performance_settings():
    if not performance_service:
        raise HTTPException(status_code=503, detail="Performance service not available")
    return performance_service.get_settings()

@app.post("/api/performance/settings", response_model=PerformanceSettings)
async def update_performance_settings(settings: PerformanceSettings):
    if not performance_service:
        raise HTTPException(status_code=503, detail="Performance service not available")
    return performance_service.update_settings(settings.dict())

# Include memory router
app.include_router(memory_router, prefix="/api", tags=["Memory"])

# Include API v1 router
app.include_router(api_router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    logger.info(f"Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info(f"Log level set to: {settings.LOG_LEVEL}")
    # You can add other startup logic here, like loading models or connecting to databases

@app.on_event("shutdown")
async def shutdown_event():
    logger.info(f"Shutting down {settings.APP_NAME}")

@app.get("/", tags=["General"], summary="Root endpoint providing basic app info")
async def read_root():
    """
    Provides basic information about the application.
    """
    logger.info("Root endpoint was called.")
    return {
        "message": f"Welcome to {settings.APP_NAME}",
        "version": settings.APP_VERSION
    }

@app.get("/api/status", tags=["General"], summary="Returns the current status of the bot")
async def get_status():
    """
    Returns the current operational status of the bot.
    """
    logger.info("Status endpoint was called.")
    return {"status": "running", "message": "Bot is operational"}

@app.get("/download/apk", tags=["Downloads"], summary="Download the latest APK file")
async def download_apk():
    """
    Download the latest minimal AI assistant APK file.
    """
    apk_path = "/app/static/minimal_ai_assistant_with_file_picker.apk"
    
    if not os.path.exists(apk_path):
        logger.error(f"APK file not found at {apk_path}")
        raise HTTPException(status_code=404, detail="APK file not found")
    
    logger.info("APK download requested")
    return FileResponse(
        path=apk_path,
        filename="minimal_ai_assistant_with_file_picker.apk",
        media_type="application/vnd.android.package-archive"
    )

# Example of a custom exception handler (optional but good practice)
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTPException: {exc.status_code} {exc.detail} at {request.url.path}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )

# Add a generic exception handler for unhandled errors
@app.exception_handler(Exception)
async def generic_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc} at {request.url.path}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected internal server error occurred."},
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8011) 