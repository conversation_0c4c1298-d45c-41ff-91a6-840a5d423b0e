#!/bin/bash

echo "🔴 LIVE BACKEND LOG MONITORING"
echo "==============================="
echo "📍 Backend URL: http://localhost:8081"
echo "📋 Monitoring all log sources..."
echo "🔥 Press Ctrl+C to stop"
echo ""

# Create a function to add timestamps
add_timestamp() {
    while IFS= read -r line; do
        echo "[$(date '+%H:%M:%S')] $line"
    done
}

# Monitor multiple log sources
{
    # Follow backend.log
    tail -f backend.log 2>/dev/null | sed 's/^/[BACKEND] /' &
    
    # Follow uvicorn access logs if they exist
    tail -f access.log 2>/dev/null | sed 's/^/[ACCESS] /' &
    
    # Monitor processes
    while true; do
        sleep 10
        echo "[MONITOR] Backend process status: $(ps aux | grep 'run_server\|start_with_logs' | grep -v grep | wc -l) running"
    done &
    
    wait
} 