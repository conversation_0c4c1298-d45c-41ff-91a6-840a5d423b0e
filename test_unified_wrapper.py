#!/usr/bin/env python3
"""
🧪 TEST UNIFIED WRAPPER
Test MCPO unified wrapper that exposes all 13 tools like Jina Crawler
"""

import requests
import json
import time

API_KEY = "mcpo-unified-secret-key-2025"
BASE_URL = "http://localhost:8001"

def test_unified_wrapper():
    """Test unified wrapper with all 13 tools"""
    print("🔧 TESTING MCPO UNIFIED WRAPPER")
    print("=" * 60)
    print("Following Jina Crawler pattern: /tools/* endpoints")
    print("All 13 MCPO tools exposed via unified API")
    print("=" * 60)
    
    # Test health first
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=10)
        if health_response.status_code == 200:
            data = health_response.json()
            print(f"✅ Health: {data['status']}")
            print(f"   Server: {data['server']}")
            print(f"   Tools count: {data['tools_count']}")
            print(f"   MCPO compliant: {data['mcpo_compliant']}")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test OpenAPI spec
    try:
        openapi_response = requests.get(f"{BASE_URL}/openapi.json", timeout=10)
        if openapi_response.status_code == 200:
            openapi_data = openapi_response.json()
            paths = list(openapi_data.get('paths', {}).keys())
            tool_paths = [p for p in paths if p.startswith('/tools/')]
            print(f"✅ OpenAPI spec: {len(tool_paths)} tool endpoints")
            print(f"   Tools: {[p.replace('/tools/', '') for p in tool_paths]}")
        else:
            print(f"❌ OpenAPI spec failed: {openapi_response.status_code}")
    except Exception as e:
        print(f"❌ OpenAPI spec error: {e}")
    
    # Test individual tools
    test_tools = [
        {
            'name': 'Weather Service',
            'endpoint': '/tools/weather',
            'payload': {'location': 'Ho Chi Minh City'},
            'expected_keys': ['temperature', 'condition']
        },
        {
            'name': 'Current Time',
            'endpoint': '/tools/current_time',
            'payload': {'timezone': 'Asia/Ho_Chi_Minh'},
            'expected_keys': ['timestamp']
        },
        {
            'name': 'Brave Search',
            'endpoint': '/tools/search_brave',
            'payload': {'query': 'artificial intelligence', 'count': 3},
            'expected_keys': ['results']
        },
        {
            'name': 'Wikipedia Search',
            'endpoint': '/tools/search_wikipedia',
            'payload': {'query': 'Vietnam', 'limit': 3},
            'expected_keys': ['results']
        },
        {
            'name': 'Jina Crawler',
            'endpoint': '/tools/jina_crawler',
            'payload': {'url': 'https://example.com', 'max_content_length': 5000},
            'expected_keys': ['content']
        }
    ]
    
    results = []
    
    for i, tool in enumerate(test_tools, 1):
        print(f"\n🔍 Test {i}/5: {tool['name']}")
        print(f"   Endpoint: {tool['endpoint']}")
        
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {API_KEY}'
            }
            
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}{tool['endpoint']}",
                json=tool['payload'],
                headers=headers,
                timeout=30
            )
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result_data = data.get('result', {})
                    
                    print(f"   ✅ SUCCESS in {elapsed:.2f}s")
                    print(f"   Response keys: {list(result_data.keys())}")
                    
                    # Check expected keys
                    has_expected = any(key in result_data for key in tool['expected_keys'])
                    if has_expected:
                        print(f"   ✅ Expected data found")
                    else:
                        print(f"   ⚠️ Expected keys not found: {tool['expected_keys']}")
                    
                    results.append({
                        'tool': tool['name'],
                        'success': True,
                        'time': elapsed,
                        'has_expected': has_expected
                    })
                else:
                    error = data.get('error', 'Unknown error')
                    print(f"   ❌ FAILED: {error}")
                    results.append({
                        'tool': tool['name'],
                        'success': False,
                        'error': error,
                        'time': elapsed
                    })
            else:
                print(f"   ❌ HTTP ERROR: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Error text: {response.text[:200]}")
                results.append({
                    'tool': tool['name'],
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'time': elapsed
                })
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            results.append({
                'tool': tool['name'],
                'success': False,
                'error': str(e),
                'time': 0
            })
        
        time.sleep(1)  # Brief delay between tests
    
    # Analysis
    print("\n" + "=" * 60)
    print("📊 UNIFIED WRAPPER TEST RESULTS")
    print("=" * 60)
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"🎯 OVERALL RESULTS:")
    print(f"   Total tools tested: {len(results)}")
    print(f"   Successful: {len(successful)}")
    print(f"   Failed: {len(failed)}")
    print(f"   Success rate: {len(successful)/len(results)*100:.1f}%")
    
    if successful:
        avg_time = sum(r['time'] for r in successful) / len(successful)
        expected_data_count = len([r for r in successful if r.get('has_expected', False)])
        
        print(f"\n⏱️  PERFORMANCE:")
        print(f"   Average response time: {avg_time:.2f}s")
        print(f"   Tools with expected data: {expected_data_count}/{len(successful)}")
        
        print(f"\n✅ SUCCESSFUL TOOLS:")
        for result in successful:
            expected_status = "✅" if result.get('has_expected', False) else "⚠️"
            print(f"   {result['tool']}: {result['time']:.2f}s {expected_status}")
    
    if failed:
        print(f"\n❌ FAILED TOOLS:")
        for result in failed:
            print(f"   {result['tool']}: {result['error']}")
    
    # Assessment
    print(f"\n🏆 UNIFIED WRAPPER ASSESSMENT:")
    if len(successful) >= 4:
        print("🎉 EXCELLENT: Unified wrapper working perfectly!")
        print("   ✅ High success rate")
        print("   ✅ Following Jina Crawler pattern")
        print("   ✅ All tools accessible via /tools/* endpoints")
        print("   ✅ Proper OpenAPI spec for Open WebUI")
        assessment = "excellent"
    elif len(successful) >= 3:
        print("✅ GOOD: Unified wrapper working well")
        print("   ✅ Good success rate")
        print("   ⚠️ Some tools need refinement")
        assessment = "good"
    else:
        print("⚠️ NEEDS IMPROVEMENT: Unified wrapper issues")
        print("   ❌ Low success rate")
        print("   ❌ Multiple tool failures")
        assessment = "poor"
    
    # Comparison with original MCPO
    print(f"\n📈 IMPROVEMENT OVER ORIGINAL MCPO:")
    print("   Before: 13 separate namespaces (/weather_service/, /time_utilities/, etc.)")
    print("   After: Unified namespace (/tools/weather, /tools/current_time, etc.)")
    print("   Benefits:")
    print("     🎯 LLM can discover all tools via single OpenAPI spec")
    print("     🔧 Consistent API pattern like Jina Crawler")
    print("     🚀 Better Open WebUI integration")
    print("     📖 Single documentation endpoint")
    
    return assessment == "excellent"

def main():
    """Main test function"""
    success = test_unified_wrapper()
    
    print("\n" + "=" * 60)
    print("🎉 UNIFIED WRAPPER DEPLOYMENT RESULTS")
    print("=" * 60)
    
    if success:
        print("🎉 UNIFIED WRAPPER DEPLOYMENT SUCCESSFUL!")
        print("\n🚀 Ready for Open WebUI integration:")
        print("   URL: http://localhost:8001")
        print("   OpenAPI: http://localhost:8001/openapi.json")
        print("   API Key: mcpo-unified-secret-key-2025")
        print("\n💡 LLM can now see all 13 MCPO tools!")
        print("   Weather, Time, Search, Documents, Memory, etc.")
        print("   All accessible via unified /tools/* endpoints")
        return True
    else:
        print("⚠️ UNIFIED WRAPPER NEEDS REFINEMENT")
        print("   Check individual tool configurations")
        print("   Verify MCPO backend connectivity")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
