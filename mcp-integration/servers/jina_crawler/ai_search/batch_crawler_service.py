"""
Batch Crawler Service

Provides parallel crawling capabilities using the existing JiniCrawler infrastructure.
Optimized for crawling multiple URLs concurrently for AI search engine use cases.
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import sys
import os

# Add parent directory to path to import existing crawlers
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from jini_crawler import JiniCrawler, JiniCrawlResult

logger = logging.getLogger(__name__)

@dataclass
class BatchCrawlResult:
    """Result from batch crawling operation"""
    url: str
    success: bool
    content: str = ""
    title: str = ""
    error: Optional[str] = None
    processing_time: float = 0.0
    content_length: int = 0

@dataclass
class BatchCrawlResponse:
    """Response from batch crawling service"""
    query: str
    urls: List[str]
    results: List[BatchCrawlResult]
    total_urls: int
    successful_crawls: int
    total_time: float
    success: bool
    error: Optional[str] = None

class BatchCrawlerService:
    """
    Service for crawling multiple URLs in parallel using JiniCrawler
    """
    
    def __init__(self, max_concurrent: int = 15, timeout: int = 20):
        """
        Initialize the batch crawler service (OPTIMIZED)

        Args:
            max_concurrent: Maximum number of concurrent crawl operations (increased)
            timeout: Timeout for each crawl operation in seconds (reduced for speed)
        """
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        try:
            self.crawler = JiniCrawler()
        except Exception as e:
            logger.error(f"❌ Failed to create JiniCrawler: {e}")
            self.crawler = None
        self._initialized = False
        self._semaphore = asyncio.Semaphore(max_concurrent)
        
    async def initialize(self):
        """Initialize the crawler"""
        if not self._initialized:
            try:
                if self.crawler is None:
                    raise Exception("JiniCrawler instance is None")
                await self.crawler.initialize()
                self._initialized = True
                logger.info(f"✅ Batch Crawler Service initialized (max_concurrent: {self.max_concurrent})")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Batch Crawler Service: {e}")
                raise
    
    async def crawl_urls(
        self,
        urls: List[str],
        query: str = "",
        task_type: str = "html_to_markdown",
        use_batch_processing: bool = True
    ) -> BatchCrawlResponse:
        """
        Crawl multiple URLs in parallel
        
        Args:
            urls: List of URLs to crawl
            query: Original search query (for context)
            task_type: Type of processing to apply
            
        Returns:
            BatchCrawlResponse with all crawl results
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🚀 Starting batch crawl of {len(urls)} URLs (batch_processing: {use_batch_processing})")

            # Ensure crawler is properly initialized with context manager
            if self.crawler and not self.crawler._initialized:
                await self.crawler.initialize()

            if use_batch_processing:
                # NEW: Batch processing approach - MUCH more efficient!
                results = await self._crawl_urls_batch_processing(urls, query)
            else:
                # OLD: Individual processing approach
                results = await self._crawl_urls_individual_processing(urls, task_type)
            
            # Convert results to BatchCrawlResult format
            crawl_results = []
            successful_crawls = 0
            
            # Handle both batch and individual processing results
            if use_batch_processing:
                # Results are already JiniCrawlResult objects from batch processing
                for result in results:
                    if result.success:
                        crawl_results.append(BatchCrawlResult(
                            url=result.url,
                            success=True,
                            content=result.processed_content,
                            title=result.title,
                            processing_time=0.1,  # Batch processing time is shared
                            error=None
                        ))
                        successful_crawls += 1
                    else:
                        crawl_results.append(BatchCrawlResult(
                            url=result.url,
                            success=False,
                            content="",
                            title="",
                            processing_time=0.1,
                            error=result.error
                        ))
            else:
                # Handle individual processing results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Crawl failed for {urls[i]}: {result}")
                        crawl_results.append(BatchCrawlResult(
                            url=urls[i],
                            success=False,
                        error=str(result)
                    ))
                else:
                    crawl_results.append(result)
                    if result.success:
                        successful_crawls += 1
            
            total_time = time.time() - start_time
            
            logger.info(f"✅ Batch crawl completed: {successful_crawls}/{len(urls)} successful in {total_time:.2f}s")
            
            return BatchCrawlResponse(
                query=query,
                urls=urls,
                results=crawl_results,
                total_urls=len(urls),
                successful_crawls=successful_crawls,
                total_time=total_time,
                success=True
            )
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"❌ Batch crawl failed: {e}")
            
            return BatchCrawlResponse(
                query=query,
                urls=urls,
                results=[],
                total_urls=len(urls),
                successful_crawls=0,
                total_time=total_time,
                success=False,
                error=str(e)
            )
    
    async def _crawl_single_url(self, url: str, task_type: str) -> BatchCrawlResult:
        """
        Crawl a single URL with concurrency control
        
        Args:
            url: URL to crawl
            task_type: Type of processing to apply
            
        Returns:
            BatchCrawlResult for the URL
        """
        async with self._semaphore:
            start_time = time.time()
            
            try:
                logger.debug(f"🕷️ Crawling: {url}")
                
                # Use the existing crawler's crawl_and_process method
                result = await self.crawler.crawl_and_process(
                    url=url,
                    max_content_length=200000  # Significantly increased for comprehensive AI processing
                )
                
                processing_time = time.time() - start_time
                
                if result.success:
                    # Extract title from processed content if available
                    title = self._extract_title_from_content(result.processed_content)
                    
                    return BatchCrawlResult(
                        url=url,
                        success=True,
                        content=result.processed_content,
                        title=title,
                        processing_time=processing_time,
                        content_length=len(result.processed_content)
                    )
                else:
                    return BatchCrawlResult(
                        url=url,
                        success=False,
                        error=result.error,
                        processing_time=processing_time
                    )
                    
            except asyncio.TimeoutError:
                processing_time = time.time() - start_time
                logger.warning(f"⏰ Crawl timeout for {url}")
                return BatchCrawlResult(
                    url=url,
                    success=False,
                    error="Crawl timeout",
                    processing_time=processing_time
                )
                
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"❌ Crawl error for {url}: {e}")
                return BatchCrawlResult(
                    url=url,
                    success=False,
                    error=str(e),
                    processing_time=processing_time
                )
    
    def _extract_title_from_content(self, content: str) -> str:
        """
        Extract title from processed content
        
        Args:
            content: Processed content (markdown)
            
        Returns:
            Extracted title or empty string
        """
        try:
            lines = content.split('\n')
            for line in lines[:10]:  # Check first 10 lines
                line = line.strip()
                if line.startswith('# '):
                    return line[2:].strip()
                elif line.startswith('## '):
                    return line[3:].strip()
            
            # Fallback: return first non-empty line
            for line in lines[:5]:
                line = line.strip()
                if line and not line.startswith('#'):
                    return line[:100]  # Limit length
            
            return ""
            
        except Exception:
            return ""
    
    async def cleanup(self):
        """Cleanup resources with comprehensive session management"""
        if self._initialized:
            try:
                # Cleanup crawler first
                if self.crawler:
                    await self.crawler.cleanup()
                    self.crawler = None

                # Force cleanup of any remaining resources
                import gc
                import asyncio

                # Wait for any pending operations to complete
                await asyncio.sleep(0.5)

                # Force garbage collection multiple times
                for i in range(5):
                    gc.collect()
                    await asyncio.sleep(0.1)

                # Additional cleanup for aiohttp resources
                try:
                    # Force cleanup of any remaining aiohttp resources
                    import aiohttp
                    # This helps cleanup any lingering connections
                    await asyncio.sleep(0.2)
                except Exception:
                    pass

                self._initialized = False
                logger.debug("✅ Batch Crawler Service cleanup completed")
            except Exception as e:
                logger.error(f"❌ Batch Crawler Service cleanup failed: {e}")

    async def _force_cleanup_sessions(self):
        """Force cleanup any remaining sessions in crawler"""
        logger.debug("🔥 Force cleanup sessions in batch crawler...")

        if self.crawler:
            try:
                # Force cleanup crawler sessions
                await self.crawler.cleanup()
                logger.debug("🔒 Force cleaned up crawler sessions")
            except Exception as e:
                logger.debug(f"Force crawler cleanup warning: {e}")

    async def _crawl_urls_batch_processing(self, urls: List[str], query: str) -> List:
        """
        🚀 NEW BATCH PROCESSING - Crawl all URLs then process with single Gemini call
        This reduces API calls from N to 1 and maximizes efficiency!

        Args:
            urls: List of URLs to crawl
            query: Search query for context

        Returns:
            List of JiniCrawlResult objects
        """
        try:
            logger.info(f"🚀 BATCH PROCESSING: Crawling {len(urls)} URLs then processing with single Gemini call")

            # Step 1: Crawl all URLs to get raw content (no Gemini processing yet)
            batch_data = await self.crawler.crawl_batch_raw(urls, max_content_length=200000)

            # Step 2: Process all raw content with single Gemini batch call
            results = await self.crawler.process_batch_with_gemini(batch_data, query)

            logger.info(f"✅ BATCH PROCESSING completed: {len(results)} URLs processed with 1 Gemini call instead of {len(urls)}!")
            logger.info(f"💰 API efficiency gain: {len(urls)}x reduction in Gemini requests!")

            return results

        except Exception as e:
            logger.error(f"❌ Batch processing failed: {e}")
            # Fallback to individual processing
            return await self._crawl_urls_individual_processing(urls, "html_to_markdown")

    async def _crawl_urls_individual_processing(self, urls: List[str], task_type: str) -> List:
        """
        🐌 OLD INDIVIDUAL PROCESSING - Process each URL separately (less efficient)

        Args:
            urls: List of URLs to crawl
            task_type: Type of processing

        Returns:
            List of BatchCrawlResult objects
        """
        logger.info(f"🐌 INDIVIDUAL PROCESSING: Processing {len(urls)} URLs separately")

        # Create crawl tasks
        tasks = [
            self._crawl_single_url(url, task_type)
            for url in urls
        ]

        # Execute crawls with concurrency control
        results = await asyncio.gather(*tasks, return_exceptions=True)

        logger.warning(f"⚠️ Individual processing used {len(urls)} separate Gemini calls - consider using batch processing!")

        return results

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
    
    def get_successful_results(self, response: BatchCrawlResponse) -> List[BatchCrawlResult]:
        """
        Get only successful crawl results
        
        Args:
            response: BatchCrawlResponse
            
        Returns:
            List of successful BatchCrawlResult objects
        """
        return [result for result in response.results if result.success and result.content.strip()]
