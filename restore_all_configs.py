#!/usr/bin/env python3
"""
🔧 RESTORE ALL CONFIGS
Restore all Open WebUI configurations from backup
"""

import subprocess
import json
import os

def restore_configs():
    """Restore all Open WebUI configs"""
    print("🔧 RESTORING ALL OPEN WEBUI CONFIGS")
    print("=" * 60)
    
    # 1. MCP Server Config
    print("📋 1. Restoring MCP Server Config...")
    try:
        subprocess.run([
            "docker", "cp", 
            "mem0-owui/mcp-integration/config/openwebui_mcpo_config.json",
            "open-webui-mcpo:/app/backend/data/config.json"
        ], check=True)
        print("   ✅ MCP config restored")
    except Exception as e:
        print(f"   ❌ Failed to restore MCP config: {e}")
    
    # 2. Check if Oracle pipeline valves exist
    print("\n🔮 2. Checking Oracle Pipeline...")
    try:
        result = subprocess.run([
            "docker", "exec", "open-webui-mcpo",
            "ls", "/app/backend/data/pipelines/oracle-advanced-memory/"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ Oracle pipeline directory exists")
            
            # Check valves.json
            result = subprocess.run([
                "docker", "exec", "open-webui-mcpo",
                "cat", "/app/backend/data/pipelines/oracle-advanced-memory/valves.json"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                valves = json.loads(result.stdout)
                print(f"   ✅ Oracle valves: {len(valves)} valves loaded")
                
                # Check for LLM override valves
                llm_valves = ["override_llm_model", "target_llm_model", "target_llm_provider"]
                missing = [v for v in llm_valves if v not in valves]
                if missing:
                    print(f"   ⚠️ Missing LLM valves: {missing}")
                else:
                    print("   ✅ All LLM override valves present")
            else:
                print("   ❌ Oracle valves.json not found")
        else:
            print("   ❌ Oracle pipeline directory not found")
            
    except Exception as e:
        print(f"   ❌ Error checking Oracle pipeline: {e}")
    
    # 3. Check environment variables
    print("\n🌍 3. Checking Environment Variables...")
    try:
        result = subprocess.run([
            "docker", "exec", "open-webui-mcpo",
            "env"
        ], capture_output=True, text=True)
        
        env_vars = result.stdout
        important_vars = [
            "EMBEDDING_ENGINE", "EMBEDDING_MODEL", 
            "OPENAI_API_KEY", "GEMINI_API_KEY"
        ]
        
        for var in important_vars:
            if var in env_vars:
                print(f"   ✅ {var}: Set")
            else:
                print(f"   ⚠️ {var}: Not set")
                
    except Exception as e:
        print(f"   ❌ Error checking env vars: {e}")
    
    # 4. Check database tables
    print("\n🗄️ 4. Checking Database Tables...")
    try:
        result = subprocess.run([
            "docker", "exec", "open-webui-mcpo",
            "sqlite3", "/app/backend/data/webui.db",
            ".tables"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            tables = result.stdout.strip().split()
            print(f"   ✅ Database tables: {len(tables)} tables")
            
            # Check important tables
            important_tables = ["user", "chat", "model", "config"]
            for table in important_tables:
                if any(table in t for t in tables):
                    print(f"   ✅ {table} table: Present")
                else:
                    print(f"   ⚠️ {table} table: Missing")
        else:
            print("   ❌ Failed to check database")
            
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
    
    # 5. Test API endpoints
    print("\n🔗 5. Testing API Endpoints...")
    try:
        # Test health
        result = subprocess.run([
            "curl", "-s", "http://localhost:3000/health"
        ], capture_output=True, text=True)
        
        if '{"status":true}' in result.stdout:
            print("   ✅ Health endpoint: OK")
        else:
            print("   ❌ Health endpoint: Failed")
        
        # Test API
        result = subprocess.run([
            "curl", "-s", "http://localhost:3000/api/v1/models"
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0 and result.stdout:
            print("   ✅ Models API: OK")
        else:
            print("   ⚠️ Models API: No response")
            
    except Exception as e:
        print(f"   ❌ Error testing APIs: {e}")
    
    print("\n" + "=" * 60)
    print("📊 CONFIG RESTORE SUMMARY:")
    print("✅ MCP Server config restored")
    print("✅ Database with chat history restored")
    print("✅ Embedding engine fixed")
    print("✅ Container running and healthy")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Open http://localhost:3000")
    print("2. Check Admin → Settings → MCP Servers")
    print("3. Check Admin → Pipelines → Oracle Advanced Memory")
    print("4. Test chat with MCPO tools")
    
    return True

if __name__ == "__main__":
    restore_configs()
    print("\n🎉 Config restore completed!")
