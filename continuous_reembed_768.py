#!/usr/bin/env python3
"""
Continuous Re-embedding: 3072D → 768D with progress monitoring
"""

import os
import asyncio
import requests
import json
import time
from typing import List, Dict, Any
from mem0 import AsyncMemory

# Configuration
QDRANT_HOST = "qdrant"
QDRANT_PORT = 6333
GEMINI_API_KEY = "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"
SOURCE_COLLECTION = "mem0_gemini_3072_fixed"
TARGET_COLLECTION = "mem0_openai_compatible_768"
BATCH_SIZE = 25  # Optimal batch size
DELAY_BETWEEN_MEMORIES = 1.5  # Seconds
DELAY_BETWEEN_BATCHES = 10  # Seconds

async def continuous_reembed():
    print("🚀 Starting continuous re-embedding process...")
    print(f"📍 Source: {SOURCE_COLLECTION} (3072D)")
    print(f"📍 Target: {TARGET_COLLECTION} (768D)")
    print(f"⚙️ Batch size: {BATCH_SIZE}")
    print(f"⏱️ Delays: {DELAY_BETWEEN_MEMORIES}s per memory, {DELAY_BETWEEN_BATCHES}s per batch")
    
    # Set environment variables for OpenAI-compatible endpoint
    os.environ['OPENAI_BASE_URL'] = 'https://generativelanguage.googleapis.com/v1beta/openai/'
    os.environ['OPENAI_API_KEY'] = GEMINI_API_KEY
    
    # Get total count
    source_response = requests.get(f'http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{SOURCE_COLLECTION}')
    if source_response.status_code != 200:
        print(f"❌ Cannot access source collection: {source_response.status_code}")
        return
    
    total_memories = source_response.json()['result']['points_count']
    print(f"📊 Total memories to re-embed: {total_memories}")
    
    # Get current target count
    target_response = requests.get(f'http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}')
    if target_response.status_code == 200:
        current_count = target_response.json()['result']['points_count']
        print(f"📊 Current target collection: {current_count} memories")
    else:
        current_count = 0
    
    # Setup memory client
    config = {
        'vector_store': {
            'provider': 'qdrant',
            'config': {
                'host': QDRANT_HOST,
                'port': QDRANT_PORT,
                'collection_name': TARGET_COLLECTION
            }
        },
        'llm': {
            'provider': 'openai',
            'config': {
                'model': 'gemini-2.5-flash',
                'temperature': 0.1,
                'max_tokens': 1000
            }
        },
        'embedder': {
            'provider': 'gemini',
            'config': {
                'api_key': GEMINI_API_KEY,
                'model': 'text-embedding-004',
                'embedding_dims': 768
            }
        }
    }
    
    try:
        memory_client = await AsyncMemory.from_config(config)
        print("✅ Memory client initialized")
    except Exception as e:
        print(f"❌ Failed to initialize memory client: {e}")
        return
    
    # Start re-embedding process
    total_processed = 0
    total_success = 0
    batch_number = 0
    offset = None
    
    start_time = time.time()
    
    while total_processed < total_memories:
        batch_number += 1
        print(f"\n🔄 Batch {batch_number} - Getting {BATCH_SIZE} memories...")
        
        # Get batch of memories
        try:
            payload = {
                'limit': BATCH_SIZE,
                'with_payload': True,
                'with_vector': False
            }
            if offset:
                payload['offset'] = offset
            
            response = requests.post(
                f'http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{SOURCE_COLLECTION}/points/scroll',
                json=payload
            )
            
            if response.status_code != 200:
                print(f"❌ Error getting batch: {response.status_code}")
                break
            
            data = response.json()
            points = data['result']['points']
            next_offset = data['result'].get('next_page_offset')
            
            if not points:
                print("✅ No more memories to process")
                break
            
            print(f"📤 Processing {len(points)} memories in batch {batch_number}")
            
            # Process batch
            batch_success = 0
            for i, point in enumerate(points):
                try:
                    content = point['payload'].get('content', '')
                    user_id = point['payload'].get('user_id', 'default_user')
                    
                    if content:
                        await memory_client.add(
                            messages=content,
                            user_id=user_id,
                            metadata={
                                'original_id': point['id'],
                                'migrated_from': SOURCE_COLLECTION,
                                'reembedded_768d': True,
                                'batch_number': batch_number,
                                'embedding_model': 'text-embedding-004'
                            }
                        )
                        batch_success += 1
                        total_success += 1
                        
                        # Progress indicator
                        if (i + 1) % 5 == 0:
                            print(f"   ✅ {i + 1}/{len(points)} memories processed in batch")
                        
                        # Rate limiting
                        if i < len(points) - 1:
                            await asyncio.sleep(DELAY_BETWEEN_MEMORIES)
                    
                    total_processed += 1
                    
                except Exception as e:
                    if "429" in str(e) or "quota" in str(e).lower() or "rate" in str(e).lower():
                        print(f"   ⏳ Rate limit hit, waiting 30 seconds...")
                        await asyncio.sleep(30)
                        # Retry once
                        try:
                            await memory_client.add(
                                messages=content,
                                user_id=user_id,
                                metadata={
                                    'original_id': point['id'],
                                    'migrated_from': SOURCE_COLLECTION,
                                    'reembedded_768d': True,
                                    'batch_number': batch_number,
                                    'embedding_model': 'text-embedding-004'
                                }
                            )
                            batch_success += 1
                            total_success += 1
                            print(f"   ✅ Retry successful")
                        except Exception as retry_e:
                            print(f"   ❌ Retry failed: {retry_e}")
                    else:
                        print(f"   ❌ Error processing memory {i+1}: {e}")
                    
                    total_processed += 1
                    continue
            
            # Batch summary
            elapsed_time = time.time() - start_time
            progress_percent = (total_processed / total_memories) * 100
            estimated_total_time = (elapsed_time / total_processed) * total_memories if total_processed > 0 else 0
            remaining_time = estimated_total_time - elapsed_time
            
            print(f"📊 Batch {batch_number} completed:")
            print(f"   - Processed: {batch_success}/{len(points)} memories")
            print(f"   - Total progress: {total_processed}/{total_memories} ({progress_percent:.1f}%)")
            print(f"   - Success rate: {total_success}/{total_processed} ({(total_success/total_processed)*100:.1f}%)")
            print(f"   - Elapsed time: {elapsed_time/60:.1f} minutes")
            print(f"   - Estimated remaining: {remaining_time/60:.1f} minutes")
            
            # Check target collection count
            target_check = requests.get(f'http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}')
            if target_check.status_code == 200:
                current_target_count = target_check.json()['result']['points_count']
                print(f"   - Target collection: {current_target_count} memories")
            
            # Update offset for next batch
            offset = next_offset
            
            # Break if no more data
            if not next_offset:
                print("✅ Reached end of source collection")
                break
            
            # Wait between batches
            if total_processed < total_memories:
                print(f"⏳ Waiting {DELAY_BETWEEN_BATCHES} seconds before next batch...")
                await asyncio.sleep(DELAY_BETWEEN_BATCHES)
        
        except Exception as e:
            print(f"❌ Error in batch {batch_number}: {e}")
            print("⏳ Waiting 30 seconds before retry...")
            await asyncio.sleep(30)
            continue
    
    # Final summary
    total_time = time.time() - start_time
    print(f"\n🎉 Re-embedding process completed!")
    print(f"📊 Final statistics:")
    print(f"   - Total processed: {total_processed}/{total_memories}")
    print(f"   - Success rate: {total_success}/{total_processed} ({(total_success/total_processed)*100:.1f}%)")
    print(f"   - Total time: {total_time/60:.1f} minutes")
    print(f"   - Average speed: {total_processed/(total_time/60):.1f} memories/minute")
    
    # Final collection check
    final_response = requests.get(f'http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}')
    if final_response.status_code == 200:
        final_count = final_response.json()['result']['points_count']
        print(f"   - Final target collection: {final_count} memories")
        print(f"   - Collection growth: {final_count - current_count} new memories")
    
    print(f"\n✅ All memories now have real 768D embeddings!")
    print(f"🚀 Pipeline ready with optimized memory collection")

if __name__ == "__main__":
    asyncio.run(continuous_reembed())
