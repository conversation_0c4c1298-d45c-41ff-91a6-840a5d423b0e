#!/bin/bash

# MCPO Project Generator
# Automatically creates a new MCPO project from template

set -e

show_help() {
    echo "🚀 MCPO Project Generator"
    echo "========================"
    echo ""
    echo "Usage: $0 <tool_name> [port]"
    echo ""
    echo "Arguments:"
    echo "  tool_name    Name of your tool (e.g., 'web_scraper', 'data_processor')"
    echo "  port         Port number (default: 8002)"
    echo ""
    echo "Examples:"
    echo "  $0 web_scraper"
    echo "  $0 data_processor 8003"
    echo "  $0 file_manager 8004"
    echo ""
    echo "This will create:"
    echo "  - {tool_name}_mcpo_server.py"
    echo "  - Dockerfile.{tool_name}-mcpo"
    echo "  - docker-compose.{tool_name}-mcpo.yml"
    echo "  - manage_{tool_name}_mcpo.sh"
    echo "  - README_{tool_name}_mcpo.md"
}

if [ $# -lt 1 ]; then
    show_help
    exit 1
fi

TOOL_NAME="$1"
PORT="${2:-8002}"
CONTAINER_NAME="${TOOL_NAME}-mcpo-${PORT}"

# Validate tool name
if [[ ! "$TOOL_NAME" =~ ^[a-z][a-z0-9_]*$ ]]; then
    echo "❌ Error: Tool name must start with lowercase letter and contain only lowercase letters, numbers, and underscores"
    echo "   Examples: web_scraper, data_processor, file_manager"
    exit 1
fi

# Validate port
if [[ ! "$PORT" =~ ^[0-9]+$ ]] || [ "$PORT" -lt 1024 ] || [ "$PORT" -gt 65535 ]; then
    echo "❌ Error: Port must be a number between 1024 and 65535"
    exit 1
fi

echo "🚀 Creating MCPO project for '$TOOL_NAME' on port $PORT"
echo "======================================================="

# Create server file
echo "📝 Creating server file: ${TOOL_NAME}_mcpo_server.py"
sed "s/TOOL_NAME = \"example_tool\"/TOOL_NAME = \"$TOOL_NAME\"/g; s/SERVER_PORT = 8002/SERVER_PORT = $PORT/g" mcpo_template.py > "${TOOL_NAME}_mcpo_server.py"

# Create Dockerfile
echo "📝 Creating Dockerfile: Dockerfile.${TOOL_NAME}-mcpo"
cat > "Dockerfile.${TOOL_NAME}-mcpo" << EOF
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \\
    fastapi==0.104.1 \\
    uvicorn==0.24.0 \\
    pydantic==2.5.0

# Copy the MCPO server
COPY ${TOOL_NAME}_mcpo_server.py .

# Expose port
EXPOSE $PORT

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:$PORT/health || exit 1

# Start command
CMD ["python", "${TOOL_NAME}_mcpo_server.py"]
EOF

# Create docker-compose file
echo "📝 Creating docker-compose: docker-compose.${TOOL_NAME}-mcpo.yml"
cat > "docker-compose.${TOOL_NAME}-mcpo.yml" << EOF
version: '3.8'

services:
  ${TOOL_NAME}-mcpo:
    build:
      context: .
      dockerfile: Dockerfile.${TOOL_NAME}-mcpo
    container_name: $CONTAINER_NAME
    ports:
      - "$PORT:$PORT"
    networks:
      - acca-network
      - unified-mcpo-network
      - gemini-network
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:$PORT/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  acca-network:
    external: true
  unified-mcpo-network:
    external: true
  gemini-network:
    external: true
EOF

# Create management script
echo "📝 Creating management script: manage_${TOOL_NAME}_mcpo.sh"
cat > "manage_${TOOL_NAME}_mcpo.sh" << 'EOF'
#!/bin/bash

# TOOL_NAME MCPO Management Script
# Manages the containerized MCPO server for Open WebUI integration

CONTAINER_NAME="CONTAINER_NAME_PLACEHOLDER"
COMPOSE_FILE="docker-compose.TOOL_NAME_PLACEHOLDER-mcpo.yml"
TOOL_NAME="TOOL_NAME_PLACEHOLDER"
PORT="PORT_PLACEHOLDER"

show_help() {
    echo "🐳 $TOOL_NAME MCPO Management"
    echo "=============================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     - Start the MCPO container"
    echo "  stop      - Stop the MCPO container"
    echo "  restart   - Restart the MCPO container"
    echo "  status    - Show container status"
    echo "  logs      - Show container logs"
    echo "  test      - Test container connectivity"
    echo "  health    - Check health endpoint"
    echo "  build     - Rebuild the container"
    echo "  clean     - Stop and remove container"
    echo "  help      - Show this help message"
}

start_container() {
    echo "🚀 Starting $TOOL_NAME MCPO container..."
    docker compose -f "$COMPOSE_FILE" up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Container started successfully"
        sleep 3
        test_connectivity
    else
        echo "❌ Failed to start container"
        return 1
    fi
}

stop_container() {
    echo "🛑 Stopping $TOOL_NAME MCPO container..."
    docker compose -f "$COMPOSE_FILE" down
    
    if [ $? -eq 0 ]; then
        echo "✅ Container stopped successfully"
    else
        echo "❌ Failed to stop container"
        return 1
    fi
}

restart_container() {
    echo "🔄 Restarting $TOOL_NAME MCPO container..."
    stop_container
    sleep 2
    start_container
}

show_status() {
    echo "📊 Container Status:"
    echo "==================="
    
    if docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        
        if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
            echo "🌐 Endpoints:"
            echo "  - Health: http://localhost:$PORT/health"
            echo "  - API Docs: http://localhost:$PORT/$TOOL_NAME/docs"
            echo "  - OpenAPI: http://localhost:$PORT/$TOOL_NAME/openapi.json"
            echo ""
            echo "🔗 For Open WebUI:"
            echo "  - Container URL: http://$CONTAINER_NAME:$PORT/$TOOL_NAME/openapi.json"
            echo "  - Host URL: http://localhost:$PORT/$TOOL_NAME/openapi.json"
        fi
    else
        echo "❌ Container does not exist"
        echo "Run '$0 start' to create and start the container"
    fi
}

show_logs() {
    echo "📋 Container Logs:"
    echo "=================="
    
    if docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker logs "$CONTAINER_NAME" --tail 50 -f
    else
        echo "❌ Container does not exist"
    fi
}

test_connectivity() {
    echo "🧪 Testing Container Connectivity:"
    echo "=================================="
    
    # Test from host
    echo "1. Testing from host..."
    if curl -s "http://localhost:$PORT/health" > /dev/null; then
        echo "   ✅ Host → Container: OK"
    else
        echo "   ❌ Host → Container: FAILED"
    fi
    
    # Test from Open WebUI container
    echo "2. Testing from Open WebUI container..."
    if docker exec open-webui-mcpo curl -s "http://$CONTAINER_NAME:$PORT/health" > /dev/null 2>&1; then
        echo "   ✅ Open WebUI → $TOOL_NAME MCPO: OK"
    else
        echo "   ❌ Open WebUI → $TOOL_NAME MCPO: FAILED"
    fi
    
    echo ""
    echo "🔗 Network Information:"
    docker inspect "$CONTAINER_NAME" --format='{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}} {{end}}' 2>/dev/null || echo "Container not running"
}

check_health() {
    echo "🏥 Health Check:"
    echo "================"
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo "Container Status: ✅ Running"
        echo ""
        echo "Health Endpoint Response:"
        curl -s "http://localhost:$PORT/health" | python3 -m json.tool 2>/dev/null || echo "Failed to get health status"
    else
        echo "❌ Container is not running"
    fi
}

build_container() {
    echo "🔨 Building $TOOL_NAME MCPO container..."
    docker compose -f "$COMPOSE_FILE" build
    
    if [ $? -eq 0 ]; then
        echo "✅ Container built successfully"
    else
        echo "❌ Failed to build container"
        return 1
    fi
}

clean_container() {
    echo "🧹 Cleaning up $TOOL_NAME MCPO container..."
    docker compose -f "$COMPOSE_FILE" down --rmi all --volumes
    
    if [ $? -eq 0 ]; then
        echo "✅ Container cleaned up successfully"
    else
        echo "❌ Failed to clean up container"
        return 1
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        start_container
        ;;
    stop)
        stop_container
        ;;
    restart)
        restart_container
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    test)
        test_connectivity
        ;;
    health)
        check_health
        ;;
    build)
        build_container
        ;;
    clean)
        clean_container
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
EOF

# Replace placeholders in management script
sed -i "s/TOOL_NAME_PLACEHOLDER/$TOOL_NAME/g; s/CONTAINER_NAME_PLACEHOLDER/$CONTAINER_NAME/g; s/PORT_PLACEHOLDER/$PORT/g" "manage_${TOOL_NAME}_mcpo.sh"
chmod +x "manage_${TOOL_NAME}_mcpo.sh"

# Create README
echo "📝 Creating README: README_${TOOL_NAME}_mcpo.md"
cat > "README_${TOOL_NAME}_mcpo.md" << EOF
# $TOOL_NAME MCPO Server

MCP OpenAPI Proxy server for $TOOL_NAME tool integration with Open WebUI.

## Quick Start

### 1. Customize the tool
Edit \`${TOOL_NAME}_mcpo_server.py\` and implement your tool functions:
- Replace example implementations with your actual logic
- Update OpenAPI specifications
- Add more endpoints as needed

### 2. Build and run
\`\`\`bash
# Build container
./manage_${TOOL_NAME}_mcpo.sh build

# Start container
./manage_${TOOL_NAME}_mcpo.sh start

# Check status
./manage_${TOOL_NAME}_mcpo.sh status
\`\`\`

### 3. Test connectivity
\`\`\`bash
# Test endpoints
./manage_${TOOL_NAME}_mcpo.sh test

# Check health
./manage_${TOOL_NAME}_mcpo.sh health
\`\`\`

### 4. Integrate with Open WebUI
Add this URL to Open WebUI Tools:
- **Container URL**: \`http://$CONTAINER_NAME:$PORT/$TOOL_NAME/openapi.json\`
- **Host URL**: \`http://localhost:$PORT/$TOOL_NAME/openapi.json\`

## Endpoints

- **Health**: http://localhost:$PORT/health
- **API Docs**: http://localhost:$PORT/$TOOL_NAME/docs
- **OpenAPI Spec**: http://localhost:$PORT/$TOOL_NAME/openapi.json

## Management Commands

\`\`\`bash
./manage_${TOOL_NAME}_mcpo.sh start     # Start container
./manage_${TOOL_NAME}_mcpo.sh stop      # Stop container
./manage_${TOOL_NAME}_mcpo.sh restart   # Restart container
./manage_${TOOL_NAME}_mcpo.sh status    # Show status
./manage_${TOOL_NAME}_mcpo.sh logs      # Show logs
./manage_${TOOL_NAME}_mcpo.sh test      # Test connectivity
./manage_${TOOL_NAME}_mcpo.sh health    # Health check
./manage_${TOOL_NAME}_mcpo.sh build     # Build container
./manage_${TOOL_NAME}_mcpo.sh clean     # Clean up
\`\`\`

## Customization

1. **Tool Functions**: Implement your logic in the \`@app.post\` endpoints
2. **Request Models**: Update Pydantic models for your data structures
3. **OpenAPI Spec**: Modify the OpenAPI specification to match your functions
4. **Dependencies**: Add required packages to Dockerfile

## Testing

\`\`\`bash
# Test individual endpoints
curl http://localhost:$PORT/health
curl http://localhost:$PORT/$TOOL_NAME/openapi.json

# Test tool function
curl -X POST "http://localhost:$PORT/$TOOL_NAME/process" \\
  -H "Content-Type: application/json" \\
  -d '{"text": "test input"}'
\`\`\`

## Integration with Open WebUI

1. Ensure container is running and healthy
2. Add tool URL to Open WebUI
3. Test with LLM prompt: "Use $TOOL_NAME to process this data: [your data]"

Generated by MCPO Project Generator
EOF

echo ""
echo "✅ MCPO project created successfully!"
echo ""
echo "📁 Files created:"
echo "  - ${TOOL_NAME}_mcpo_server.py"
echo "  - Dockerfile.${TOOL_NAME}-mcpo"
echo "  - docker-compose.${TOOL_NAME}-mcpo.yml"
echo "  - manage_${TOOL_NAME}_mcpo.sh"
echo "  - README_${TOOL_NAME}_mcpo.md"
echo ""
echo "🚀 Next steps:"
echo "1. Edit ${TOOL_NAME}_mcpo_server.py and implement your tool functions"
echo "2. Build container: ./manage_${TOOL_NAME}_mcpo.sh build"
echo "3. Start container: ./manage_${TOOL_NAME}_mcpo.sh start"
echo "4. Test: ./manage_${TOOL_NAME}_mcpo.sh test"
echo "5. Add to Open WebUI: http://$CONTAINER_NAME:$PORT/$TOOL_NAME/openapi.json"
echo ""
echo "📖 See README_${TOOL_NAME}_mcpo.md for detailed instructions"
