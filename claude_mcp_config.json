{"mcpServers": {"mem0_system": {"command": "curl", "args": ["-X", "POST", "http://localhost:8000/mem0_system/mem0_get_recent_memories", "-H", "Content-Type: application/json", "-d", "{\"user_id\": \"1281789c-d7b2-4661-8d0f-d88f06864b4d\", \"limit\": 10}"]}, "oracle_db": {"command": "curl", "args": ["-X", "POST", "http://localhost:8000/oracle_db/query_oracle_memories", "-H", "Content-Type: application/json", "-d", "{\"query\": \"AI\", \"limit\": 10, \"table_name\": \"AI_DOCUMENTS\"}"]}, "gemini_search": {"command": "curl", "args": ["-X", "POST", "http://localhost:8000/gemini_search_engine/search_with_gemini", "-H", "Content-Type: application/json", "-d", "{\"query\": \"Oracle Advanced Memory Pipeline\", \"max_results\": 5}"]}}}